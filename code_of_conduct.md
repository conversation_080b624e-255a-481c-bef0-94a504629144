
# Contributor Covenant Code of Conduct

## Our values

We as members, contributors, and leaders pledge to make participation in our
environment a harassment-free experience for everyone, regardless of age, body
size, visible or invisible disability, ethnicity, sex characteristics, gender
identity and expression, level of experience, education, socio-economic status,
nationality, personal appearance, race, caste, color, religion, or sexual
identity and orientation.

We pledge to act and interact in ways that contribute to an open, welcoming,
diverse, inclusive, and healthy work environment.

## Our standards

Examples of behavior that contributes to a positive environment include:

* Demonstrating empathy and kindness toward other people
* Being respectful of differing opinions, viewpoints, and experiences
* Giving and gracefully accepting constructive feedback
* Accepting responsibility and apologizing to those affected by our mistakes,
  and learning from the experience
* Focusing on what is best not just for us as individuals, but for the overall
  environment

Examples of unacceptable behavior include:

* The use of sexualized language or imagery, and sexual attention or advances of
  any kind
* Trolling, insulting or derogatory comments, and personal or political attacks
* Public or private harassment
* Publishing others' private information, such as a physical or email address,
  without their explicit permission
* Other conduct which could reasonably be considered inappropriate in a
  professional setting

## Project structure

### Development cycles
We have 3 environments set up `production` , `staging`, `development`. Each of the environments signify branches in the GitHub repo.  
>  `production` => `main`  
>  `staging` => `staging`  
>  `development` => `dev`  

<img src="./.github/assets/DevelopmentCycles.png" width="auto" />

To learn more about how our environments work and when to use which environment please read [this detailed document](https://www.notion.so/Development-Environments-926177fc3b96402c9783c8c74a704dd7?pvs=4).

**Environment files**  
All 3 environment files can be gathered from teammates: `.env`, `.env.dev`, and `.env.staging`. Please make sure the all the env files received have the following variables.
```
ENV=

API_URL=
HOST=

CLIENT_ID=

reCAPTCHA_SITE_KEY=
reCAPTCHA_SECRET_KEY=

SENTRY_DSN=
SENTRY_AUTH_TOKEN=

LOCATION_SERVICE_KEY=

ONE_SIGNAL_APP_ID=

NETWORK_ID=
```


### Workflows
A normal development day to day workflow should look something like this. Another thing not mentioned in the workflow is daily standups.
<img src="./.github/assets/Workflow.png" width="auto" />


### Folder structure
The project folder structure is as following:
- All components live in one place
- All screens live in one place
- All style files, hooks, services and so on live in their own individual place.

What this means is, if there are any components related to a screen, they will always live and be added under the components folder. 
Same thing goes with screens, hooks, etc. 
> For example: Under the screens folder there should never exist a singular component, it should only exist in the components folder.

To learn more about our project details be sure to look at the [project documentation in Notion](https://www.notion.so/Documentation-487a9f10e27e41fc8f20948785dd4fc1?pvs=4).



### Code style
All code should be developed as per our [Code Style Guide](https://www.notion.so/Code-style-guide-b877bc132db84c5184dbd46afd829091?pvs=4).


## Linear history + Rebase on pull
When committing to a branch, please make sure to always use `git pull origin --rebase` to maintain a clear linear history. 


### Why rebase?
This approach ensures that:
- There are no unnecessary merge commits
- Your new commits are always on top of the latest dev changes, which have already been reviewed and merged.
- Your pull requests contain **only** your changes, making them easier to review.
- Keeps the git history in a **straight line** which is **cleaner**, and makes it **easier to track changes, revert commits** if needed, and understand the project's evolution over time.


### Best practices for rebasing with `dev`
1. **Before creating a feature branch from `dev`**, ensure your local `dev` branch is up to date:  
   ```sh
   git checkout dev
   git pull origin --rebase
   ```
2. Always rebase your feature branch with dev whenever new changes are pushed to dev to keep your branch updated and avoid merge conflicts later:
    ```sh
    git checkout <feature-branch>
    git rebase dev
    ```
3. If conflicts arise during rebase, you would need to follow the steps below repeatedly until all your commits are added on top of dev successfully:
- Manually fix conflicts in affected files
- Staging resolved files:
    ```sh
    git add <file name>
    ```
- Continuing the rebase:
    ```sh
    git rebase --continue
    ```
4. After a successful rebase, your branch’s history will differ from the remote branch, so you must force push with lease to update the remote safely:
    ```sh
    git push --force-with-lease
    ```


## Enforcement Responsibilities

Leaders are responsible for clarifying and enforcing our standards of
acceptable behavior and will take appropriate and fair corrective action in
response to any behavior that they deem inappropriate, threatening, offensive,
or harmful.

Leaders have the right and responsibility to remove, edit, or reject
comments, commits, code, wiki edits, issues, and other contributions that are
not aligned to this Code of Conduct, and will communicate reasons for moderation
decisions when appropriate.

## Scope

This Code of Conduct applies within all team work spaces, and also applies when
an individual is officially representing the Rapid Medical team in public spaces.
Examples of representing our team include using an official email address,
posting via an official social media account, or acting as an appointed
representative at an online or offline event.

## Enforcement

Instances of abusive, harassing, or otherwise unacceptable behavior may be
reported to the team leaders. All complaints will be reviewed and investigated promptly and fairly.

All team leaders are obligated to respect the privacy and security of the
reporter of any incident.

## Enforcement Guidelines

Repository owners, admins and maintainers will follow these guidelines in determining
the consequences for any action they deem in violation of this Code of Conduct:


### Branches
All branches should follow the same standards and be created from the default `dev` branch unless its to hotfix another branch or targeting a different target other than `dev`. Hotfixes and different targets than `dev` should be edge cases and not the default. 
* **Category**: feat, fix, refactor, etc - See descriptions below  
* **Ticket number**: TSU-<number>, copy the reference number from the ticket  
* **Description**: A short description of what is being implemented


      Template
      --------
      category/ticket-number/description-in-kebab-case



      Example
      -------
      feat/TSU-123/feature-description

      fix/TSU-234/bug-description


   
Add the following prefix to your branches and commits  

| **Category** | **Description**                                                                                                            |
| ------------ | -------------------------------------------------------------------------------------------------------------------------- |
| **feat**     | Additions of new, backward-compatible features or enhancements.                                                            |
| **breaking** | Modifications resulting in backward incompatibility, including API changes or feature removals.                            |
| **upgrade**  | Upgrades or changes that significantly alter functionalities, requiring migration or major adjustments by users.           |
| **chore**    | Routine tasks, maintenance, or build process updates not changing the app's functionality.                                 |
| **config**   | Changes in configuration affecting the build system or external dependencies without direct functional impact.             |
| **docs**     | Updates or additions to documentation only.                                                                                |
| **fix**      | Corrections for backward-compatible bugs or issues.                                                                        |
| **perf**     | Performance improvements enhancing the app's efficiency without altering functionality.                                    |
| **refactor** | Code restructuring or cleaning that does not add features or fix bugs but improves readability or structure.               |
| **style**    | Formatting changes, whitespace adjustments, or code style compliance fixes that don't affect logic.                        |
| **test**     | Adding, removing, or correcting tests without affecting production code.                                                   |
| **security** | Patches specifically aimed at removing security vulnerabilities.                                                           |
| **ci-cd**    | Modifications to CI/CD pipelines that do not alter the app's code directly but might affect build or deployment processes. |

<br>

### Commits
All commits should be concise and a clear indication on what you're committing. Any commits that need further explanation should include a header and a body so they are easier to read. 

Link all commits with [gitmoji](https://gitmoji.dev/) prefix to signify the change being made. Review the gitmoji documentation to know which gitemoji to use as a prefix.

```
:gitmoji: Commit header - A short description

Commit body - A short explanation of changes
```

**Examples of bad commit messages ([Hall of Shame](https://www.codelord.net/2015/03/16/bad-commit-messages-hall-of-shame/)):**
-  **“bug fix”, “more work”, “minor changes”, “oopsie”, “wtf”** – These mean nothing. You may as well supply no commit message. Why did you even bother to write it?
- **“Work on feature blah blah” x 20 commits in a row** – This is unfortunately aided by different IDEs and tools that suggest your previous commit message automatically. It creates a bunch of commits that are not discernible from one another. Yeah, I get a general topic of what they’re about, but we lost the ability to talk about them without using specific hashes and if you see that these changes for feature “blah blah” introduced a bug you have no easy way to guess which commit did it. You have to start going through the diffs.
- **“Fix BUG-9284”** – Bug trackers can be awesome, and if your workplace uses them then it makes a lot of sense to write the ticket number in the commit message. The problem is that writing just that now means that if I’m looking at a commit and trying to understand why a change was done I now have to go to a different system and search that number in it. This is a hassle. I usually copy and paste some part of the description of the bug to the commit message to make it easier for anyone in the future to understand what this change does.
- **“Change X constant to be 10”** – These commit messages are a lot like useless comments in code. When the description is just which changes were made it’s just duplicating the code – we can see the diff and see that X was changed from 5 to 10. What we can’t see is **why** this change was needed.
- **“super long commit message goes here, something like 100 words and lots of characters woohoo!”** – Tools care about your commit messages. There’s a convention used by most git tools that prefers the length of the first line in your commit message to be 50 characters or less. Stick to it. It makes it easier to look at the history and means you intentionally put the really important bits in the top line.


**Examples of good commit messages**
- Explain **why** something was changed
- Communicates the **purpose** and the **context** of the changes made
- Aren't vague, but rather **straight to the point** and clear
- Are **short** and **concise**
- Identifies what was worked on and why the change was made

<br>

**Examples of commit messages**
| **Category** | **Example**                                                                                                                |
| ------------ | -------------------------------------------------------------------------------------------------------------------------- |
| **Simple update/change**     | **✏️ Fix typo in README** <br><br> Corrected the spelling of 'application' in the introduction section. This change improves document clarity.|
| **Feature addition** | **✨ Add user authentication module** <br><br>Introduced a new user authentication module supporting OAuth 2.0. This module includes initial setup for token generation and verification, enhancing security for user logins. |
| **Bug fix** | **🐛 Resolve memory leak in data processing script** <br><br>Identified and fixed a memory leak in processData.js caused by unclosed file streams. This fix should improve the performance and stability of data processing operations.  |
| **Refactoring code** | **♻️ Refactor database connection logic** <br><br>Refactored the existing database connection logic for improved error handling and connection pooling. This update aims to enhance database performance and reliability.  |
| **Update build numbers** | **🚀 Create 2.0.2 builds - iOS(18) & Android(24)**  |
| **Updating documentation** | **📝 Update API documentation for new endpoints** <br><br>Expanded the API documentation to include newly added endpoints related to user management. The update covers request/response formats, error codes, and sample requests.  |
| **description** | **`:gitmoji:` Commit title** <br><br>Commit body  |

<!-- | **description** | **title** <br><br>Com  | -->

### Error messages
All error messages that are logged in the code base, weather they're being sent to Sentry or being logged locally should follow the following pattern:  
- Logging locally:   
   - `console.error('<file name>: <function name>(): <more information if needed>', error)`

### Pull requests
All pull requests should follow the correct naming conventions. If you are actively working on a PR it should have the prefix `WIP:` signifying that its a work in progress or it should be marked as draft. Any PRs that are still in progress or being worked on should be marked as draft.

**Merging**: The assignee of the PR should merge the PR in once its been reviewed and approved by the reviewers.

**Images:** When adding images or screenshots to PRs you should always use `<img src="" width=""></img>` to make sure that the image isn't huge. If its a screenshot from a simulator/emulator or a physical device a good standard is to use 300 px as the width `<img src="" width="300"></img>`

**Examples of PR titles**

| **Category** | **Example**                                                                                                                |
| ------------ | -------------------------------------------------------------------------------------------------------------------------- |
| **feat**     | Feat: ✨ Feature implemented [TSU-0000]                               |
| **upgrade**  | Upgrade: ⬆️ Upgrade react native to v00.0 [TSU-0000]                  |
| **chore**    | Chore: ⬆️ Update dependency [TSU-0000] <br>Chore: 🔨 Scripts added, changed or removed [TSU-0000]                               |
| **config**   | Config: 🔧 Configure or update project configurations [TSU-0000]             |
| **docs**     | Docs: 📝 Create or update documentation [TSU-0000]                   |
| **fix**      | Fix: 🐛 Bug fixed [TSU-0000]  <br>Fix: 🚑 Critical hotfix fixed [TSU-0000] <br>Fix: 🐛 Multiple bugs fixed [TSU-0000, TSU-0001, TSU-0002]             |
| **perf**     | Perf: ⚡️ Enhance performance for x, y, z [TSU-0000]                                                                     |
| **refactor** | Refactor: ♻️ Refactor x, y, z [TSU-0000]                                                                            |
| **style**    | Style: 💄 Style added or updated for x, y, z [TSU-0000]  <br>Style: 🚸 User experience improved [TSU-0000]  <br>Style: 📱 Responsive design work [TSU-0000]           |
| **test**     | Test: ✅ Tests added or updated for x, y, z [TSU-0000]                                                                |
| **security** | Security: 🔒 Fix or enhance anything security related [TSU-0000]   |
| **ci-cd**    | CI/CD: 💚 Fix CI/CD build issue [TSU-0000] <br> CI/CD: 👷‍♂️ Add or update CI/CD action pipelines [TSU-0000] |


### Code reviews
All code reviews should include a review of the code it self as well as a review of the behavior of the feature/fix that was worked on. In some cases when working on a bug or fixing any small items a complete run up should still be completed to verify that the issue has been fixed. Any other code review requirements will be mentioned in the PR template.



### Changelogs
When a new build is created a new version should be added in the [change log file](./.github/CHANGELOG.md).


## Attribution

This Code of Conduct is inspired by the [Contributor Covenant](https://www.contributor-covenant.org/version/2/1/code_of_conduct.html),
version 2.1.
