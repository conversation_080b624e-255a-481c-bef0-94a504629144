# RapidMedical React Native App

This is a React Native project for RapidMedical, bootstrapped using [`@react-native-community/cli`](https://github.com/react-native-community/cli).

# Getting Started

## Step 0: Before you start - Required readings
- [ ] Read the [Code of Conduct](./code_of_conduct.md) file to better understand how we work
  - :bulb: Its very important to read the code of conduct well to make sure all processes and procedures are being properly followed
- [ ] Read the [Code Style Guide](https://www.notion.so/Code-style-guide-b877bc132db84c5184dbd46afd829091?pvs=4) located in Notion
- [ ] Familiarize yourself with the [Mobile Teamspace](https://www.notion.so/Documentation-487a9f10e27e41fc8f20948785dd4fc1?pvs=4) on Notion

## Step 1: Environment Setup

### Prerequisites
1. **Environment Variables**: Contact team members to obtain the necessary `.env` files:
   - `.env` (Production)
   - `.env.dev` (Development)
   - `.env.staging` (Staging)

2. Add the enviroment files in the project root (`rapid-medical-app/`)

3. Complete the [React Native - Environment Setup](https://reactnative.dev/docs/environment-setup) instructions.

### Installation

You can use our quick setup script:

```bash
./scripts/build_run.sh
```

Or follow these steps manually:

1. Install dependencies:
```bash
yarn install
```

2. For iOS, install pods:
```bash
cd ios/
pod install
cd ..
```

## Step 2: Start Metro Server
```bash
# Start Metro
yarn start

# Start Metro with clean cache (if you're having bundling issues)
yarn start --reset-cache
```

## Step 3: Running the Application

### For Android
```bash
# Development
yarn android:dev

# Staging
yarn android:staging

# Production
yarn android:prod

# Release variants
yarn android:dev-release
yarn android:staging-release
yarn android:prod-release
```

### For iOS
```bash
# Development
yarn ios:dev

# Staging
yarn ios:staging

# Production
yarn ios:prod
```

## Development Scripts

### Cleaning the Project
```bash
yarn clean
```

This script will:
- Clear React Native and Metro cache
- Remove node_modules
- Clean Android build files
- Clean iOS pods and build files

### Code Quality
```bash
# Lint check
yarn lint

# Lint fix
yarn lint:fix

# Format code
yarn format

# Format check
yarn format:check
```

# Debugging

## Reactotron Setup
1. Download [Reactotron](https://github.com/infinitered/reactotron/releases) for your platform
2. Launch Reactotron before starting your app
3. Start your app in development mode

### Android Troubleshooting
If having connection issues on Android, run:
```bash
adb reverse tcp:9090 tcp:9090
```

# Development Guidelines

## Branch Naming
Follow the pattern: `category/ticket-number/description-in-kebab-case`

Example:
```bash
feat/TSU-123/feature-description
fix/TSU-234/bug-description
```

For more details about our development process, branching strategy, and code style guidelines, please refer to our [Code of Conduct](./code_of_conduct.md).

# Troubleshooting

If you encounter any issues:
1. Try cleaning the project using `yarn clean`
2. Ensure all environment variables are properly set
3. Contact the team leads mentioned in [CODEOWNERS](./.github/CODEOWNERS.md)