## Summary and Changes Made
<!-- A high-level overview of the changes made in this pull request. Include a list of new features, bug fixes, enhancements, and any other relevant modifications.-->
- 

### Screenshots
<!-- Any screenshots and or videos to show the changes made -->
<div class="image-gallery">
  <!-- Upload the attachment and then copy paste the src url into the src="<url>" below -->
  <img src="" width="300"></img>
  <img src="" width="300"></img>
  <img src="" width="300"></img>
</div>


## Notes to the Code Reviewers
<!-- If there are specific areas you’d like the reviewers to focus on during the code review, mention them here. Highlight any design decisions, implementation details, or potential edge cases that require attention. -->
- 


## Assignee Checklist
### General
- [ ] PR title follows `<change type>: <change made> [Notion Ticket ID]`. See [documentation here](https://www.notion.so/PR-Title-Conventions-a43dda35b88a41e29abe2b0c61efa193?pvs=4).
- [ ] Pipeline passes all checks for PR
- [ ] I code reviewed my own code
- [ ] PR title contains an appropriate emoji in the beginning. See the list of approved GitMoji mappings [here](https://www.notion.so/GitMoji-Mappings-7ceeed25f53a48ca86692232bf4f3358)
- [ ] PR agent comments have been reviewed.
- [ ] Any ignored PR agent comments have been justified in the comments.
- [ ] If changes were made based upon suggestions made by the PR agent, the PR agent was run again using `/improve`.

### Code Quality
- [ ] Code is self-documenting (reads like a story) and does not need additional comments
- [ ] Code is modular and exhibits a separation of concerns
- [ ] The command `yarn lint --fix` was run and did not result in any linting errors or warnings
- [ ] Variables, methods, and components are meaningfully named, clear and concise
- [ ] Commit messages are meaningful, clear and concise
- [ ] All issues raised by Sonar Cloud have been appropriately addressed
- [ ] No import statements start with `../../`

### Security
- [ ] No new security vulnerabilities are introduced
- [ ] All data transactions are securely handled
- [ ] Passwords or sensitive data are not hard-coded, logged or stored inappropriately

### UI/UX
- [ ] UI consistency is maintained with existing styles and components
- [ ] Responsiveness and layout for various device sizes has been verified
- [ ] Accessibility standards are followed

### Testing
**General**
- [ ] [Unit tests](https://www.notion.so/Unit-Tests-using-Jest-100aa85b950f801d8f55e034cf0f5d5f?pvs=4) are created for all the changeable items such as text fields, buttons or other action items
- [ ] Test ids are added to all the relevant elements that follows the [correct naming conventions](https://www.notion.so/Testing-ids-23007a2c50d5439d9db1eeef26624290?pvs=4)

**Simulator / Emulator**
- [ ] Tested in the Xcode iOS simulator.
- [ ] Tested in the Android Studio in the Android Emulator.

**Physical device**
- [ ] Tested on a physical iOS device.
- [ ] Tested on a physical Android device.
