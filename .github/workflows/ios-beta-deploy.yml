name: Deploy to iOS Beta

on:
  workflow_dispatch:
  # schedule:
  #   - cron: "0 16 * * 1-5" # Runs at 4 PM UTC, which corresponds to 11 AM EST

jobs:
  check-commits:
    runs-on: ubuntu-latest
    outputs:
      has_commits: ${{ steps.check.outputs.has_commits }}
    steps:
      - name: Check out Repository
        uses: actions/checkout@v2
        with:
          fetch-depth: 0

      - name: Check for commits in the last 24 hours excluding 'version bump'
        id: check
        run: |
          if [ $(git log main --since='1 day ago' --oneline | grep -v 'version bump' | wc -l) -gt 0 ]; then
            echo "::set-output name=has_commits::true"
          else
            echo "::set-output name=has_commits::false"
          fi

  build-and-deploy-to-beta:
    needs: check-commits
    if: ${{ needs.check-commits.outputs.has_commits == 'true' }}
    name: iOS Build & Deploy
    runs-on: macos-latest
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v2

      - name: Install Yarn Dependencies
        run: yarn install

      - name: Set up Ruby
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: '3.0.2'
          bundler-cache: true

      - name: Install Bundler
        run: gem install bundler

      - name: Bundle Install
        run: bundle install
        working-directory: ./ios

      - name: Install CocoaPods
        run: sudo gem install cocoapods
        working-directory: ./ios

      - name: Install Fastlane
        run: sudo gem install fastlane
        working-directory: ./ios

      - name: Create .netrc for Mapbox SDK authorization
        run: |
          echo "machine api.mapbox.com" >> ~/.netrc
          echo "   login mapbox" >> ~/.netrc
          echo "   password ${{ secrets.MAPBOX_PASSWORD }}" >> ~/.netrc
          chmod 600 ~/.netrc

      - name: Setup SalesforceMobileSDK iOS
        run: node ./installios.js

      - name: Deploy to TestFlight
        run: bundle exec fastlane ios beta
        working-directory: ./ios

      - name: Remove .netrc
        if: always()
        run: rm ~/.netrc

env:
  APPLE_TEAM_ID: ${{ secrets.APPLE_TEAM_ID }}
  APP_STORE_CONNECT_API_KEY_ID: ${{ secrets.APP_STORE_CONNECT_API_KEY_ID }}
  APP_STORE_CONNECT_API_ISSUER_ID: ${{ secrets.APP_STORE_CONNECT_API_ISSUER_ID }}
  APP_STORE_CONNECT_API_KEY_CONTENT: ${{ secrets.APP_STORE_CONNECT_API_KEY_CONTENT }}
  APPLE_APP_ID: ${{ secrets.APPLE_APP_ID }}
  GIT_PERSONAL_ACCESS_TOKEN: ${{ secrets.GIT_PERSONAL_ACCESS_TOKEN }}
  MATCH_PASSWORD: ${{ secrets.MATCH_PASSWORD }}
  SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
  SENTRY_ORG: ${{ secrets.SENTRY_ORG }}
  SENTRY_PROJECT: ${{ secrets.SENTRY_PROJECT }}
  BETA_APP_FEEDBACK_EMAIL: ${{ secrets.BETA_APP_FEEDBACK_EMAIL }}
  BETA_APP_REVIEW_INFO_CONTACT_EMAIL: ${{ secrets.BETA_APP_REVIEW_INFO_CONTACT_EMAIL }}
  BETA_APP_REVIEW_INFO_CONTACT_FIRST_NAME: ${{ secrets.BETA_APP_REVIEW_INFO_CONTACT_FIRST_NAME }}
  BETA_APP_REVIEW_INFO_CONTACT_LAST_NAME: ${{ secrets.BETA_APP_REVIEW_INFO_CONTACT_LAST_NAME }}
  BETA_APP_REVIEW_INFO_CONTACT_PHONE: ${{ secrets.BETA_APP_REVIEW_INFO_CONTACT_PHONE }}
  BETA_APP_REVIEW_INFO_DEMO_ACCOUNT_NAME: ${{ secrets.BETA_APP_REVIEW_INFO_DEMO_ACCOUNT_NAME }}
  BETA_APP_REVIEW_INFO_DEMO_ACCOUNT_PASSWORD: ${{ secrets.BETA_APP_REVIEW_INFO_DEMO_ACCOUNT_PASSWORD }}
  LOCALIZED_APP_INFO_FEEDBACK_EMAIL: ${{ secrets.LOCALIZED_APP_INFO_FEEDBACK_EMAIL }}
  SLACK_MOBILE_RELEASES_WEBHOOK_URL: ${{ secrets.SLACK_MOBILE_RELEASES_WEBHOOK_URL }}
