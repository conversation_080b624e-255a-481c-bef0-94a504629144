name: Deploy to iOS Production

on: workflow_dispatch

jobs:
  deploy-to-production:
    runs-on: macos-latest
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v2

      - name: Set up Ruby
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: '3.0.2'
          bundler-cache: true

      - name: Install Bundler
        run: gem install bundler

      - name: Bundle Install
        run: bundle install
        working-directory: ./ios

      - name: Install CocoaPods
        run: sudo gem install cocoapods
        working-directory: ./ios

      - name: Install Fastlane
        run: sudo gem install fastlane
        working-directory: ./ios

      - name: Deploy to Production
        run: bundle exec fastlane ios releaseproduction
        working-directory: ./ios


env:
  APPLE_TEAM_ID: ${{ secrets.APPLE_TEAM_ID }}
  APP_STORE_CONNECT_API_KEY_ID: ${{ secrets.APP_STORE_CONNECT_API_KEY_ID }}
  APP_STORE_CONNECT_API_ISSUER_ID: ${{ secrets.APP_STORE_CONNECT_API_ISSUER_ID }}
  APP_STORE_CONNECT_API_KEY_CONTENT: ${{ secrets.APP_STORE_CONNECT_API_KEY_CONTENT }}
  APPLE_APP_ID: ${{ secrets.APPLE_APP_ID }}
  GIT_PERSONAL_ACCESS_TOKEN: ${{ secrets.GIT_PERSONAL_ACCESS_TOKEN }}
  MATCH_PASSWORD: ${{ secrets.MATCH_PASSWORD }}
  SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
  SENTRY_ORG: ${{ secrets.SENTRY_ORG }}
  SENTRY_PROJECT: ${{ secrets.SENTRY_PROJECT }}
  BETA_APP_FEEDBACK_EMAIL: ${{ secrets.BETA_APP_FEEDBACK_EMAIL }}
  BETA_APP_REVIEW_INFO_CONTACT_EMAIL: ${{ secrets.BETA_APP_REVIEW_INFO_CONTACT_EMAIL }}
  BETA_APP_REVIEW_INFO_CONTACT_FIRST_NAME: ${{ secrets.BETA_APP_REVIEW_INFO_CONTACT_FIRST_NAME }}
  BETA_APP_REVIEW_INFO_CONTACT_LAST_NAME: ${{ secrets.BETA_APP_REVIEW_INFO_CONTACT_LAST_NAME }}
  BETA_APP_REVIEW_INFO_CONTACT_PHONE: ${{ secrets.BETA_APP_REVIEW_INFO_CONTACT_PHONE }}
  BETA_APP_REVIEW_INFO_DEMO_ACCOUNT_NAME: ${{ secrets.BETA_APP_REVIEW_INFO_DEMO_ACCOUNT_NAME }}
  BETA_APP_REVIEW_INFO_DEMO_ACCOUNT_PASSWORD: ${{ secrets.BETA_APP_REVIEW_INFO_DEMO_ACCOUNT_PASSWORD }}
  LOCALIZED_APP_INFO_FEEDBACK_EMAIL: ${{ secrets.LOCALIZED_APP_INFO_FEEDBACK_EMAIL }}
  SLACK_MOBILE_RELEASES_WEBHOOK_URL: ${{ secrets.SLACK_MOBILE_RELEASES_WEBHOOK_URL }}
