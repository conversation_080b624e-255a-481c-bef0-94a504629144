name: Build, test and scan
on:
  pull_request:
    types: [opened, synchronize, reopened]

permissions:
    id-token: write
    contents: read
    pull-requests: read

jobs:
  lint:
    # For now, we can do these as serial—it doesn't increase the
    # overall build time by that much. At some point, if the tests
    # start taking too long, we can parallelize.
    name: Run Tests and Sonar
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 20.x

      # Get yarn cache directory path
      - name: Get yarn cache directory path
        id: yarn-cache-dir-path
        run: echo "dir=$(yarn cache dir)" >> $GITHUB_OUTPUT

      # Cache yarn dependencies
      - name: Cache yarn dependencies
        uses: actions/cache@v3
        id: yarn-cache
        with:
          path: |
            ${{ steps.yarn-cache-dir-path.outputs.dir }}
            node_modules
            */*/node_modules
          key: ${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-yarn-

      # Install dependencies only if cache miss
      - name: Install dependencies
        if: steps.yarn-cache.outputs.cache-hit != 'true'
        run: yarn install --frozen-lockfile --prefer-offline

      # tj-actions has a major security flaw. Excluding the linting for now
      # https://semgrep.dev/blog/2025/popular-github-action-tj-actionschanged-files-is-compromised/
      #- name: Run eslint on changed files
      #  uses: tj-actions/eslint-changed-files@v25
      #  with:
      #    config_path: ".eslintrc.js"
      #    extra_args: "--max-warnings=0"

      - name: Run Jest tests with coverage
        run: yarn jest --coverage --runInBand --force-exit

      - name: SonarQube Scan
        uses: SonarSource/sonarqube-scan-action@v4.2.1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}