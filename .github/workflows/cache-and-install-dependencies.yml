name: Cache and install dependencies
on:
    workflow_dispatch:

permissions:
    id-token: write # This is required for requesting the JWT Token to assume roles from AWS
    contents: read # This is required for actions/checkout to read the id-token contents

jobs:
    build:
        name: Build and install
        runs-on: ubuntu-latest
        steps:
            - uses: actions/checkout@v4
            - name: Set up Node.js
              uses: actions/setup-node@v4
              with:
                node-version: 20.x

            - name: Cache node modules
              uses: actions/cache@v3
              env:
                cache-name: cache-node-modules
              with:
                path: ~/.yarn-cache
                key: ${{ runner.os }}-build-${{ env.cache-name }}-${{ hashFiles('**/yarn.lock') }}
                restore-keys: |
                  ${{ runner.os }}-build-${{ env.cache-name }}-
                  ${{ runner.os }}-build-
                  ${{ runner.os }}-
                  
            - name: Install dependencies
              run: yarn install --frozen-lockfile