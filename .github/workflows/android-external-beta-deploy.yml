name: Deploy to Android External Beta

on:
  workflow_dispatch:

jobs:
  check-commits:
    runs-on: ubuntu-latest
    outputs:
      has_commits: ${{ steps.check.outputs.has_commits }}
    steps:
      - name: Check out Repository
        uses: actions/checkout@v2
        with:
          fetch-depth: 0 
      
      - name: Check for commits in the last 24 hours excluding 'version bump'
        id: check
        run: |
          if [ $(git log main --since='1 day ago' --oneline | grep -v 'version bump' | wc -l) -gt 0 ]; then
            echo "::set-output name=has_commits::true"
          else
            echo "::set-output name=has_commits::false"
          fi

  android-build-deploy:
    needs: check-commits
    if: ${{ needs.check-commits.outputs.has_commits == 'true' }}
    name: Android Build & Deploy
    runs-on: ubuntu-latest
    steps:
      - name: Check out Repository
        uses: actions/checkout@v2

      - name: Install Yarn Dependencies
        run: yarn install

      - name: Set up Ruby
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: '3.0.2'
          bundler-cache: true

      - name: Install Bundler
        run: gem install bundler

      - name: Bundle Install
        run: bundle install
        working-directory: ./android

      - name: Install CocoaPods
        run: sudo gem install cocoapods
        working-directory: ./android

      - name: Install Fastlane
        run: sudo gem install fastlane
        working-directory: ./android

      - name: Set up our JDK environment
        uses: actions/setup-java@v1.4.3
        with:
          java-version: 17

      - name: Create Assets Folder
        run: mkdir -p android/app/src/main/assets

      - name: Create libs Folder
        run: mkdir -p android/libs

      - name: Setup SalesforceMobileSDK Android
        run: node ./installandroid.js

      - name: Decode Service Account Key JSON File
        uses: timheuer/base64-to-file@v1
        id: service_account_json_file
        with:
          fileName: 'serviceAccount.json'
          encodedString: ${{ secrets.ANDROID_SERVICE_ACCOUNT_BASE64 }}

      - name: Decode Keystore File
        uses: timheuer/base64-to-file@v1
        id: android_keystore
        with:
          fileName: 'android_keystore.keystore'
          encodedString: ${{ secrets.ANDROID_KEYSTORE_BASE64 }}

      - name: Setup bundle
        run: |
          npx react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle
        env:
          SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
          SENTRY_ORG: ${{ secrets.SENTRY_ORG }}
          SENTRY_PROJECT: ${{ secrets.SENTRY_PROJECT }}

      - name: Build & deploy Android release
        run: bundle exec fastlane android externalbeta
        working-directory: ./android
        env:
          KEYSTORE_FILE: ${{ steps.android_keystore.outputs.filePath }}
          ANDROID_KEY_STORE_PASSWORD: ${{ secrets.ANDROID_KEY_STORE_PASSWORD }}
          ANDROID_ALIAS: ${{ secrets.ANDROID_ALIAS }}
          KEY_PASSWORD: ${{ secrets.ANDROID_KEY_PASSWORD }}
          ANDROID_JSON_KEY_FILE: ${{ steps.service_account_json_file.outputs.filePath }}
          SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
          SENTRY_ORG: ${{ secrets.SENTRY_ORG }}
          SENTRY_PROJECT: ${{ secrets.SENTRY_PROJECT }}
          ANDROID_SERVICE_ACCOUNT_JSON_PLAIN_TEXT: ${{ secrets.ANDROID_SERVICE_ACCOUNT_JSON_PLAIN_TEXT }}
          SLACK_MOBILE_RELEASES_WEBHOOK_URL: ${{ secrets.SLACK_MOBILE_RELEASES_WEBHOOK_URL }}
          GIT_PERSONAL_ACCESS_TOKEN: ${{ secrets.GIT_PERSONAL_ACCESS_TOKEN }}

env:
  SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
  ANDROID_ALIAS: ${{ secrets.ANDROID_ALIAS }}
  ANDROID_KEY_STORE_PASSWORD: ${{ secrets.ANDROID_KEY_STORE_PASSWORD }}
  ANDROID_KEY_PASSWORD: ${{ secrets.ANDROID_KEY_PASSWORD }}
  ANDROID_SERVICE_ACCOUNT_JSON_PLAIN_TEXT: ${{ secrets.ANDROID_SERVICE_ACCOUNT_JSON_PLAIN_TEXT }}
  ANDROID_KEYSTORE_BASE64: ${{ secrets.ANDROID_KEYSTORE_BASE64 }}
  ANDROID_SERVICE_ACCOUNT_BASE64: ${{ secrets.ANDROID_SERVICE_ACCOUNT_BASE64 }}
  SENTRY_ORG: ${{ secrets.SENTRY_ORG }}
  SENTRY_PROJECT: ${{ secrets.SENTRY_PROJECT }}
  SLACK_MOBILE_RELEASES_WEBHOOK_URL: ${{ secrets.SLACK_MOBILE_RELEASES_WEBHOOK_URL }}
  GIT_PERSONAL_ACCESS_TOKEN: ${{ secrets.GIT_PERSONAL_ACCESS_TOKEN }}
