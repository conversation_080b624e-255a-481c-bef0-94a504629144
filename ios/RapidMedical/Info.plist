<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>BGTaskSchedulerPermittedIdentifiers</key>
	<array>
		<string>com.transistorsoft.fetch</string>
	</array>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>$(APP_NAME)</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleGetInfoString</key>
	<string></string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleURLName</key>
			<string>com.rapidmedical.app</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.rapidmedical.app</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>oauth</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSApplicationCategoryType</key>
	<string></string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>MBXAccessToken</key>
	<string>**********************************************************************************************</string>
	<key>MGLMapboxAccessToken</key>
	<string>${MAPBOX_ACCESS_TOKEN}</string>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<false/>
		<key>NSAllowsLocalNetworking</key>
		<true/>
	</dict>
	<key>NSCameraUsageDescription</key>
	<string>We require camera access to capture photo-based proof of delivery—such as images of parcels, services, or equipment—directly within the app.</string>
	<key>NSHumanReadableCopyright</key>
	<string></string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>Your location is needed both during app use and in the background to support continuous route monitoring and time-sensitive logistics.</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>We use your location while you have active tasks for the day to enable background tracking for deliveries, even when the app is not open.</string>
	<key>NSLocationTemporaryUsageDescriptionDictionary</key>
	<dict>
		<key>full-accuracy</key>
		<string>Precise location is needed to ensure exact pickup and delivery points for parcel deliveries.</string>
	</dict>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>We use your location during active deliveries to provide real-time tracking, optimize routing, and ensure timely pickups and drop-offs of parcels.</string>
	<key>NSMotionUsageDescription</key>
	<string>Motion data is used to detect driver movement and ensure safety during transit, enabling features such as automatic trip detection</string>
	<key>UIAppFonts</key>
	<array>
		<string>Satoshi-Regular.ttf</string>
		<string>Satoshi-Variable.ttf</string>
		<string>Satoshi-Medium.ttf</string>
		<string>Satoshi-Light.ttf</string>
		<string>Satoshi-Bold.ttf</string>
		<string>Satoshi-Black.ttf</string>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>location</string>
		<string>audio</string>
		<string>remote-notification</string>
	</array>
	<key>UILaunchImageFile</key>
	<string>logo</string>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>arm64</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
</dict>
</plist>
