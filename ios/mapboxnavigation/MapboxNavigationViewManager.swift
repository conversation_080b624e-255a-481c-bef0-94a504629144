// MapboxNavigationView (based on <PERSON><PERSON>'s code)
//Portions of this code are derived from <PERSON><PERSON>'s open source project:
//https://github.com/username/repo-name

//Copyright (c) 2020 Homee  
//Licensed under the MIT License:  
//https://opensource.org/licenses/MIT

import UIKit
import React

@objc(MapboxNavigationManager)
class MapboxNavigationManager: RCTViewManager {
  override func view() -> UIView! {
    return MapboxNavigationView();
  }

  override static func requiresMainQueueSetup() -> Bool {
    return true
  }
}
