# Resolve react_native_pods.rb with node to allow for hoisting
# Transform this into a `node_require` generic function:
def node_require(script)
  # Resolve script with node to allow for hoisting
  require Pod::Executable.execute_command('node', ['-p',
    "require.resolve(
      '#{script}',
      {paths: [process.argv[1]]},
    )", __dir__]).strip
end

# Use it to require both react-native's and this package's scripts:
node_require('react-native/scripts/react_native_pods.rb')
node_require('react-native-permissions/scripts/setup.rb')

platform :ios, '15.1'

setup_permissions([
  'Camera',
  'LocationAccuracy',
  'LocationAlways',
  'LocationWhenInUse',
  'Motion',
  'Notifications',
])

linkage = ENV['USE_FRAMEWORKS']
if linkage != nil
  Pod::UI.puts "Configuring Pod with #{linkage}ally linked Frameworks".green
  use_frameworks! :linkage => linkage.to_sym
end

pre_install do |installer|
  $RNMapboxMaps.pre_install(installer)
end

target 'RapidMedical' do
  config = use_native_modules!
  pod 'MapboxNavigation', :git => 'https://github.com/mapbox/mapbox-navigation-ios.git', :tag => 'v2.19.0' # or latest tag

  use_react_native!(
    :path => config[:reactNativePath],
    # An absolute path to your application root.
    :app_path => "#{Pod::Config.instance.installation_root}/.."
  )
end

target 'OneSignalNotificationServiceExtension' do
  pod 'OneSignalXCFramework', '>= 5.0.0', '< 6.0'
end

target 'RapidMedicalDev' do
  config = use_native_modules!

  pod 'MapboxNavigation', :git => 'https://github.com/mapbox/mapbox-navigation-ios.git', :tag => 'v2.19.0'

  use_react_native!(
    :path => config[:reactNativePath],
    :app_path => "#{Pod::Config.instance.installation_root}/.."
  )
end

target 'OneSignalNotificationServiceExtensionDev' do
  pod 'OneSignalXCFramework', '>= 5.0.0', '< 6.0'
end

post_install do |installer|
  $RNMapboxMaps.post_install(installer)
  react_native_post_install(
    installer,
    use_native_modules![:reactNativePath],
    :mac_catalyst_enabled => false,
  )
end
