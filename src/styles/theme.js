import { createTheme } from '@rneui/themed';

import colors from '~/styles/colors';
import { buttonText } from '~/styles/text';

// Documentation: https://reactnativeelements.com/docs/customization/theme_object

const theme = createTheme({
  lightColors: {
    primary: colors.red600,
    secondary: colors.darkBlue500,
    background: colors.darkBlue25,
  },
  darkColors: {
    primary: colors.red600,
    secondary: colors.darkBlue500,
    background: colors.darkBlue25,
  },
  components: {
    Button: () => ({
      buttonStyle: {
        borderRadius: 10,
      },
      titleStyle: {
        ...buttonText,
      },
    }),
  },
  background: colors.backgroundLight,
});

export default theme;
