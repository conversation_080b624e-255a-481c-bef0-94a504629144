import { Dimensions, Platform, ViewStyle } from 'react-native';

import colors from '~/styles/colors';
import { iconView } from '~/styles/icons';

export const deviceWidth = Dimensions.get('window').width;
export const deviceHeight = Dimensions.get('window').height;

export const headerHeight = 126;
export const contentWidth = deviceWidth - 32;
export const contentHeight = deviceHeight - headerHeight;

export const SLIDER_WIDTH = deviceWidth * 0.85;
export const THUMB_SIZE = iconView.height;
export const TRACK_HEIGHT = iconView.height / 2;
export const THUMB_WIDTH = iconView.height * 2;

export const container = {
  flex: 1,
};

export const screenBase = {
  width: '100%',
  flexDirection: 'column',
  backgroundColor: colors.darkBlue25,
  paddingHorizontal: 16,
};

export const screenBaseWhite = {
  width: '100%',
  height: '100%',
  flex: 1,
  paddingHorizontal: 16,
  backgroundColor: colors.white,
};

export const screenContent = {
  top: 126,
  left: 16,
  gap: 16,
  position: 'absolute',
};

export const scrollViewParent = {
  marginTop: 126,
};

export const center = {
  alignItems: 'center',
  justifyContent: 'center',
};

export const shadowSmall = {
  shadowColor: Platform.select({
    ios: colors.black,
    android: colors.transBlack50,
  }),
  shadowOffset: {
    width: 0,
    height: 4,
  },
  shadowOpacity: 0.1,
  shadowRadius: 16,
  elevation: 16,
  justifyContent: 'center',
} as ViewStyle;

export const shadowMedium = {
  shadowColor: colors.transBlack8,
  shadowOffset: {
    width: 0,
    height: 0,
  },
  shadowRadius: 4,
  elevation: 4,
  shadowOpacity: 1,
  borderRadius: 12,
  backgroundColor: '#fff',
  flex: 1,
  width: '100%',
  height: '100%',
  overflow: 'hidden',
};

export const shadowLarge = {
  shadowColor: colors.transBlack8,
  shadowOffset: {
    width: 0,
    height: 0,
  },
  shadowRadius: 4,
  elevation: 4,
  shadowOpacity: 1,
  borderRadius: 12,
  backgroundColor: '#0000',
  flex: 1,
  width: '100%',
  height: 200,
  overflow: 'hidden',
};

export const row = {
  flexDirection: 'row',
};

export const column = {
  flexDirection: 'column',
};

export const rowViewFillSpace = {
  alignItems: 'center',
  justifyContent: 'space-between',
  flexDirection: 'row',
  flex: 1,
};

export const leftView = {
  alignSelf: 'flex-start',
};

export const rightView = {
  alignSelf: 'flex-end',
};

export const stretchView = {
  alignSelf: 'stretch',
};

export const inputBoxContainer = {
  flexDirection: 'row',
  height: 56,
  borderWidth: 1,
  borderRadius: 10,
  alignItems: 'center',
  paddingHorizontal: 10,
  backgroundColor: colors.white,
  borderColor: colors.lightGray,
};

export const inputContainer = {
  gap: 16,
  alignSelf: 'stretch',
};

export const lightRedBorder = {
  borderColor: colors.red100,
};

export const resizeIndicator = {
  position: 'absolute',
  top: 8,
  borderRadius: 999,
  backgroundColor: '#1c1e20',
  width: 32,
  height: 5,
};

export const shadowView = {
  shadowColor: colors.black,
  shadowOpacity: 0.1,
  shadowOffset: {
    width: 0,
    height: 4,
  },
  shadowRadius: 16,
  elevation: 10,
};

export const borderRadiusTopLeftTopRight = {
  borderTopLeftRadius: 20,
  borderTopRightRadius: 20,
};

export const absoluteLeft = {
  position: 'absolute',
  left: 30,
};

export const absoluteRight = {
  position: 'absolute',
  right: 30,
};

export const errorContainer = {
  ...center,
  position: 'absolute',
  zIndex: 1,
};

export const image = {
  width: '100%',
  height: '100%',
};

export const appLoader = {
  ...center,
  position: 'absolute',
  zIndex: 1,
};

export const topRightContainer = {
  top: 16,
  right: 16,
  padding: 8,
  alignItems: 'center',
  position: 'absolute',
};

export const cardView = {
  marginVertical: 10,
  backgroundColor: '#fff',
  padding: 16,
  borderRadius: 10,
};

export const bottomView = {
  position: 'absolute',
  bottom: 10,
  left: 0,
  right: 0,
  backgroundColor: colors.white,
  padding: 16,
  borderTopWidth: 1,
  borderTopColor: colors.lightGray,
};

export const commentCard = {
  alignSelf: 'stretch',
  paddingHorizontal: 8,
  borderRadius: 12,
  backgroundColor: 'white',
  marginVertical: 24,
};

export const radioButtonUnselected = {
  ...center,
  paddingLeft: 10,
  width: 20,
  height: 20,
  borderRadius: 10,
  borderWidth: 1,
  backgroundColor: colors.white,
  borderColor: colors.transBlack50,
  marginRight: 12,
};

export const radioButtonSelected = {
  ...center,
  width: 20,
  height: 20,
  borderRadius: 10,
  backgroundColor: colors.white,
  borderWidth: 6,
  borderColor: colors.darkBlue400,
  marginRight: 12,
};

export const verticalLine = {
  backgroundColor: colors.transGray20,
  width: 1,
  height: 20,
  marginHorizontal: 16,
};

export const horizontalLine = {
  backgroundColor: colors.transGray70,
  width: '100%',
  height: 1,
  paddingHorizontal: 16,
  marginVertical: 8,
} as ViewStyle;

export const horizontalLineTransparent = {
  width: '100%',
  height: 1,
  paddingHorizontal: 16,
  marginVertical: 8,
  backgroundColor: colors.transGray20,
};

export const marginTop8 = {
  marginTop: 8,
};

export const itemSeparator = {
  height: 16,
};

export const stepperView = {
  borderWidth: 1,
  borderRadius: 20,
  borderStyle: 'dashed',
  borderColor: colors.darkBlue300,
  paddingHorizontal: 8,
  paddingVertical: 4,
};
