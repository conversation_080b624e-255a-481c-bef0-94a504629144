const colors = {
  // Red - Primary
  red900: '#2c0707',
  red800: '#580e0e',
  red700: '#851616',
  red600: '#DD2424',
  red400: '#e34b4b',
  red300: '#e97373',
  red200: '#ef9a9a',
  red100: '#f6c2c2',
  red50: '#fce9e9',
  red25: '#fef6f6',
  red10: '#FDF7F7',

  // Dark blue - Secondary
  darkBlue900: '#000517',
  darkBlue800: '#000a2e',
  darkBlue700: '#010f45',
  darkBlue600: '#01145c',
  darkBlue500: '#011973',
  darkBlue400: '#2f428c',
  darkBlue300: '#5c6ca5',
  darkBlue200: '#8a95bf',
  darkBlue100: '#b8bfd8',
  darkBlue50: '#e6e8f1',
  darkBlue25: '#EDEEF3',

  // Gray-brownish
  grey900: '#191919',
  grey800: '#272424',
  grey700: '#4f4848',
  grey600: '#766c6c',
  grey500: '#A2A2AA',
  grey550: '#666666',
  grey400: '#b1a6a6',
  grey300: '#c4bcbc',
  grey200: '#d8d3d3',
  grey100: '#ebe9e9',
  grey50: '#f6f5f3',

  // Light blue
  lightBlue900: '#142332',
  lightBlue800: '#284764',
  lightBlue700: '#3b6a97',
  lightBlue600: '#4f8ec9',
  lightBlue400: '#7fbffc',
  lightBlue300: '#9bcdfc',
  lightBlue200: '#b7dbfd',
  lightBlue100: '#d3e9fe',
  lightBlue50: '#eff7ff',

  // Blue
  blue700: '#007AFF',
  blue600: '#1E90FF',
  blue300: '#F8F8F9',
  blue50: '#f5f7fe',

  // Purple
  purple900: '#10000f',
  purple800: '#21011e',
  purple700: '#31012d',
  purple600: '#42023c',
  purple400: '#71306b',
  purple300: '#905d8c',
  purple200: '#af8bac',
  purple100: '#cfb8cd',
  purple50: '#eee6ed',

  // General colors
  white: '#FFFFFF',
  black: '#000000',
  error: 'red',
  warning: '#F9F1E7',
  info: '#DBEAFE',
  success: '#D1FAE5',

  // Transparent
  transBlack90: 'rgba(0, 0, 0, 0.9)',
  transBlack80: 'rgba(0, 0, 0, 0.8)',
  transBlack70: 'rgba(0, 0, 0, 0.7)',
  transBlack60: 'rgba(0, 0, 0, 0.6)',
  transBlack50: 'rgba(0, 0, 0, 0.5)',
  transBlack40: 'rgba(0, 0, 0, 0.4)',
  transBlack30: 'rgba(0, 0, 0, 0.3)',
  transBlack20: 'rgba(0, 0, 0, 0.2)',
  transBlack10: 'rgba(0, 0, 0, 0.1)',
  transBlack8: 'rgba(0, 0, 0, 0.08)',
  transBlack5: 'rgba(0, 0, 0, 0.05)',
  transWhite100: 'rgba(255, 255, 255, 1)',
  transWhite90: 'rgba(255, 255, 255, 0.9)',
  transWhite80: 'rgba(255, 255, 255, 0.8)',
  transWhite70: 'rgba(255, 255, 255, 0.7)',
  transWhite60: 'rgba(255, 255, 255, 0.6)',
  transWhite50: 'rgba(255, 255, 255, 0.5)',
  transWhite40: 'rgba(255, 255, 255, 0.4)',
  transWhite30: 'rgba(255, 255, 255, 0.3)',
  transWhite20: 'rgba(255, 255, 255, 0.2)',
  transWhite10: 'rgba(255, 255, 255, 0.1)',
  transWhite5: 'rgba(255, 255, 255, 0.05)',
  transGray70: 'rgba(229, 229, 229, 0.7)',
  transGray20: 'rgba(229, 229, 229, 0.3)',
  transGray4: 'rgba(30, 30, 30, 0.04)',
  transGreen50: 'rgba(46, 107, 81, 0.5)',

  // UI specific colors
  textBlack: '#25212C',
  backgroundWhite: '#F5F7FE',
  backgroundLight: '#F8F8F9',
  buttonGray: '#878791',
  lightGray: '#E5E5E6',
  darkGray: '#4d4d4d',
  grayishBlue: '#CDD0D1',
  lightGrayishBlue: '#F6F7F9',
  dodgerBlue: '#1E90FF',

  modalHeader: '#1c1e20',
  modalDesc: '#6C717A',

  yellowLight: '#FFF5E7',
  yellowDark: '#98631B',
  greenLight: '#E7F9F1',
  greenDark: '#367D5F',
  transparent: '#00000000',
};

export default colors;
