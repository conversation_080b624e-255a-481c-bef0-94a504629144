import { Dimensions } from 'react-native';
import colors from '~/styles/colors';

const deviceWidth = Dimensions.get('window').width;

export const buttonFullWidth = {
  width: deviceWidth - 32,
};

export const primarySolidButton = {
  padding: 12,
  justifyContent: 'center',
  borderRadius: 12,
  overflow: 'ellipsis',
};

export const outlinedButton = {
  padding: 12,
  justifyContent: 'center',
  overflow: 'hidden',
};

export const grayOutline = {
  borderColor: colors.gray700,
  borderRadius: 12,
};

export const cameraIconContainer = {
  width: 45,
  height: 45,
  borderRadius: 23,
  backgroundColor: colors.transWhite20,
  justifyContent: 'center',
  alignItems: 'center',
};

export const retryButton = {
  paddingHorizontal: 20,
  paddingVertical: 10,
  borderRadius: 5,
};
