import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';

type TaskStore = {
  completedTasks: string[];
  addCompletedTask: (taskId: string) => void;
  removeCompletedTask: (taskId: string) => void;
  resetCompletedTasks: () => void;
};

export const useTaskStore = create<TaskStore>()(
  persist(
    set => ({
      completedTasks: [], // Initially empty
      addCompletedTask: taskId =>
        set(state => ({
          completedTasks: [...new Set([...state.completedTasks, taskId])],
        })),
      removeCompletedTask: taskId =>
        set(state => ({
          completedTasks: state.completedTasks.filter(id => id !== taskId),
        })),
      resetCompletedTasks: () => set({ completedTasks: [] }),
    }),
    {
      name: 'completed-tasks-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: state => ({ completedTasks: state.completedTasks }),
    },
  ),
);
