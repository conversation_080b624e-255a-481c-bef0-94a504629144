import { Platform, Linking, Alert, ActionSheetIOS } from 'react-native';

interface OpenMapArgs {
  lat: number;
  lng: number;
  label: string;
}

interface MapsApp {
  readonly name: string;
  readonly url: string;
  readonly package: string;
}

interface AlertOptions {
  title?: string;
  message: string;
  buttons?: Array<{
    text: string;
    onPress?: () => void;
    style?: 'default' | 'cancel' | 'destructive';
  }>;
}

const COORDINATE_BOUNDS = {
  LAT: { MIN: -90, MAX: 90 },
  LNG: { MIN: -180, MAX: 180 },
} as const;

const DEFAULT_ERROR_TITLE = 'Error';
const DEFAULT_BUTTON = { text: 'OK' };

const MAPS_APPS: readonly MapsApp[] = [
  {
    name: 'Apple Maps',
    url: 'maps:0,0?q={{label}}@{{destination}}',
    package: 'maps',
  },
  {
    name: 'Google Maps',
    url: 'https://www.google.com/maps/dir/?api=1&destination={{destination}}',
    package: 'com.google.android.apps.maps',
  },
  {
    name: 'Waze',
    url: 'https://waze.com/ul?ll={{destination}}&navigate=yes',
    package: 'com.waze',
  },
] as const;

const ERROR_MESSAGES = {
  NO_APPS: {
    title: 'No Maps Available',
    message: 'No maps applications available on this device',
  },
  UNABLE_TO_OPEN: {
    title: 'Navigation Error',
    message: 'Unable to open maps application',
  },
  INVALID_ARGS: {
    title: 'Invalid Input',
    message: 'Invalid coordinates or label provided',
  },
} as const;

const buildAppUrl = (
  app: MapsApp,
  { lat, lng, label }: OpenMapArgs,
): string => {
  const destination = `${lat},${lng}`;

  return app.url
    .replace('{{destination}}', destination)
    .replace('{{label}}', encodeURIComponent(label))
    .replace('{{latitude}}', lat.toString())
    .replace('{{longitude}}', lng.toString());
};

const checkAppAvailability = async (
  app: MapsApp,
  args: OpenMapArgs,
): Promise<boolean> => {
  try {
    const url = buildAppUrl(app, args);
    return await Linking.canOpenURL(url);
  } catch (error) {
    console.warn(`Error checking ${app.name} availability:`, error);
    return false;
  }
};

const getAvailableApps = async (args: OpenMapArgs): Promise<MapsApp[]> => {
  const availabilityChecks = MAPS_APPS.map(async app => ({
    app,
    isAvailable: await checkAppAvailability(app, args),
  }));

  const results = await Promise.all(availabilityChecks);
  return results.filter(({ isAvailable }) => isAvailable).map(({ app }) => app);
};

const showIOSActionSheet = (
  availableApps: MapsApp[],
  args: OpenMapArgs,
): Promise<void> => {
  return new Promise(resolve => {
    const options = [...availableApps.map(app => app.name), 'Cancel'];
    const cancelButtonIndex = options.length - 1;

    ActionSheetIOS.showActionSheetWithOptions(
      {
        options,
        cancelButtonIndex,
        title: 'Choose Maps App',
      },
      buttonIndex => {
        if (buttonIndex !== cancelButtonIndex) {
          const selectedApp = availableApps[buttonIndex];
          const url = buildAppUrl(selectedApp, args);
          Linking.openURL(url).catch(error => {
            console.error(`Failed to open ${selectedApp.name}:`, error);
            showAlert({
              title: ERROR_MESSAGES.UNABLE_TO_OPEN.title,
              message: ERROR_MESSAGES.UNABLE_TO_OPEN.message,
            });
          });
        }
        resolve();
      },
    );
  });
};

const openAndroidMaps = async (args: OpenMapArgs): Promise<void> => {
  const { lat, lng, label } = args;
  const destination = `${lat},${lng}`;
  const geoUrl = `geo:${destination}?q=${destination}(${encodeURIComponent(label)})`;
  await Linking.openURL(geoUrl);
};

const isValidCoordinates = (lat: number, lng: number): boolean => {
  return (
    typeof lat === 'number' &&
    typeof lng === 'number' &&
    isFinite(lat) &&
    isFinite(lng) &&
    lat >= COORDINATE_BOUNDS.LAT.MIN &&
    lat <= COORDINATE_BOUNDS.LAT.MAX &&
    lng >= COORDINATE_BOUNDS.LNG.MIN &&
    lng <= COORDINATE_BOUNDS.LNG.MAX
  );
};

const isValidLabel = (label: string): boolean => {
  return typeof label === 'string' && label.trim().length > 0;
};

const showAlert = ({
  title = DEFAULT_ERROR_TITLE,
  message,
  buttons = [DEFAULT_BUTTON],
}: AlertOptions): void => {
  Alert.alert(title, message, buttons);
};

export const openMap = async (args: OpenMapArgs): Promise<void> => {
  try {
    if (!isValidCoordinates(args.lat, args.lng)) {
      console.warn('Invalid arguments provided to openMap:', args);
      showAlert({
        title: ERROR_MESSAGES.INVALID_ARGS.title,
        message: ERROR_MESSAGES.INVALID_ARGS.message,
      });
      return;
    }

    if (!isValidLabel(args.label)) {
      console.warn('Invalid label provided to openMap:', { label: args.label });
      showAlert({
        title: ERROR_MESSAGES.INVALID_ARGS.title,
        message: ERROR_MESSAGES.INVALID_ARGS.message,
      });
      return;
    }

    if (Platform.OS === 'ios') {
      const availableApps = await getAvailableApps(args);

      if (availableApps.length === 0) {
        showAlert({
          message: ERROR_MESSAGES.NO_APPS.message,
          title: ERROR_MESSAGES.NO_APPS.title,
        });
        return;
      }

      if (availableApps.length === 1) {
        const url = buildAppUrl(availableApps[0], args);
        await Linking.openURL(url);
      } else {
        await showIOSActionSheet(availableApps, args);
      }
    } else {
      await openAndroidMaps(args);
    }
  } catch (error) {
    console.error('Failed to open map:', error);
    showAlert({
      title: ERROR_MESSAGES.UNABLE_TO_OPEN.title,
      message: ERROR_MESSAGES.UNABLE_TO_OPEN.message,
    });
  }
};
