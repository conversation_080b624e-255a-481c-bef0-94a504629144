export const interpolateString = (
  str: string,
  values: Record<string, string | number>,
) => {
  return str.replace(/{{([^}]+)}}/g, (match, p1) =>
    values[p1] != null ? String(values[p1]) : match,
  );
};

export const parseBoolean = (input?: string | boolean): boolean => {
  if (!input) return false;
  if (typeof input === 'boolean') return input;
  return input.trim().toLowerCase() === 'true';
};

/**
 * Normalizes barcode value for formats like UPC-A embedded in EAN-13.
 *
 * @param raw - Raw scanned barcode value
 * @param type - Barcode format (e.g., 'ean-13', 'qr', etc.)
 * @returns Normalized string value
 */
export function normalizeBarcodeValue(raw: string, type?: string): string {
  if (!raw) return '';

  const value = raw.trim();

  // Only normalize when explicitly confirmed as UPC-A inside EAN-13
  if (type === 'ean-13' && value?.length === 13 && value.startsWith('0')) {
    const upcCandidate = value.slice(1);

    // Heuristic: treat as UPC-A only if the result is 12 digits
    if (/^\d{12}$/.test(upcCandidate)) {
      return upcCandidate;
    }
  }

  return value;
}
