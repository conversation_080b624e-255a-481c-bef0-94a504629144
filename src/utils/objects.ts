/**
 * Retrieves a nested value from an object using a dot-separated path.
 * E.g., path "user.profile.name" accesses obj.user.profile.name.
 *
 * @param {any} obj - The object from which to extract the value.
 * @param {string} path - The dot-separated path specifying the nested field.
 * @returns {any} The value found at the specified path, or undefined if not found.
 */
export function getValueByPath(obj: any, path: string): any {
  return path.split('.').reduce((current, key) => current?.[key], obj);
}

/**
 * Stores a value in an object at a dot-separated path, creating objects along the way if needed.
 * E.g., path "settings.preferences.theme" will set obj.settings.preferences.theme.
 *
 * @param {any} obj - The object in which to store the value.
 * @param {string} path - The dot-separated path specifying where to store the value.
 * @param {any} value - The value to store.
 * @returns {void}
 */
export function setValueByPath(obj: any, path: string, value: any): void {
  const segments = path.split('.');
  const lastKey = segments.pop();
  if (!lastKey) return;

  // Traverse the path, creating intermediate objects if they don't exist.
  const target = segments.reduce((acc, key) => {
    if (!acc[key]) {
      acc[key] = {};
    }
    return acc[key];
  }, obj);

  // Finally, set the value at the last step.
  target[lastKey] = value;
}
