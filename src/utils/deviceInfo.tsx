import { getFreeDiskStorage } from 'react-native-device-info';

export const formatBytes = (bytes: number, decimals = 2) => {
  // https://stackoverflow.com/questions/15900485/correct-way-to-convert-size-in-bytes-to-kb-mb-gb-in-javascript

  if (!+bytes) return '0 Bytes';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = [
    'Bytes',
    'KiB',
    'MiB',
    'GiB',
    'TiB',
    'PiB',
    'EiB',
    'ZiB',
    'YiB',
  ];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(dm))} ${sizes[i]}`;
};

export const getPhoneAvailableDiskSpace = async () => {
  try {
    const freeSpace = await getFreeDiskStorage();
    const byteString = formatBytes(freeSpace);

    return byteString;
  } catch (error) {
    console.error('device-info.ts: Error getting available storage:', error);
  }

  return null;
};
