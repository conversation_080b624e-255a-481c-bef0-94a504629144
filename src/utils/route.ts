import colors from '~/styles/colors';
import { RouteStatus } from '~/types/routes.types';

export const getStepperPillColors = (status: RouteStatus): [string, string] => {
  if (!status) {
    return [colors.blue50, colors.darkBlue500];
  }

  switch (status) {
    case 'Ready':
      return [colors.blue50, colors.darkBlue500];
    case 'Scheduled':
      return [colors.backgroundLight, colors.darkGray];
    case 'Complete':
      return [colors.greenLight, colors.greenDark];
    case 'InProgress':
      return [colors.yellowLight, colors.yellowDark];
    case 'Cancelled':
      return [colors.red25, colors.red600];
    default:
      return [colors.blue50, colors.darkBlue500];
  }
};
