import * as Keychain from 'react-native-keychain';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { TokenData } from '~/services/TokenService';

export class KeychainService {
  static async setTokensInKeychain(tokenData: TokenData) {
    await Keychain.setGenericPassword(
      'rapid_medical_tokens',
      JSON.stringify(tokenData),
    );
    return true;
  }

  static async getTokenFromKeychain() {
    try {
      const token = await Keychain.getGenericPassword();
      if (token) {
        return token;
      } else {
        console.info(
          'KeychainService: getTokenFromKeychain(): No token stored',
        );
      }
    } catch (error) {
      console.error(
        'KeychainService: getTokenFromKeychain(): Failed to access Keychain',
        error,
      );
    }
    return null;
  }

  static async removeTokenFromKeychain() {
    try {
      return await Keychain.resetGenericPassword();
    } catch (error) {
      console.error(
        'KeychainService: Failed to remove token from Keychain',
        error,
      );
      return false;
    }
  }

  static async clearKeychainOnReinstall() {
    const isAppInitialled = await AsyncStorage.getItem('isAppInitialled');
    if (!isAppInitialled) {
      this.removeTokenFromKeychain();
    }
    await AsyncStorage.setItem('isAppInitialled', 'true');
  }
}
