import { MotionActivity } from 'react-native-background-geolocation';
import { SalesforceActivityTypes } from '~/types/location.types';
import { RouteSummary } from '~/types/routes.types';
import { Stop } from '~/types/stops.types';

type GeofenceInputValue = string | number | null | undefined;
/**
 * Maps the activity type from MotionActivity to SalesforceActivityTypes
 *
 * @param {MotionActivity['type'] | null | undefined} activity - The activity type from MotionActivity
 * @returns {SalesforceActivityTypes} The mapped Salesforce activity type
 */
export function mapActivityType(
  activity: MotionActivity['type'] | null | undefined,
): SalesforceActivityTypes {
  if (!activity) {
    return 'Still';
  }

  // Mapping based on Salesforce picklist type
  const activityMappings: Record<
    MotionActivity['type'],
    SalesforceActivityTypes
  > = {
    in_vehicle: 'In Vehicle',
    on_foot: 'On Foot',
    walking: 'On Foot',
    running: 'On Foot',
    still: 'Still',
    on_bicycle: 'In Vehicle',
    unknown: 'Still',
  };

  return activityMappings[activity] || 'Still';
}

const EARTH_RADIUS_METERS = 6371e3; // Approx Earth radius in meters

/**
 * @description Converts an angle in degrees to its approximate measure in radians for use in trigonometric calculations.
 *
 * @param {number} degrees - The angle in degrees to be converted.
 * @returns {number} The angle in radians.
 */
function convertDegreesToRadians(degrees: number): number {
  return degrees * (Math.PI / 180);
}

/**
 * @description Calculates the distance in meters between two points on Earth, taking into account the planet's curvature.
 *
 * The calculation uses the Haversine formula, which converts latitude/longitude differences into radians.
 * It then computes a fraction of the Earth's radius, ultimately returning the distance in meters.
 *
 * @param {number} startingLatitude   - The latitude (in degrees) of the starting location.
 * @param {number} startingLongitude  - The longitude (in degrees) of the starting location.
 * @param {number} endingLatitude     - The latitude (in degrees) of the ending location.
 * @param {number} endingLongitude    - The longitude (in degrees) of the ending location.
 * @returns {number} The calculated distance in meters between the two coordinates.
 */
export function calculateDistanceBetweenCoordinatesInMeters(
  startingLatitude: number,
  startingLongitude: number,
  endingLatitude: number,
  endingLongitude: number,
): number {
  // Convert differences in latitude and longitude to radians.
  const differenceInLatitude = convertDegreesToRadians(
    endingLatitude - startingLatitude,
  );
  const differenceInLongitude = convertDegreesToRadians(
    endingLongitude - startingLongitude,
  );

  // Convert starting and ending latitudes to radians.
  const radStartingLatitude = convertDegreesToRadians(startingLatitude);
  const radEndingLatitude = convertDegreesToRadians(endingLatitude);

  // Apply the Haversine formula.
  const fractionOfEarthRadius =
    Math.sin(differenceInLatitude / 2) * Math.sin(differenceInLatitude / 2) +
    Math.cos(radStartingLatitude) *
      Math.cos(radEndingLatitude) *
      Math.sin(differenceInLongitude / 2) *
      Math.sin(differenceInLongitude / 2);

  const centralAngle =
    2 *
    Math.atan2(
      Math.sqrt(fractionOfEarthRadius),
      Math.sqrt(1 - fractionOfEarthRadius),
    );

  // Multiply the central angle by the Earth's approximate radius to get the distance in meters.
  return EARTH_RADIUS_METERS * centralAngle;
}

/**
 * Checks if the given coordinate object has valid numeric lat/lon in the real-world range.
 * @param coord An object with optional lat/lon properties.
 * @returns true if coord.lat is between -90 and 90 and coord.lon is between -180 and 180, otherwise false.
 */
export function isValidCoordinate(
  coord: { lat?: number; lon?: number } | null | undefined,
): boolean {
  if (!coord) {
    return false;
  }

  const latIsValid =
    typeof coord.lat === 'number' && coord.lat >= -90 && coord.lat <= 90;
  const lonIsValid =
    typeof coord.lon === 'number' && coord.lon >= -180 && coord.lon <= 180;

  return latIsValid && lonIsValid;
}

export function getValidGeofenceDistance(
  value: GeofenceInputValue,
): number | undefined {
  if (!value) {
    return undefined;
  }

  const parsedValue = typeof value === 'number' ? value : parseFloat(value);
  return Number.isFinite(parsedValue) && parsedValue > 0
    ? parsedValue
    : undefined;
}

export function getEffectiveGeofenceDistance(param: {
  route?: RouteSummary | null;
  stop?: Stop | null;
}): number | undefined {
  const stop = param.stop ?? null;
  const route = param.route ?? null;

  const distances = [
    getValidGeofenceDistance(stop?.Location__r?.Geofencing_Distance__c),
    getValidGeofenceDistance(stop?.Geofencing_Distance__c),
    getValidGeofenceDistance(route?.Geofencing_Distance__c),
    getValidGeofenceDistance(route?.Route__r?.Geofencing_Distance__c),
  ];

  return distances.find(
    (distance): distance is number => distance !== undefined,
  );
}

/**
 * Converts a distance in meters to miles
 * @param meters - The distance in meters to convert
 * @returns The equivalent distance in miles, rounded to 3 decimal places
 */
export function metersToMiles(meters: number): number {
  const METERS_PER_MILE = 1609.344;

  if (meters < 0) {
    throw new Error('Distance cannot be negative');
  }

  const miles = meters / METERS_PER_MILE;

  // Round to 3 decimal places for practical use
  return Math.round(miles * 1000) / 1000;
}
