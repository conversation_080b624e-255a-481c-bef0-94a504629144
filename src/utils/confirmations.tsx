import React from 'react';
import en from '~/localization/en';
import colors from '~/styles/colors';
import {
  GreenCheckWithBackground,
  BoxWithBackground,
  HotWeather,
} from '~/assets/icons';
import {
  ConfirmationType,
  ConfirmationSubType,
} from '~/types/confirmations.types';

import { CheckCircle } from '~/components/icons';
import OverlappingIcons from '~/components/icons/OverlappingIcons';

export const ConfirmationTypeInfo: Record<
  ConfirmationType,
  { title: string; icon: React.ReactNode; color: string }
> = {
  [ConfirmationType.Route]: {
    title: en.route_completed,
    icon: <GreenCheckWithBackground />,
    color: colors.transGreen50,
  },
  [ConfirmationType.Pickup]: {
    title: en.pickup_completed,
    icon: (
      <OverlappingIcons
        mainIcon={<BoxWithBackground />}
        overlayIcon={<CheckCircle />}
      />
    ),
    color: colors.darkBlue500,
  },
  [ConfirmationType.Delivery]: {
    title: en.dropoff_completed,
    icon: (
      <OverlappingIcons
        mainIcon={<BoxWithBackground />}
        overlayIcon={<CheckCircle />}
      />
    ),
    color: colors.darkBlue500,
  },
  [ConfirmationType.Stop]: {
    title: en.stop_completed,
    icon: <GreenCheckWithBackground />,
    color: colors.darkBlue500,
  },
  [ConfirmationType.Protocol]: {
    title: en.protocol_completed,
    icon: <HotWeather />,
    color: colors.darkBlue500,
  },
};

export const subLabelMapping: Record<ConfirmationSubType, string> = {
  [ConfirmationSubType.RouteCompleted]: en.great_job,
  [ConfirmationSubType.PickupContinueRoute]: en.sub_pickup_continue_route,
  [ConfirmationSubType.PickupContinueDropoff]: en.sub_pickup_continue_dropoff,
  [ConfirmationSubType.PickupSno]: en.sub_pickup_sno,
  [ConfirmationSubType.PickupNoConfirm]: en.sub_pickup_no_confirm,
  [ConfirmationSubType.DropoffContinueRoute]: en.sub_dropoff_continue_route,
  [ConfirmationSubType.DropoffContinuePickup]: en.sub_dropoff_continue_pickup,
  [ConfirmationSubType.SurveyCompleted]: en.great_job,
  [ConfirmationSubType.StopCompleted]: en.great_job,
  [ConfirmationSubType.ProtocolCompleted]: en.great_job,
};
