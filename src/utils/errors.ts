import en from '~/localization/en';

export const getHumanReadableErrorMessage = (error: any) => {
  if (error) {
    const errorInfo = error.response?.data;
    const errorMessage = error.message;

    if (
      errorInfo &&
      typeof errorInfo === 'object' &&
      'error_description' in errorInfo
    ) {
      const errorText = errorInfo.error_description;

      switch (errorText) {
        case 'authentication failure':
          return en.error_auth_failure;
      }
    } else if (errorMessage) {
      switch (errorMessage) {
        case 'Network Error':
          return en.error_no_network;
      }
    }
  }

  return en.error_generic;
};
