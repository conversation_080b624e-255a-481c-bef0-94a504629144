import { DateTime } from 'luxon';

import en from '~/localization/en';

export const ONE_SECOND_IN_MS = 1000;

export function getDeviceLocale(): string {
  try {
    return Intl.DateTimeFormat().resolvedOptions().locale;
  } catch (error) {
    console.error('Error getting device locale:', error);
    return 'en-US';
  }
}

export function getDeviceTimeZone(): string {
  try {
    return Intl.DateTimeFormat().resolvedOptions().timeZone;
  } catch (error) {
    console.error(
      'dateAndTime.ts: getDeviceTimeZone(): Error getting device time zone:',
      error,
    );
    return 'UTC';
  }
}

type DateString = string | null | undefined;

export function getFormattedTime(
  dateString: DateString,
  withTimeZone: boolean = true,
) {
  if (!dateString) {
    return en.invalid_date;
  }

  const locale = getDeviceLocale();
  const zone = getDeviceTimeZone();

  try {
    const dateTime = DateTime.fromISO(dateString, { zone, locale });

    if (!dateTime.isValid) {
      console.warn(`Invalid date string: ${dateString}`);
      return en.invalid_date;
    }

    if (withTimeZone) {
      return dateTime.toFormat('h:mm a ZZZZ').toUpperCase();
    }

    return dateTime.toFormat('h:mm a').toUpperCase();
  } catch (error) {
    console.error('Error formatting time:', error);
    return en.invalid_date;
  }
}

export function getFormattedTimeWithoutTimeZone(dateString: DateString) {
  return getFormattedTime(dateString, false);
}

export const formatTimeRemaining = (seconds?: number): string => {
  if (!seconds || seconds < 0) {
    return '00:00';
  }

  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
};

export const getTimeOfDay = () => {
  const now = new Date();
  const hour = now.getHours();

  if (hour >= 5 && hour < 12) {
    return 'morning';
  } else if (hour >= 12 && hour < 17) {
    return 'afternoon';
  } else {
    return 'evening';
  }
};

/**
 * Parse a date string into a Date object in UTC
 * @param dateString - The date string to parse
 * @returns A Date object in UTC
 * Function copied from 1.0 version of the app
 */
const parseDate = (dateString: string) => {
  const [datePart, timePart] = dateString.split('T');
  const [year, month, day] = datePart.split('-').map(Number);
  const [hourPart] = timePart.split('.');
  const [hour, minute] = hourPart.split(':').map(Number);

  return new Date(Date.UTC(year, month - 1, day, hour, minute));
};

/**
 * Get a formatted time string using a given offset
 * @param time - The time string to format
 * @param offset - The offset to apply to the time
 * @returns A formatted time string
 * Function copied from 1.0 version of the app
 */
export const getFormattedTimeUsingOffset = async (
  time: number | string | null,
  offset: number,
) => {
  if (!time) {
    return 'N/A';
  }

  const date = parseDate(time as any);

  // Adjust the date object based on the given offset
  date.setUTCHours(date.getUTCHours() + offset * -1);

  // Extract hours and minutes
  let hours = date.getUTCHours();
  const minutes = date.getUTCMinutes();

  // Convert to 12-hour format
  const ampm = hours >= 12 ? 'PM' : 'AM';
  hours = hours % 12 || 12;
  const minutesStr = minutes < 10 ? '0' + minutes : minutes;

  return `${hours}:${minutesStr} ${ampm}`;
};
