/**
 * Checks if an email is valid according to a general HTML-spec-like pattern.
 *
 * Developer instructions:
 * - Use this helper for strict email format checks.
 * - Ensure you pass a trimmed string or handle spaces externally.
 *
 * Example usage:
 * ```ts
 * const isValid = checkIfValidEmail('<EMAIL>');
 * console.log(isValid); // true
 * ```
 *
 * @param {string} email - The email string to validate.
 * @returns {boolean} True if email passes the regex test, otherwise false.
 */
export const checkIfValidEmail = (email: string): boolean => {
  const emailRegex =
    /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;

  return emailRegex.test(email);
};

/**
 * Determines whether an email string is valid or not, returning a descriptive error.
 *
 * Developer instructions:
 * - Use this when you need to provide a user-friendly error message.
 * - If null is returned, it means the email is valid.
 * - If a non-null string is returned, it is the error message describing the issue.
 *
 * Example usage:
 * ```ts
 * const errorMsg = getEmailValidationMessage('');
 * // errorMsg -> 'Email cannot be empty'
 * ```
 *
 * @param {string} email - The email string to validate.
 * @returns {string | null} Null if valid, otherwise an error message string.
 */
export const getEmailValidationMessage = (email: string): string | null => {
  if (email === null) {
    return null;
  } else if (email === '' || !email.trim()) {
    return 'Email cannot be empty';
  } else if (email.includes(' ')) {
    return 'Email cannot include spaces';
  } else if (!checkIfValidEmail(email)) {
    return 'Enter a valid email address';
  }

  return null;
};

/**
 * Evaluates a password string for basic requirements.
 *
 * Developer instructions:
 * - Minimum requirement: no empty string, no spaces, at least 8 characters.
 * - Returns a descriptive error string if invalid.
 *
 * Example usage:
 * ```ts
 * const passErr = getPasswordValidationMessage('my pa');
 * // passErr -> 'Password cannot include spaces'
 * ```
 *
 * @param {string} password - The password string to validate.
 * @returns {string | null} Null if valid, otherwise an error message string.
 */
export const getPasswordValidationMessage = (
  password: string,
): string | null => {
  if (password === '') {
    return 'Password cannot be empty';
  } else if (password.includes(' ')) {
    return 'Password cannot include spaces';
  } else if (password?.length < 8) {
    return 'Password needs to be at least 8 characters';
  }

  return null;
};

/**
 * Checks if a value satisfies the "required" constraint.
 *
 * Developer instructions:
 * - This function returns a boolean, so you can decide how to handle the error message.
 * - Typically used in forms to ensure the user has entered data.
 *
 * Example usage:
 * ```ts
 * const isValid = isRequiredValid('Hello');
 * // isValid -> true
 * ```
 *
 * @param {*} value - The value to check (usually a string).
 * @returns {boolean} True if value is non-empty, otherwise false.
 */
export const isRequiredValid = (value: any): boolean => {
  if (typeof value === 'object' && !Array.isArray(value) && value !== null) {
    return Object.keys(value).length > 0;
  }
  if (value === null || value === undefined) return false;
  if (typeof value === 'string' && !value.trim()) return false;
  if (Array.isArray(value) && value.length === 0) return false;
  return true;
};

/**
 * Checks if a string meets a minimum length.
 *
 * Developer instructions:
 * - Pass the user-entered string and the required min length.
 * - If return is true, it meets or exceeds the minimum.
 *
 * Example usage:
 * ```ts
 * const longEnough = isMinLengthValid('abcde', 5);
 * // longEnough -> true
 * ```
 *
 * @param {string} value - The string to evaluate.
 * @param {number} minLength - The required minimum length.
 * @returns {boolean} True if `value.length >= minLength`, otherwise false.
 */
export const isMinLengthValid = (value: string, minLength: number): boolean => {
  return value.length >= minLength;
};

/**
 * Checks if an array meets a minimum length.
 *
 * Developer instructions:
 * - Pass the user-provided array and the required min length.
 * - If return is true, the array has enough elements.
 *
 * Example usage:
 * ```ts
 * const valid = isArrayMinLengthValid(['a', 'b', 'c'], 2);
 * // valid -> true
 * ```
 *
 * @param {any[]} value - The array to evaluate.
 * @param {number} minLength - The required minimum length.
 * @returns {boolean} True if `value.length >= minLength`, otherwise false.
 */
export const isArrayMinLengthValid = (
  value: any[],
  minLength: number,
): boolean => {
  return value.length >= minLength;
};

/**
 * Checks if a string does not exceed a maximum length.
 *
 * Developer instructions:
 * - Pass the user-entered string and the allowed max length.
 * - If return is true, it does not exceed that length.
 *
 * Example usage:
 * ```ts
 * const shortEnough = isMaxLengthValid('hello', 5);
 * // shortEnough -> true
 * ```
 *
 * @param {string} value - The string to evaluate.
 * @param {number} maxLength - The maximum allowed length.
 * @returns {boolean} True if `value.length <= maxLength`, otherwise false.
 */
export const isMaxLengthValid = (value: string, maxLength: number): boolean => {
  return value.length <= maxLength;
};

/**
 * Validates a string against a given regular expression.
 *
 * Developer instructions:
 * - Provide the user input and a regex pattern as a string.
 * - Returns true if the value matches the pattern, false otherwise.
 *
 * Example usage:
 * ```ts
 * const matches = isRegexValid('abc123', '^[a-z0-9]+$');
 * // matches -> true
 * ```
 *
 * @param {string} value - The string to evaluate.
 * @param {string} pattern - The regular expression pattern (without slashes).
 * @returns {boolean} True if the regex matches, otherwise false.
 */
export const isRegexValid = (value: string, pattern: string): boolean => {
  const regex = new RegExp(pattern);
  return regex.test(value);
};

/**
 * Checks if a phone number contains only numeric characters.
 *
 * Developer instructions:
 * - This is a simplified approach that expects strictly digits.
 * - If you allow international formats (like +, parentheses), adjust the logic.
 *
 * Example usage:
 * ```ts
 * const isValidPhone = isPhoneNumberValid('1234567890');
 * // isValidPhone -> true
 * ```
 *
 * @param {string} phone - The phone string to check.
 * @returns {boolean} True if it is a non-empty numeric string, otherwise false.
 */
export const isPhoneNumberValid = (phone: string): boolean => {
  if (!phone.trim()) return false;
  const numericRegex = /^[0-9]+$/;
  return numericRegex.test(phone);
};

/**
 * Checks if an email string is valid using the same logic as checkIfValidEmail.
 *
 * Developer instructions:
 * - This is just a convenience wrapper.
 * - Returns boolean, so you can handle error messages externally.
 *
 * Example usage:
 * ```ts
 * const isValid = isEmailValid('<EMAIL>');
 * // isValid -> true
 * ```
 *
 * @param {string} email - The email string to validate.
 * @returns {boolean} True if the email passes the checkIfValidEmail test, otherwise false.
 */
export const isEmailValid = (email: string): boolean => {
  if (!email.trim()) return false;
  return checkIfValidEmail(email);
};

/**
 * Simplified check for a US-style Social Security Number in the format XXX-XX-XXXX.
 *
 * Developer instructions:
 * - Not suitable for production-level verification as real SSNs have more rules.
 * - Extend or replace this with a more robust approach if needed.
 *
 * Example usage:
 * ```ts
 * const validSSN = isSSNValid('***********');
 * // validSSN -> true
 * ```
 *
 * @param {string} value - The string to check.
 * @returns {boolean} True if it matches the pattern, otherwise false.
 */
export const isSSNValid = (value: string): boolean => {
  const ssnRegex = /^\d{3}-\d{2}-\d{4}$/;
  if (!value.trim()) return false;
  return ssnRegex.test(value);
};

/**
 * Checks whether a given file size (in MB) is within an allowable maximum.
 *
 * Developer instructions:
 * - Typically used in file upload constraints.
 * - Provide the file size and the max limit in MB.
 *
 * Example usage:
 * ```ts
 * const withinLimit = isFileSizeValid(2, 5);
 * // withinLimit -> true (2 MB <= 5 MB)
 * ```
 *
 * @param {number} fileSizeMB - The file size in megabytes.
 * @param {number} maxSizeMB - The maximum allowed size in megabytes.
 * @returns {boolean} True if fileSizeMB <= maxSizeMB, otherwise false.
 */
export const isFileSizeValid = (
  fileSizeMB: number,
  maxSizeMB: number,
): boolean => {
  return fileSizeMB <= maxSizeMB;
};

/**
 * Determines if a file has an allowed extension.
 *
 * Developer instructions:
 * - Provide the fileName and an array of allowed extensions (e.g., ["jpg", "png"]).
 * - The check is case-insensitive, but expects the extension after a dot at the end of the file name.
 *
 * Example usage:
 * ```ts
 * const isAllowed = isFileTypeAllowed('photo.jpg', ['jpg', 'png']);
 * // isAllowed -> true
 * ```
 *
 * @param {string} fileName - The name of the file, including extension.
 * @param {string[]} allowedExtensions - Array of allowed file extensions (no dot).
 * @returns {boolean} True if file extension is in the allowed list, otherwise false.
 */
export const isFileTypeAllowed = (
  fileName: string,
  allowedExtensions: string[],
): boolean => {
  const extMatch = fileName.toLowerCase().match(/\.([a-z0-9]+)$/);
  if (!extMatch) return false; // no extension
  const extension = extMatch[1];
  return allowedExtensions.includes(extension);
};
