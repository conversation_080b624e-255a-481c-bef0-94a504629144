import ImageResizer, {
  ResizeFormat,
} from '@bam.tech/react-native-image-resizer';
import { readAsync } from '@lodev09/react-native-exify';
import { Platform } from 'react-native';
import RNFS from 'react-native-fs';

const IMAGE_EXTENSIONS = ['jpg', 'jpeg', 'png', 'avif', 'webp'];

const isImageType = (fileName: string) => {
  return IMAGE_EXTENSIONS.some(type =>
    fileName.toLowerCase().endsWith(`.${type}`),
  );
};

const compressImageFromUri = async (
  uri: string,
  format: ResizeFormat = 'JPEG',
  targetWidth: number = 800,
  targetHeight: number = 800,
  compressionPercentage: number = 40,
): Promise<string | null> => {
  try {
    if (!uri) {
      console.error(
        'imageUtils.ts: compressImageFromUri(): URI is undefined, or empty',
      );
      return null;
    }

    const orientation = await getExifOrientation(uri);
    const rotation =
      Platform.OS === 'ios'
        ? 0
        : mapExifOrientationToRotation(orientation ?? 1);

    const result = await ImageResizer.createResizedImage(
      uri,
      targetWidth,
      targetHeight,
      format,
      compressionPercentage,
      rotation,
      undefined,
      false,
      {
        mode: 'cover',
        onlyScaleDown: true,
      },
    );
    deleteTempFile(uri);

    return result?.uri;
  } catch (error) {
    console.error(
      'imageUtils.ts: compressImageFromUri(): Failed to compress image',
      error,
    );
    return null;
  }
};

const getExifOrientation = async (
  imagePath: string,
): Promise<number | undefined> => {
  try {
    const tags = await readAsync(`file://${imagePath}`);
    return tags?.Orientation;
  } catch (error) {
    console.error(
      'imageUtils.ts: getExifOrientation(): Failed to read EXIF data',
      error,
    );
    return undefined;
  }
};

const mapExifOrientationToRotation = (orientation: number): number => {
  switch (orientation) {
    case 3:
      return 180;
    case 6:
      return 90;
    case 8:
      return 270;
    default:
      return 0;
  }
};

const removeFilesInBatch = async (
  filePaths: string[],
  batchSize: number = 10,
) => {
  for (let i = 0; i < filePaths.length; i += batchSize) {
    const batch = filePaths.slice(i, i + batchSize);
    const promises = batch.map(filePath => deleteTempFile(filePath));

    try {
      await Promise.allSettled(promises);
    } catch (error) {
      console.error(
        'imageUtils.ts: removeFilesInBatch(): Failed to delete files in batch',
        error,
      );
      throw error;
    }
  }
};

export const getRandomImgFile = async () => {
  const imageArray = [
    // Red 1x1 PNG
    'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8z8BQDwAEhQGAhKmMIQAAAABJRU5ErkJggg==',
    // Blue 1x1 PNG
    'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==',
    // Green 1x1 PNG
    'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
    // Yellow 1x1 PNG
    'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==',
    // Purple 1x1 PNG
    'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==',
    // Orange 1x1 PNG
    'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==',
    // White 1x1 PNG
    'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==',
    // Black 1x1 PNG
    'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==',
  ];
  const randomIndex = Math.floor(Math.random() * imageArray.length);

  const randomImg = imageArray[randomIndex];

  return randomImg;
};

export const convertFileToBase64 = async (
  filePath: string,
): Promise<string | null> => {
  try {
    return await RNFS.readFile(filePath, 'base64');
  } catch (error) {
    console.error(
      error,
      'imageUtils.ts: convertFileToBase64(): Failed to read file as base64 string',
    );
    return null;
  }
};

export const deleteTempFile = async (filePath: string) => {
  try {
    const path = filePath.includes('file://')
      ? filePath.replace('file://', '')
      : filePath;

    if (path?.length) {
      const exists = await RNFS.exists(path);
      if (exists) {
        await RNFS.unlink(path);
      }
    }
  } catch (error) {
    console.error(
      'imageUtils.ts: deleteTempFile(): Failed to delete temp file',
      error,
    );
    throw error;
  }
};

const compressImageAndConvertToBase64 = async (imagePath: string) => {
  try {
    const compressedImagePath = await compressImageFromUri(imagePath);
    if (!compressedImagePath) {
      console.error(
        'imageUtils.ts: compressImageAndConvertToBase64(): Failed to compress image',
      );
      return null;
    }
    const base64 = await convertFileToBase64(compressedImagePath);
    if (!base64) {
      console.error(
        'imageUtils.ts: compressImageAndConvertToBase64(): Failed to convert image to base64',
      );
      return null;
    }
    return base64;
  } catch (error) {
    console.error(
      'imageUtils.ts: compressImageAndConvertToBase64(): Failed to compress image and convert to base64',
      error,
    );
    return null;
  }
};

async function deleteCachedImages() {
  try {
    const exists = await RNFS.exists(RNFS.CachesDirectoryPath);
    if (!exists) {
      console.info(
        'imageUtils.ts: deleteCachedImages(): CachesDirectoryPath does not exist',
      );
      return;
    }
    const files = await RNFS.readDir(RNFS.CachesDirectoryPath);
    for (const file of files) {
      if (file.isFile() && isImageType(file.name)) {
        const filePath = [RNFS.CachesDirectoryPath, file.name].join('/');
        try {
          await RNFS.unlink(filePath);
        } catch (error) {
          console.error(
            `imageUtils.ts: deleteCachedImages(): Failed to delete file ${filePath}:`,
            error,
          );
        }
      }
    }
  } catch (error) {
    console.error(
      'imageUtils.ts: deleteCachedImages(): Failed to delete cached images',
      error,
    );
  }
}

export const ImageTitle = {
  proofOfService: 'proofOfService',
  photosTaken: 'photosTaken',
  parcel: 'parcel',
  photoEmptyLockbox: 'photoEmptyLockbox',
  photoSNOTag: 'photoSNOTag',
  photoLockboxArea: 'photoLockboxArea',
  photoPerimeter: 'photoPerimeter',
  emptyCoolerInVehicleStepExamplePhotos:
    'emptyCoolerInVehicleStepExamplePhotos',
  areaWhereCoolerIsStoredPhotos: 'areaWhereCoolerIsStoredPhotos',
  areaWhereSamplesAreHandledPhotos: 'areaWhereSamplesAreHandledPhotos',
  dropOffSignature: 'dropOffSignature',
  thermometerPhoto: 'thermometerPhoto',
  lockboxPhoto: 'lockboxPhoto',
  parcelBarcodeBypassPhoto: 'parcelBarcodeBypassPhoto',
} as const;

export const ImageTitleInfo: Record<
  keyof typeof ImageTitle,
  { title: string; multiple: boolean }
> = {
  [ImageTitle.proofOfService]: {
    title: 'proof_of_service',
    multiple: false,
  },
  [ImageTitle.photosTaken]: {
    title: 'photos_taken',
    multiple: true,
  },
  [ImageTitle.parcel]: {
    title: 'parcel',
    multiple: true,
  },
  [ImageTitle.photoEmptyLockbox]: {
    title: 'sno_empty_lockbox',
    multiple: false,
  },
  [ImageTitle.photoSNOTag]: {
    title: 'sno_tag_left',
    multiple: false,
  },
  [ImageTitle.photoLockboxArea]: {
    title: 'sno_area_around_lockbox',
    multiple: false,
  },
  [ImageTitle.photoPerimeter]: {
    title: 'perimeter_check',
    multiple: true,
  },
  [ImageTitle.emptyCoolerInVehicleStepExamplePhotos]: {
    title: 'survey_empty_cooler',
    multiple: false,
  },
  [ImageTitle.areaWhereCoolerIsStoredPhotos]: {
    title: 'survey_cooler_stored',
    multiple: false,
  },
  [ImageTitle.areaWhereSamplesAreHandledPhotos]: {
    title: 'survey_samples_handled',
    multiple: false,
  },
  [ImageTitle.dropOffSignature]: {
    title: 'dropoff_signature',
    multiple: false,
  },
  [ImageTitle.thermometerPhoto]: {
    title: 'lockbox_thermometer_photo',
    multiple: false,
  },
  [ImageTitle.lockboxPhoto]: {
    title: 'lockbox_photo',
    multiple: false,
  },
  [ImageTitle.parcelBarcodeBypassPhoto]: {
    title: 'parcel_barcode_bypass_photo',
    multiple: true,
  },
} as const;

export const filterValidImageUris = (images: any[]): string[] =>
  images
    .filter(img => img?.uri && typeof img.uri === 'string')
    .map(img => img.uri);

export {
  compressImageFromUri,
  removeFilesInBatch,
  compressImageAndConvertToBase64,
  deleteCachedImages,
};
