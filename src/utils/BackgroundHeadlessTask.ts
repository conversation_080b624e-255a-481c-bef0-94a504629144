import BackgroundGeolocation from 'react-native-background-geolocation';
import { postLocation } from '~/services/location/LocationService';

type BackgroundGeolocationEvent = {
  name: 'terminate' | 'heartbeat';
  [key: string]: any;
};
/**
 * Background Geolocation Headless Task
 * @param event - The event triggered by BackgroundGeolocation.
 */
const BGHeadlessTask = async (event: BackgroundGeolocationEvent) => {
  try {
    switch (event.name) {
      case 'terminate':
        console.info('[BackgroundGeolocation HeadlessTask] - App terminated');
        break;
      case 'heartbeat': {
        const location = await BackgroundGeolocation.getCurrentPosition({
          samples: 1,
          extras: {
            headless: true,
            event: 'headless',
          },
          persist: true,
        });
        console.info(
          '[BackgroundGeolocation HeadlessTask] - getCurrentPosition:',
          location,
        );
        await postLocation(location, false, { event: 'headless' });
        break;
      }
      default:
        console.warn(
          '[BackgroundGeolocation HeadlessTask] - Unknown event:',
          event.name,
        );
        break;
    }
  } catch (error) {
    console.error('[BackgroundGeolocation HeadlessTask] - Error:', error);
  }
};

export default BGHeadlessTask;
