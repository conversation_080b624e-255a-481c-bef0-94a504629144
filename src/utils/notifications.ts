import { NotificationType } from '~/types/notifications.types';
import { TWO_MINUTES_IN_MS } from '~/utils/constants';

export const NotificationHandleInstructions: Record<
  NotificationType,
  {
    title: string;
    message: string;
    shouldRefresh: boolean;
    dataRequired: string[];
    showMultipleNotifications: boolean;
    maxNotifications: number | null;
    debounceTimeInMs: number | null;
  }
> = {
  [NotificationType.stopAdded]: {
    title: 'New Stop Added',
    message:
      'A new stop has been added to your route. Open the app to review your updated schedule.',
    shouldRefresh: true,
    dataRequired: ['postDate', 'dailyScheduleId', 'stopId'],
    showMultipleNotifications: false,
    maxNotifications: 1,
    debounceTimeInMs: TWO_MINUTES_IN_MS,
  },
  [NotificationType.stopRemoved]: {
    title: 'Stop removed',
    message:
      'A stop has been removed from your route. Open the app to review your updated schedule.',
    shouldRefresh: false,
    dataRequired: ['stopId'],
    showMultipleNotifications: true,
    maxNotifications: null,
    debounceTimeInMs: null,
  },
  [NotificationType.shipmentPickupAdded]: {
    title: 'Shipment pickup added',
    message:
      'A shipment pickup has been added to your route. Open the app to review your updated schedule.',
    shouldRefresh: true,
    dataRequired: ['postDate', 'dailyScheduleId', 'stopId'],
    showMultipleNotifications: false,
    maxNotifications: 1,
    debounceTimeInMs: TWO_MINUTES_IN_MS,
  },
  [NotificationType.autoSyncUp]: {
    title: 'Auto sync up',
    message:
      'A new sync has been completed. Open the app to review your updated schedule.',
    shouldRefresh: true,
    dataRequired: [],
    showMultipleNotifications: false,
    maxNotifications: null,
    debounceTimeInMs: null,
  },
  [NotificationType.newWorkOrderAssigned]: {
    title: 'New work order assigned',
    message:
      'A new work order stop has been added to your route. Please tap to confirm within 2 minutes',
    shouldRefresh: false,
    dataRequired: ['workOrderId', 'timeAssigned'],
    showMultipleNotifications: true,
    maxNotifications: null,
    debounceTimeInMs: null,
  },
};
