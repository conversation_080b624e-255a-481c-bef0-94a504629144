import { customOperators } from '~/types/customOperators.types';
import {
  calculateDistanceBetweenCoordinatesInMeters,
  isValidCoordinate,
} from '~/utils/location';
import { resolveValue } from '~/services/protocol/ProtocolConditionEvaluatorService';

// Mock dependencies
jest.mock('~/utils/location', () => ({
  calculateDistanceBetweenCoordinatesInMeters: jest.fn(),
  isValidCoordinate: jest.fn(),
}));

jest.mock('~/services/protocol/ProtocolConditionEvaluatorService', () => ({
  resolveValue: jest.fn(),
}));

const mockCalculateDistance =
  calculateDistanceBetweenCoordinatesInMeters as jest.MockedFunction<
    typeof calculateDistanceBetweenCoordinatesInMeters
  >;
const mockIsValidCoordinate = isValidCoordinate as jest.MockedFunction<
  typeof isValidCoordinate
>;
const mockResolveValue = resolveValue as jest.MockedFunction<
  typeof resolveValue
>;

describe('customOperators', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    console.warn = jest.fn();
  });

  describe('isWithinGeofence', () => {
    const validReference = { lat: 40.7128, lon: -74.006 };
    const validTarget = { lat: 40.7129, lon: -74.0061 };

    it('returns true when target is within radius', () => {
      mockIsValidCoordinate.mockReturnValue(true);
      mockCalculateDistance.mockReturnValue(50); // 50 meters

      const condition = {
        field: 'location',
        operator: 'custom' as const,
        customOperator: 'isWithinGeofence',
        reference: validReference,
        maxRadius: 100, // 100 meters
      };

      const result = customOperators.isWithinGeofence(condition, validTarget);
      expect(result).toBe(true);
      expect(mockCalculateDistance).toHaveBeenCalledWith(
        40.7128,
        -74.006,
        40.7129,
        -74.0061,
      );
    });

    it('returns false when target is outside radius', () => {
      mockIsValidCoordinate.mockReturnValue(true);
      mockCalculateDistance.mockReturnValue(150); // 150 meters

      const condition = {
        field: 'location',
        operator: 'custom' as const,
        customOperator: 'isWithinGeofence',
        reference: validReference,
        maxRadius: 100, // 100 meters
      };

      const result = customOperators.isWithinGeofence(condition, validTarget);
      expect(result).toBe(false);
    });

    it('returns false when reference coordinate is invalid', () => {
      mockIsValidCoordinate.mockImplementation(coord => {
        return coord === validTarget; // Only target is valid
      });

      const condition = {
        field: 'location',
        operator: 'custom' as const,
        customOperator: 'isWithinGeofence',
        reference: { lat: null, lon: -74.006 },
        maxRadius: 100,
      };

      const result = customOperators.isWithinGeofence(condition, validTarget);
      expect(result).toBe(false);
      expect(console.warn).toHaveBeenCalled();
    });

    it('returns false when target coordinate is invalid', () => {
      mockIsValidCoordinate.mockImplementation(coord => {
        return coord === validReference; // Only reference is valid
      });

      const condition = {
        field: 'location',
        operator: 'custom' as const,
        customOperator: 'isWithinGeofence',
        reference: validReference,
        maxRadius: 100,
      };

      const result = customOperators.isWithinGeofence(condition, {
        lat: null,
        lon: -74.0061,
      });
      expect(result).toBe(false);
      expect(console.warn).toHaveBeenCalled();
    });

    it('returns false when both coordinates are invalid', () => {
      mockIsValidCoordinate.mockReturnValue(false);

      const condition = {
        field: 'location',
        operator: 'custom' as const,
        customOperator: 'isWithinGeofence',
        reference: { lat: null, lon: null },
        maxRadius: 100,
      };

      const result = customOperators.isWithinGeofence(condition, {
        lat: null,
        lon: null,
      });
      expect(result).toBe(false);
      expect(console.warn).toHaveBeenCalled();
    });
  });

  describe('isOutsideGeofence', () => {
    const validTarget = { lat: 40.7129, lon: -74.0061 };
    const mockContext = {
      location: { lat: 40.7128, lon: -74.006 },
      radius: 100,
    };

    it('returns true when target is outside radius', () => {
      mockResolveValue.mockImplementation((context, key) => {
        if (key === 'lat') return 40.7128;
        if (key === 'lon') return -74.006;
        if (key === 'radius') return 100;
        return null;
      });
      mockIsValidCoordinate.mockReturnValue(true);
      mockCalculateDistance.mockReturnValue(150); // 150 meters

      const condition = {
        field: 'location',
        operator: 'custom' as const,
        customOperator: 'isOutsideGeofence',
        reference: {
          lat: 'location.lat',
          lon: 'location.lon',
          radius: 'radius',
        },
      };

      const result = customOperators.isOutsideGeofence(
        condition,
        validTarget,
        mockContext,
      );
      expect(result).toBe(true);
    });

    it('returns false when target is within radius', () => {
      mockResolveValue.mockImplementation((context, key) => {
        if (key === 'lat') return 40.7128;
        if (key === 'lon') return -74.006;
        if (key === 'radius') return 100;
        return null;
      });
      mockIsValidCoordinate.mockReturnValue(true);
      mockCalculateDistance.mockReturnValue(50); // 50 meters

      const condition = {
        field: 'location',
        operator: 'custom' as const,
        customOperator: 'isOutsideGeofence',
        reference: {
          lat: 'location.lat',
          lon: 'location.lon',
          radius: 'radius',
        },
      };

      const result = customOperators.isOutsideGeofence(
        condition,
        validTarget,
        mockContext,
      );
      expect(result).toBe(false);
    });

    it('returns false when context is missing', () => {
      const condition = {
        field: 'location',
        operator: 'custom' as const,
        customOperator: 'isOutsideGeofence',
        reference: {
          lat: 'location.lat',
          lon: 'location.lon',
          radius: 'radius',
        },
      };

      const result = customOperators.isOutsideGeofence(
        condition,
        validTarget,
        undefined,
      );
      expect(result).toBe(false);
      expect(console.warn).toHaveBeenCalledWith(
        '[isOutsideGeofence] Missing context',
      );
    });

    it('returns false when coordinates are invalid', () => {
      mockResolveValue.mockReturnValue(null);
      mockIsValidCoordinate.mockReturnValue(false);

      const condition = {
        field: 'location',
        operator: 'custom' as const,
        customOperator: 'isOutsideGeofence',
        reference: { lat: 'invalid.lat', lon: 'invalid.lon', radius: 'radius' },
      };

      const result = customOperators.isOutsideGeofence(
        condition,
        validTarget,
        mockContext,
      );
      expect(result).toBe(false);
      expect(console.warn).toHaveBeenCalled();
    });
  });

  describe('isWithinTimeSince', () => {
    const now = new Date('2023-01-01T12:00:00Z');
    const fiveMinutesAgo = new Date('2023-01-01T11:55:00Z').toISOString();
    const tenMinutesAgo = new Date('2023-01-01T11:50:00Z').toISOString();

    beforeEach(() => {
      jest.useFakeTimers();
      jest.setSystemTime(now);
    });

    afterEach(() => {
      jest.useRealTimers();
    });

    it('returns true when time is within threshold', () => {
      const condition = {
        field: 'completedTime',
        operator: 'custom' as const,
        customOperator: 'isWithinTimeSince',
        value: 300, // 5 minutes in seconds
      };

      const result = customOperators.isWithinTimeSince(
        condition,
        fiveMinutesAgo,
      );
      expect(result).toBe(true);
    });

    it('returns false when time is outside threshold', () => {
      const condition = {
        field: 'completedTime',
        operator: 'custom' as const,
        customOperator: 'isWithinTimeSince',
        value: 300, // 5 minutes in seconds
      };

      const result = customOperators.isWithinTimeSince(
        condition,
        tenMinutesAgo,
      );
      expect(result).toBe(false);
    });

    it('returns false when time data is null', () => {
      const condition = {
        field: 'completedTime',
        operator: 'custom' as const,
        customOperator: 'isWithinTimeSince',
        value: 300,
      };

      const result = customOperators.isWithinTimeSince(condition, null);
      expect(result).toBe(false);
      expect(console.warn).toHaveBeenCalled();
    });

    it('returns false when time data is undefined', () => {
      const condition = {
        field: 'completedTime',
        operator: 'custom' as const,
        customOperator: 'isWithinTimeSince',
        value: 300,
      };

      const result = customOperators.isWithinTimeSince(condition, undefined);
      expect(result).toBe(false);
      expect(console.warn).toHaveBeenCalled();
    });

    it('returns false when time data is not a string', () => {
      const condition = {
        field: 'completedTime',
        operator: 'custom' as const,
        customOperator: 'isWithinTimeSince',
        value: 300,
      };

      const result = customOperators.isWithinTimeSince(condition, 123);
      expect(result).toBe(false);
      expect(console.warn).toHaveBeenCalled();
    });

    it('returns false when threshold is not a number', () => {
      const condition = {
        field: 'completedTime',
        operator: 'custom' as const,
        customOperator: 'isWithinTimeSince',
        value: 'invalid',
      };

      const result = customOperators.isWithinTimeSince(
        condition,
        fiveMinutesAgo,
      );
      expect(result).toBe(false);
      expect(console.warn).toHaveBeenCalled();
    });

    it('returns false when date format is invalid', () => {
      const condition = {
        field: 'completedTime',
        operator: 'custom' as const,
        customOperator: 'isWithinTimeSince',
        value: 300,
      };

      const result = customOperators.isWithinTimeSince(
        condition,
        'invalid-date',
      );
      expect(result).toBe(false);
      expect(console.warn).toHaveBeenCalled();
    });

    it('handles exact threshold boundary', () => {
      const condition = {
        field: 'completedTime',
        operator: 'custom' as const,
        customOperator: 'isWithinTimeSince',
        value: 300, // 5 minutes in seconds
      };

      const result = customOperators.isWithinTimeSince(
        condition,
        fiveMinutesAgo,
      );
      expect(result).toBe(true);
    });
  });

  describe('includes', () => {
    it('returns true when array includes the value', () => {
      const condition = {
        field: 'items',
        operator: 'custom' as const,
        customOperator: 'includes',
        value: 'apple',
      };

      const result = customOperators.includes(condition, [
        'banana',
        'apple',
        'orange',
      ]);
      expect(result).toBe(true);
    });

    it('returns false when array does not include the value', () => {
      const condition = {
        field: 'items',
        operator: 'custom' as const,
        customOperator: 'includes',
        value: 'grape',
      };

      const result = customOperators.includes(condition, [
        'banana',
        'apple',
        'orange',
      ]);
      expect(result).toBe(false);
    });

    it('returns false when data is null', () => {
      const condition = {
        field: 'items',
        operator: 'custom' as const,
        customOperator: 'includes',
        value: 'apple',
      };

      const result = customOperators.includes(condition, null);
      expect(result).toBe(false);
    });

    it('returns false when data is undefined', () => {
      const condition = {
        field: 'items',
        operator: 'custom' as const,
        customOperator: 'includes',
        value: 'apple',
      };

      const result = customOperators.includes(condition, undefined);
      expect(result).toBe(false);
    });

    it('returns false when data is not an array', () => {
      const condition = {
        field: 'items',
        operator: 'custom' as const,
        customOperator: 'includes',
        value: 'apple',
      };

      const result = customOperators.includes(condition, 'not-an-array');
      expect(result).toBe(false);
    });

    it('returns false when data is an empty array', () => {
      const condition = {
        field: 'items',
        operator: 'custom' as const,
        customOperator: 'includes',
        value: 'apple',
      };

      const result = customOperators.includes(condition, []);
      expect(result).toBe(false);
    });

    it('handles numeric values', () => {
      const condition = {
        field: 'numbers',
        operator: 'custom' as const,
        customOperator: 'includes',
        value: 42,
      };

      const result = customOperators.includes(condition, [1, 42, 100]);
      expect(result).toBe(true);
    });

    it('handles boolean values', () => {
      const condition = {
        field: 'flags',
        operator: 'custom' as const,
        customOperator: 'includes',
        value: true,
      };

      const result = customOperators.includes(condition, [false, true, false]);
      expect(result).toBe(true);
    });
  });
});
