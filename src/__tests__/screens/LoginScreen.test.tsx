import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import LoginScreen from '~/screens/auth/LoginScreen';

jest.mock('~/services/AuthService');
jest.mock('react-native-safe-area-context', () => ({
  useSafeAreaInsets: () => ({ bottom: 0 }),
}));
jest.mock('~/assets/logo.svg', () => 'Logo');
jest.mock('~/services/AuthService', () => ({
  AuthService: {
    login: jest.fn(),
  },
}));

describe('LoginScreen', () => {
  const mockNavigation = {
    replace: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render correctly', () => {
    const { getByText, getByPlaceholderText } = render(
      <LoginScreen navigation={mockNavigation} />,
    );

    expect(getByPlaceholderText('Email address')).toBeTruthy();
    expect(getByPlaceholderText('Password')).toBeTruthy();
    expect(getByText('Log in')).toBeTruthy();
  });

  it('should validate email format', () => {
    const { getByTestId, queryByText } = render(
      <LoginScreen navigation={mockNavigation} />,
    );
    const emailInput = getByTestId('LoginScreen.InputField.email');
    fireEvent.changeText(emailInput, 'invalid-email');
    expect(queryByText('Enter a valid email address')).toBeTruthy();
  });

  it('should disable login button when form is invalid', () => {
    const { getByTestId } = render(<LoginScreen navigation={mockNavigation} />);
    const loginButton = getByTestId('LoginScreen.FilledButton.login');
    expect(loginButton.props.accessibilityState.disabled).toBeTruthy();
  });

  it('should enable login button with valid credentials', () => {
    const { getByPlaceholderText, getByTestId } = render(
      <LoginScreen navigation={mockNavigation} />,
    );

    const emailInput = getByPlaceholderText('Email address');
    const passwordInput = getByPlaceholderText('Password');
    fireEvent.changeText(emailInput, '<EMAIL>');
    fireEvent.changeText(passwordInput, 'password123');
    const loginButton = getByTestId('LoginScreen.FilledButton.login');
    expect(loginButton.props.isDisabled).toBeFalsy();
  });

  it('should toggle password visibility', () => {
    const { getByTestId } = render(<LoginScreen navigation={mockNavigation} />);
    const passwordInput = getByTestId('LoginScreen.InputField.password');
    expect(passwordInput.props.secureTextEntry).toBeTruthy();
    const eyeButton = getByTestId('LoginScreen.TouchableWithoutFeedback.eye');
    fireEvent.press(eyeButton);
    expect(passwordInput.props.secureTextEntry).toBeFalsy();
  });
});
