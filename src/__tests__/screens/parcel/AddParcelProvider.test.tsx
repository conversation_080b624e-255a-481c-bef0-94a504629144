import React from 'react';
import { render, act } from '@testing-library/react-native';
import {
  AddParcelProvider,
  useAddParcelContext,
} from '~/screens/parcel/AddParcelProvider';
import { ParcelManager } from '~/services/sync/parcelManager';
import { SORTING_OPTIONS } from '~/components/modals/SortModal';
import { Stop, StopType } from '~/types/stops.types';
import { ParcelType } from '~/types/parcel.types';

// Mock ParcelManager
jest.mock('~/services/sync/parcelManager', () => ({
  ParcelManager: {
    createParcel: jest.fn(),
  },
}));

// Create proper mock data with required properties
const mockStop: Stop = {
  attributes: { type: 'Stop', url: '/test' },
  LastModifiedDate: '2023-01-01',
  Id: 'stop1',
  Name: 'Test Stop',
  Stop_Time_Min__c: '10',
  Stop_Time_Preferred__c: '15',
  Stop_Time_Max__c: '20',
  ETA__c: '2023-01-01T10:00:00Z',
  Type__c: StopType.Pickup,
  Address__c: '123 Test St',
  Address_1__c: '123 Test St',
  Address_2__c: null,
  City__c: 'Test City',
  Postal_Code__c: '12345',
  State__c: 'TS',
  Status__c: 'SCHEDULED',
  Post_Date__c: '2023-01-01',
  Stop_Coordinates__Latitude__s: 40.7128,
  Stop_Coordinates__Longitude__s: -74.006,
  Notes__c: null,
  Notes_Other__c: null,
  POD__c: null,
  POD_Comments__c: null,
  Customer_Reference_1__c: null,
  Customer_Reference_2__c: null,
  Pieces__c: 1,
  Summary__c: 'Test summary',
  Arrival_Time__c: null,
  Completed_Time__c: null,
  Geofencing_Distance__c: null,
  Location__r: {
    attributes: { type: 'Location', url: '/test' },
    Arrival_Verification_Method__c: null,
    Name: 'Test Location',
    Coordinates__Latitude__s: 40.7128,
    Coordinates__Longitude__s: -74.006,
    Driver_Wait_Time_Duration__c: null,
    Geofencing_Distance__c: null,
    Business_Hours__c: null,
  },
  Coordinates__c: '40.7128,-74.0060',
  Wait_Time_Minutes__c: null,
  SNO_Bypass_DateTime__c: null,
  SNO_Driver_Waiting__c: false,
  SNO_Tags__c: null,
  No_Perimeter_Check_Notes__c: null,
  Pickup_Contact__c: null,
  Pickup_Comments__c: null,
  Delivery_Contact__c: null,
  Delivery_Comments__c: null,
  Driver_Initiated_Early_Departure__c: false,
  Service_Comments__c: '',
  Proof_of_Service__c: null,
};

const mockStopList: Stop[] = [
  mockStop,
  {
    ...mockStop,
    Id: 'stop2',
    Name: 'Test Stop 2',
  },
];

const mockStopType: StopType = StopType.Pickup;

const mockParcelTypes: ParcelType[] = [
  {
    Id: 'type1',
    Name: 'Box',
  },
  {
    Id: 'type2',
    Name: 'Envelope',
  },
];

function renderWithProvider(children: React.ReactNode) {
  return render(
    <AddParcelProvider
      stop={mockStop}
      stopList={mockStopList}
      stopType={mockStopType}
      parcelTypes={mockParcelTypes}>
      {children}
    </AddParcelProvider>,
  );
}

// Define the context type locally since it's not exported
type AddParcelContextType = {
  stop: Stop;
  stopList: Stop[];
  stopType: StopType;
  parcelData: {
    quantity: number;
    type?: ParcelType;
    dropoffDestination?: string;
    isSignatureRequired?: boolean;
    comments?: string;
    posPhoto?: string;
    barcodeScannerResult?: string;
  };
  parcelTypes: ParcelType[];
  selectedSortingOption: string;
  setSelectedSortingOption: (option: string) => void;
  setParcelType: (type: ParcelType) => void;
  setParcelQuantity: (quantity: number) => void;
  setDropoffDestination: (destination: string) => void;
  setIsSignatureRequired: (required: boolean) => void;
  setComments: (comments: string) => void;
  setPosPhoto: (photo: string) => void;
  setBarcodeResult: (barcode: string) => void;
  saveParcel: () => Promise<boolean>;
  isContinueButtonEnabled: (screen: 'first' | 'second' | 'third') => boolean;
};

// Test component that uses the context
function TestComponent({
  callback,
}: {
  callback: (ctx: AddParcelContextType) => void;
}) {
  const ctx = useAddParcelContext();
  callback(ctx);
  return null;
}

describe('AddParcelProvider', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();
    // Suppress console.error for expected error cases
    jest.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.runOnlyPendingTimers();
    jest.clearAllTimers();
    jest.useRealTimers();
    jest.restoreAllMocks();
  });

  it('provides context with default values', () => {
    let context: AddParcelContextType | undefined;
    renderWithProvider(
      <TestComponent
        callback={(ctx: AddParcelContextType) => {
          context = ctx;
        }}
      />,
    );

    expect(context).toBeDefined();
    expect(context!.stop).toEqual(mockStop);
    expect(context!.stopList).toEqual(mockStopList);
    expect(context!.stopType).toEqual(mockStopType);
    expect(context!.parcelTypes).toEqual(mockParcelTypes);
    expect(context!.parcelData.quantity).toBe(1);
    expect(context!.selectedSortingOption).toBe(
      SORTING_OPTIONS.NEXT_AVAILABLE_STOPS,
    );
    expect(typeof context!.setParcelType).toBe('function');
    expect(typeof context!.setParcelQuantity).toBe('function');
    expect(typeof context!.setDropoffDestination).toBe('function');
    expect(typeof context!.setIsSignatureRequired).toBe('function');
    expect(typeof context!.setComments).toBe('function');
    expect(typeof context!.setPosPhoto).toBe('function');
    expect(typeof context!.setBarcodeResult).toBe('function');
    expect(typeof context!.saveParcel).toBe('function');
    expect(typeof context!.isContinueButtonEnabled).toBe('function');
  });

  it('updates parcel type', () => {
    let context: AddParcelContextType | undefined;
    renderWithProvider(
      <TestComponent
        callback={(ctx: AddParcelContextType) => {
          context = ctx;
        }}
      />,
    );
    act(() => {
      context!.setParcelType(mockParcelTypes[1]);
    });
    expect(context!.parcelData.type).toEqual(mockParcelTypes[1]);
  });

  it('updates parcel quantity', () => {
    let context: AddParcelContextType | undefined;
    renderWithProvider(
      <TestComponent
        callback={(ctx: AddParcelContextType) => {
          context = ctx;
        }}
      />,
    );
    act(() => {
      context!.setParcelQuantity(5);
    });
    expect(context!.parcelData.quantity).toBe(5);
  });

  it('updates dropoff destination', () => {
    let context: AddParcelContextType | undefined;
    renderWithProvider(
      <TestComponent
        callback={(ctx: AddParcelContextType) => {
          context = ctx;
        }}
      />,
    );
    act(() => {
      context!.setDropoffDestination('stop2');
    });
    expect(context!.parcelData.dropoffDestination).toBe('stop2');
  });

  it('updates signature required', () => {
    let context: AddParcelContextType | undefined;
    renderWithProvider(
      <TestComponent
        callback={(ctx: AddParcelContextType) => {
          context = ctx;
        }}
      />,
    );
    act(() => {
      context!.setIsSignatureRequired(true);
    });
    expect(context!.parcelData.isSignatureRequired).toBe(true);
  });

  it('updates comments', () => {
    let context: AddParcelContextType | undefined;
    renderWithProvider(
      <TestComponent
        callback={(ctx: AddParcelContextType) => {
          context = ctx;
        }}
      />,
    );
    act(() => {
      context!.setComments('fragile');
    });
    expect(context!.parcelData.comments).toBe('fragile');
  });

  it('updates posPhoto', () => {
    let context: AddParcelContextType | undefined;
    renderWithProvider(
      <TestComponent
        callback={(ctx: AddParcelContextType) => {
          context = ctx;
        }}
      />,
    );
    act(() => {
      context!.setPosPhoto('photo.jpg');
    });
    expect(context!.parcelData.posPhoto).toBe('photo.jpg');
  });

  it('updates barcode result', () => {
    let context: AddParcelContextType | undefined;
    renderWithProvider(
      <TestComponent
        callback={(ctx: AddParcelContextType) => {
          context = ctx;
        }}
      />,
    );
    act(() => {
      context!.setBarcodeResult('barcode123');
    });
    expect(context!.parcelData.barcodeScannerResult).toBe('barcode123');
  });

  it('sets selected sorting option', () => {
    let context: AddParcelContextType | undefined;
    renderWithProvider(
      <TestComponent
        callback={(ctx: AddParcelContextType) => {
          context = ctx;
        }}
      />,
    );
    act(() => {
      context!.setSelectedSortingOption(SORTING_OPTIONS.ASCENDING);
    });
    expect(context!.selectedSortingOption).toBe(SORTING_OPTIONS.ASCENDING);
  });

  it('saveParcel returns true on success', async () => {
    const mockCreateParcel = ParcelManager.createParcel as jest.MockedFunction<
      typeof ParcelManager.createParcel
    >;
    mockCreateParcel.mockResolvedValueOnce({
      success: true,
      message: 'Parcel saved',
    });

    let context: AddParcelContextType | undefined;
    renderWithProvider(
      <TestComponent
        callback={(ctx: AddParcelContextType) => {
          context = ctx;
        }}
      />,
    );
    let result: boolean | undefined;
    await act(async () => {
      result = await context!.saveParcel();
    });
    expect(ParcelManager.createParcel).toHaveBeenCalled();
    expect(result).toBe(true);
  });

  it('saveParcel returns false on error', async () => {
    const mockCreateParcel = ParcelManager.createParcel as jest.MockedFunction<
      typeof ParcelManager.createParcel
    >;
    mockCreateParcel.mockRejectedValueOnce(new Error('fail'));

    let context: AddParcelContextType | undefined;
    renderWithProvider(
      <TestComponent
        callback={(ctx: AddParcelContextType) => {
          context = ctx;
        }}
      />,
    );
    let result: boolean | undefined;
    await act(async () => {
      result = await context!.saveParcel();
    });
    expect(ParcelManager.createParcel).toHaveBeenCalled();
    expect(result).toBe(false);
  });

  describe('isContinueButtonEnabled', () => {
    it('returns true for first screen if type and quantity set', () => {
      let context: AddParcelContextType | undefined;
      renderWithProvider(
        <TestComponent
          callback={(ctx: AddParcelContextType) => {
            context = ctx;
          }}
        />,
      );
      act(() => {
        context!.setParcelType(mockParcelTypes[0]);
        context!.setParcelQuantity(2);
      });
      expect(context!.isContinueButtonEnabled('first')).toBe(true);
    });

    it('returns false for first screen if type or quantity missing', () => {
      let context: AddParcelContextType | undefined;
      renderWithProvider(
        <TestComponent
          callback={(ctx: AddParcelContextType) => {
            context = ctx;
          }}
        />,
      );
      act(() => {
        // Don't set parcel type to test undefined case
        context!.setParcelQuantity(0);
      });
      expect(context!.isContinueButtonEnabled('first')).toBe(false);
    });

    it('returns true for second screen if dropoff and signature set', () => {
      let context: AddParcelContextType | undefined;
      renderWithProvider(
        <TestComponent
          callback={(ctx: AddParcelContextType) => {
            context = ctx;
          }}
        />,
      );
      act(() => {
        context!.setDropoffDestination('stop2');
        context!.setIsSignatureRequired(false);
      });
      expect(context!.isContinueButtonEnabled('second')).toBe(true);
    });

    it('returns false for second screen if dropoff or signature missing', () => {
      let context: AddParcelContextType | undefined;
      renderWithProvider(
        <TestComponent
          callback={(ctx: AddParcelContextType) => {
            context = ctx;
          }}
        />,
      );
      act(() => {
        // Don't set dropoff destination to test undefined case
        context!.setIsSignatureRequired(true);
      });
      expect(context!.isContinueButtonEnabled('second')).toBe(false);
    });

    it('returns true for third screen if posPhoto or barcode set', () => {
      let context: AddParcelContextType | undefined;
      renderWithProvider(
        <TestComponent
          callback={(ctx: AddParcelContextType) => {
            context = ctx;
          }}
        />,
      );
      act(() => {
        context!.setPosPhoto('photo.jpg');
      });
      expect(context!.isContinueButtonEnabled('third')).toBe(true);
      act(() => {
        context!.setPosPhoto('');
        context!.setBarcodeResult('barcode123');
      });
      expect(context!.isContinueButtonEnabled('third')).toBe(true);
    });

    it('returns false for third screen if neither posPhoto nor barcode set', () => {
      let context: AddParcelContextType | undefined;
      renderWithProvider(
        <TestComponent
          callback={(ctx: AddParcelContextType) => {
            context = ctx;
          }}
        />,
      );
      act(() => {
        context!.setPosPhoto('');
        context!.setBarcodeResult('');
      });
      expect(context!.isContinueButtonEnabled('third')).toBe(false);
    });
  });

  it('throws if useAddParcelContext is used outside provider', () => {
    const Test = () => {
      useAddParcelContext();
      return null;
    };
    expect(() => render(<Test />)).toThrow(
      'useAddParcelContext must be used within an AddParcelProvider',
    );
  });
});
