import * as driverActions from '~/services/sync/driverActions';

jest.mock('~/db/realm/utils/realm.config', () => ({
  getRealmInstance: jest.fn(),
}));
jest.mock('~/db/realm/operations', () => ({
  updateEntity: jest.fn(),
}));
jest.mock('~/services/sync/syncUp', () => ({
  addToRequestQueue: jest.fn(),
}));
jest.mock('~/utils/strings', () => ({
  interpolateString: jest.fn(
    (str, values) => `${str}:${JSON.stringify(values)}`,
  ),
}));
jest.mock('react-native-uuid', () => ({
  v4: jest.fn(() => 'mock-uuid'),
}));

const en = {
  stop_not_found: 'Stop not found',
  already_marked_arrived: 'Already marked arrived',
  already_marked_completed: 'Already marked completed',
  must_arrive_first: 'Must arrive first',
  stop_update_failed: 'Stop update failed',
  stop_failed_to_queue: 'Stop failed to queue',
  stop_updated: 'Stop updated',
  route_not_found: 'Route not found',
  route_updated: 'Route updated',
  no_parcels_picked_at_stop: 'No parcels picked at stop',
  parcels_created: 'Parcels created',
  parcel_create_failed: 'Parcel create failed',
};
jest.mock('~/localization/en', () => en);

const getRealmInstance =
  require('~/db/realm/utils/realm.config').getRealmInstance;
const updateEntity = require('~/db/realm/operations').updateEntity;
const addToRequestQueue = require('~/services/sync/syncUp').addToRequestQueue;

function mockRealm(overrides = {}) {
  return {
    objectForPrimaryKey: jest.fn(),
    objects: jest.fn(() => ({ filtered: jest.fn(() => []) })),
    beginTransaction: jest.fn(),
    commitTransaction: jest.fn(),
    create: jest.fn(),
    ...overrides,
  };
}

describe('driverActions', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('validateTime', () => {
    it('returns false for null/undefined/empty', () => {
      expect(driverActions.validateTime()).toBe(false);
      expect(driverActions.validateTime(null)).toBe(false);
      expect(driverActions.validateTime('')).toBe(false);
    });
    it('returns true if dateString is in the past', () => {
      const past = new Date(Date.now() - 1000).toISOString();
      expect(driverActions.validateTime(past)).toBe(true);
    });
    it('returns false if dateString is in the future', () => {
      const future = new Date(Date.now() + 100000).toISOString();
      expect(driverActions.validateTime(future)).toBe(false);
    });
    it('returns true if dateString is now', () => {
      const now = new Date().toISOString();
      expect(driverActions.validateTime(now)).toBe(true);
    });
  });

  describe('markStopArrival', () => {
    it('returns error if stop not found', async () => {
      const realm = mockRealm({ objectForPrimaryKey: jest.fn(() => null) });
      getRealmInstance.mockResolvedValue(realm);
      const result = await driverActions.DriverActions.markStopArrival('stop1');
      expect(result).toEqual({ success: false, message: en.stop_not_found });
    });
    it('returns error if already arrived', async () => {
      const stop = { Arrival_Time__c: new Date().toISOString() };
      const realm = mockRealm({ objectForPrimaryKey: jest.fn(() => stop) });
      getRealmInstance.mockResolvedValue(realm);
      const result = await driverActions.DriverActions.markStopArrival('stop1');
      expect(result).toEqual({
        success: false,
        message: en.already_marked_arrived,
      });
    });
    it('calls updateStopAndQueue and returns success', async () => {
      const stop = { Id: 'stop1', Arrival_Time__c: null };
      const realm = mockRealm({ objectForPrimaryKey: jest.fn(() => stop) });
      getRealmInstance.mockResolvedValue(realm);
      updateEntity.mockResolvedValue(true);
      addToRequestQueue.mockResolvedValue(true);
      const result = await driverActions.DriverActions.markStopArrival('stop1');
      expect(result.success).toBe(true);
      expect(updateEntity).toHaveBeenCalled();
      expect(addToRequestQueue).toHaveBeenCalled();
    });
  });

  describe('markStopCompletion', () => {
    it('returns error if stop not found', async () => {
      const realm = mockRealm({ objectForPrimaryKey: jest.fn(() => null) });
      getRealmInstance.mockResolvedValue(realm);
      const result = await driverActions.DriverActions.markStopCompletion(
        'stop1',
        {},
      );
      expect(result).toEqual({ success: false, message: en.stop_not_found });
    });
    it('returns error if not arrived yet', async () => {
      const stop = { Arrival_Time__c: null };
      const realm = mockRealm({ objectForPrimaryKey: jest.fn(() => stop) });
      getRealmInstance.mockResolvedValue(realm);
      const result = await driverActions.DriverActions.markStopCompletion(
        'stop1',
        {},
      );
      expect(result).toEqual({ success: false, message: en.must_arrive_first });
    });
    it('returns error if already completed', async () => {
      const stop = {
        Arrival_Time__c: new Date().toISOString(),
        Completed_Time__c: new Date().toISOString(),
      };
      const realm = mockRealm({ objectForPrimaryKey: jest.fn(() => stop) });
      getRealmInstance.mockResolvedValue(realm);
      const result = await driverActions.DriverActions.markStopCompletion(
        'stop1',
        {},
      );
      expect(result).toEqual({
        success: false,
        message: en.already_marked_completed,
      });
    });
    it('calls updateStopAndQueue and returns success', async () => {
      const stop = {
        Id: 'stop1',
        Arrival_Time__c: new Date().toISOString(),
        Completed_Time__c: null,
        Summary__c: 'route1',
      };
      const realm = mockRealm({ objectForPrimaryKey: jest.fn(() => stop) });
      getRealmInstance.mockResolvedValue(realm);
      updateEntity.mockResolvedValue(true);
      addToRequestQueue.mockResolvedValue(true);
      const result = await driverActions.DriverActions.markStopCompletion(
        'stop1',
        { foo: 'bar' },
      );
      expect(result.success).toBe(true);
      expect(updateEntity).toHaveBeenCalled();
      expect(addToRequestQueue).toHaveBeenCalled();
    });
  });

  describe('markRouteCompleted', () => {
    it('returns error if route not found', async () => {
      const realm = mockRealm({ objectForPrimaryKey: jest.fn(() => null) });
      getRealmInstance.mockResolvedValue(realm);
      updateEntity.mockResolvedValue(true);
      const result = await driverActions.markRouteCompleted({
        attributes: { type: '', url: '' },
        LastModifiedDate: '',
        Id: 'route1',
        Name: '',
        Planned_Start__c: null,
        Planned_End__c: null,
        Date__c: '',
        Start_Time__c: null,
        End_Time__c: null,
        Miles_Driven__c: 0,
        Number_of_Open_Stops__c: null,
        Number_of_Stops__c: 0,
        Number_of_Stops_Completed_Late__c: null,
        Number_of_Stops_Completed_Early__c: 0,
        Geofencing_Distance__c: null,
        Status__c: '',
        Service_Status__c: null,
        Route__r: {
          attributes: { type: '', url: '' },
          Name: '',
          Timezone__c: 0,
          Geofencing_Distance__c: null,
        },
        UTC_Offset__c: null,
        Driver__c: null,
        SNO_Flow__c: true,
        Lock_Check_In_Order__c: false,
        Rapid_Closure_Warning__c: false,
        Hours_Run__c: 0,
        Default_Delivery_Destination__c: null,
        Survey_Complete__c: null,
        Require_End_of_Route_Survey__c: null,
      });
      expect(result).toEqual({ success: false, message: en.route_not_found });
    });
    it('returns success if route updated', async () => {
      const route = {
        attributes: { type: '', url: '' },
        LastModifiedDate: '',
        Id: 'route1',
        Name: '',
        Planned_Start__c: null,
        Planned_End__c: null,
        Date__c: '',
        Start_Time__c: null,
        End_Time__c: null,
        Miles_Driven__c: 0,
        Number_of_Open_Stops__c: null,
        Number_of_Stops__c: 0,
        Number_of_Stops_Completed_Late__c: null,
        Number_of_Stops_Completed_Early__c: 0,
        Geofencing_Distance__c: null,
        Status__c: 'Ready',
        Service_Status__c: null,
        Route__r: {
          attributes: { type: '', url: '' },
          Name: '',
          Timezone__c: 0,
          Geofencing_Distance__c: null,
        },
        UTC_Offset__c: null,
        Driver__c: null,
        SNO_Flow__c: true,
        Lock_Check_In_Order__c: false,
        Rapid_Closure_Warning__c: false,
        Hours_Run__c: 0,
        Default_Delivery_Destination__c: null,
        Survey_Complete__c: null,
        Require_End_of_Route_Survey__c: null,
      };
      const realm = mockRealm({ objectForPrimaryKey: jest.fn(() => route) });
      getRealmInstance.mockResolvedValue(realm);
      updateEntity.mockResolvedValue(true);
      const result = await driverActions.markRouteCompleted(route);
      expect(result.success).toBe(true);
      expect(updateEntity).toHaveBeenCalled();
    });
    it('returns error if updateEntity fails', async () => {
      const route = {
        attributes: { type: '', url: '' },
        LastModifiedDate: '',
        Id: 'route1',
        Name: '',
        Planned_Start__c: null,
        Planned_End__c: null,
        Date__c: '',
        Start_Time__c: null,
        End_Time__c: null,
        Miles_Driven__c: 0,
        Number_of_Open_Stops__c: null,
        Number_of_Stops__c: 0,
        Number_of_Stops_Completed_Late__c: null,
        Number_of_Stops_Completed_Early__c: 0,
        Geofencing_Distance__c: null,
        Status__c: '',
        Service_Status__c: null,
        Route__r: {
          attributes: { type: '', url: '' },
          Name: '',
          Timezone__c: 0,
          Geofencing_Distance__c: null,
        },
        UTC_Offset__c: null,
        Driver__c: null,
        SNO_Flow__c: true,
        Lock_Check_In_Order__c: false,
        Rapid_Closure_Warning__c: false,
        Hours_Run__c: 0,
        Default_Delivery_Destination__c: null,
        Survey_Complete__c: null,
        Require_End_of_Route_Survey__c: null,
      };
      const realm = mockRealm({ objectForPrimaryKey: jest.fn(() => route) });
      getRealmInstance.mockResolvedValue(realm);
      updateEntity.mockResolvedValue(false);
      const result = await driverActions.markRouteCompleted(route);
      expect(result).toEqual({
        success: false,
        message: 'route_update_failed',
      });
    });
  });
});
