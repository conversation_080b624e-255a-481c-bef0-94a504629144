import { callPhoneNumber } from '~/services/dispatch/CallService';
import { Platform, Linking, Alert } from 'react-native';

describe('callPhoneNumber', () => {
  const originalPlatform = Platform.OS;
  beforeEach(() => {
    jest.clearAllMocks();
  });
  afterEach(() => {
    Object.defineProperty(Platform, 'OS', { value: originalPlatform });
  });

  it('calls Linking.openURL with tel: on android', () => {
    Object.defineProperty(Platform, 'OS', { value: 'android' });
    const openURLMock = jest
      .spyOn(Linking, 'openURL')
      .mockResolvedValueOnce(undefined);
    callPhoneNumber('1234567890');
    expect(openURLMock).toHaveBeenCalledWith('tel:1234567890');
  });

  it('calls Linking.openURL with telprompt: on ios if supported', async () => {
    Object.defineProperty(Platform, 'OS', { value: 'ios' });
    const canOpenURLMock = jest
      .spyOn(Linking, 'canOpenURL')
      .mockResolvedValueOnce(true);
    const openURLMock = jest
      .spyOn(Linking, 'openURL')
      .mockResolvedValueOnce(undefined);
    await callPhoneNumber('1234567890');
    expect(canOpenURLMock).toHaveBeenCalledWith('telprompt:1234567890');
    expect(openURLMock).toHaveBeenCalledWith('telprompt:1234567890');
  });

  it('shows alert if Linking.canOpenURL returns false on ios', async () => {
    Object.defineProperty(Platform, 'OS', { value: 'ios' });
    jest.spyOn(Linking, 'canOpenURL').mockResolvedValueOnce(false);
    const alertMock = jest.spyOn(Alert, 'alert').mockImplementation(() => {});
    await callPhoneNumber('1234567890');
    expect(alertMock).toHaveBeenCalledWith('', 'Phone number not available');
  });

  it('logs error if Linking.canOpenURL throws on ios', async () => {
    Object.defineProperty(Platform, 'OS', { value: 'ios' });
    jest.spyOn(Linking, 'canOpenURL').mockRejectedValueOnce(new Error('fail'));
    const errorMock = jest.spyOn(console, 'error').mockImplementation(() => {});
    await callPhoneNumber('1234567890');
    await new Promise(r => setTimeout(r, 0));
    expect(errorMock).toHaveBeenCalledWith(
      'CallService.ts: callPhoneNumber()',
      expect.any(Error),
    );
  });

  it('throws error on unsupported platform', () => {
    Object.defineProperty(Platform, 'OS', { value: 'windows' });
    expect(() => callPhoneNumber('1234567890')).toThrow(
      'Unsupported platform -1234567890',
    );
  });
});
