import {
  RESULTS,
  checkNotifications,
  requestNotifications,
} from 'react-native-permissions';
import { NotificationPermissionService } from '~/services/permission/NotificationPermissionService';

// Mock react-native-permissions
jest.mock('react-native-permissions', () => ({
  RESULTS: {
    GRANTED: 'granted',
    DENIED: 'denied',
    BLOCKED: 'blocked',
    LIMITED: 'limited',
    UNAVAILABLE: 'unavailable',
  },
  checkNotifications: jest.fn(),
  requestNotifications: jest.fn(),
}));

describe('NotificationPermissionService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    console.error = jest.fn();
  });

  describe('checkPermission', () => {
    it('should return true when permission is granted', async () => {
      (checkNotifications as jest.Mock).mockResolvedValue({
        status: RESULTS.GRANTED,
      });

      const result = await NotificationPermissionService.checkPermission();
      expect(result).toBe(true);
    });

    it('should return false for non-granted permissions', async () => {
      const testCases = [
        RESULTS.DENIED,
        RESULTS.BLOCKED,
        RESULTS.LIMITED,
        RESULTS.UNAVAILABLE,
      ];

      for (const status of testCases) {
        (checkNotifications as jest.Mock).mockResolvedValue({ status });
        const result = await NotificationPermissionService.checkPermission();
        expect(result).toBe(false);
      }
    });

    it('should handle errors gracefully', async () => {
      const error = new Error('Permission check failed');
      (checkNotifications as jest.Mock).mockRejectedValue(error);

      await expect(
        NotificationPermissionService.checkPermission(),
      ).rejects.toThrow('Permission check failed');
    });
  });

  describe('requestPermission', () => {
    it('should return true when permission is already granted', async () => {
      (checkNotifications as jest.Mock).mockResolvedValue({
        status: RESULTS.GRANTED,
      });

      const result = await NotificationPermissionService.requestPermission();
      expect(result).toBe(true);
      expect(requestNotifications).not.toHaveBeenCalled();
    });

    it('should return false for blocked, limited, and unavailable permissions', async () => {
      const testCases = [RESULTS.BLOCKED, RESULTS.LIMITED, RESULTS.UNAVAILABLE];

      for (const status of testCases) {
        (checkNotifications as jest.Mock).mockResolvedValue({ status });
        const result = await NotificationPermissionService.requestPermission();
        expect(result).toBe(false);
        expect(requestNotifications).not.toHaveBeenCalled();
      }
    });

    it('should request permission when denied and return true when granted', async () => {
      (checkNotifications as jest.Mock).mockResolvedValue({
        status: RESULTS.DENIED,
      });
      (requestNotifications as jest.Mock).mockResolvedValue({
        status: RESULTS.GRANTED,
      });

      const result = await NotificationPermissionService.requestPermission();
      expect(result).toBe(true);
      expect(requestNotifications).toHaveBeenCalledWith([
        'alert',
        'sound',
        'badge',
      ]);
    });

    it('should handle unknown status and return false', async () => {
      (checkNotifications as jest.Mock).mockResolvedValue({
        status: 'unknown_status',
      });

      const result = await NotificationPermissionService.requestPermission();
      expect(result).toBe(false);
    });

    it('should handle errors and throw them', async () => {
      const error = new Error('Test error');
      (checkNotifications as jest.Mock).mockRejectedValue(error);

      await expect(
        NotificationPermissionService.requestPermission(),
      ).rejects.toThrow('Test error');
    });
  });
});
