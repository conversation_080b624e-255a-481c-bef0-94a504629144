// Mock native modules that cause issues in tests
jest.mock('react-native-background-geolocation', () => ({}));
jest.mock('react-native-background-fetch', () => ({}));
jest.mock('react-native-geolocation-service', () => ({}));
jest.mock('react-native-device-info', () => ({}));
jest.mock('react-native-keychain', () => ({
  setGenericPassword: jest.fn(),
  getGenericPassword: jest.fn(),
  resetGenericPassword: jest.fn(),
}));
jest.mock('react-native-onesignal', () => ({
  OneSignal: {
    Debug: {
      setLogLevel: jest.fn(),
    },
    initialize: jest.fn(),
    logout: jest.fn(),
    login: jest.fn(),
    User: {
      addEmail: jest.fn(),
      getOnesignalId: jest.fn(),
    },
    Notifications: {
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
    },
  },
  LogLevel: {
    Verbose: 'verbose',
  },
}));
jest.mock('react-native-permissions', () => ({
  openSettings: jest.fn(),
  check: jest.fn(),
  request: jest.fn(),
  PERMISSIONS: {
    IOS: {
      LOCATION_ALWAYS: 'ios.permission.LOCATION_ALWAYS',
      LOCATION_WHEN_IN_USE: 'ios.permission.LOCATION_WHEN_IN_USE',
      CAMERA: 'ios.permission.CAMERA',
      MICROPHONE: 'ios.permission.MICROPHONE',
      PHOTO_LIBRARY: 'ios.permission.PHOTO_LIBRARY',
      NOTIFICATIONS: 'ios.permission.NOTIFICATIONS',
    },
    ANDROID: {
      ACCESS_FINE_LOCATION: 'android.permission.ACCESS_FINE_LOCATION',
      ACCESS_COARSE_LOCATION: 'android.permission.ACCESS_COARSE_LOCATION',
      CAMERA: 'android.permission.CAMERA',
      RECORD_AUDIO: 'android.permission.RECORD_AUDIO',
      READ_EXTERNAL_STORAGE: 'android.permission.READ_EXTERNAL_STORAGE',
      WRITE_EXTERNAL_STORAGE: 'android.permission.WRITE_EXTERNAL_STORAGE',
    },
  },
  RESULTS: {
    UNAVAILABLE: 'unavailable',
    DENIED: 'denied',
    LIMITED: 'limited',
    GRANTED: 'granted',
    BLOCKED: 'blocked',
  },
}));
jest.mock('@sentry/react-native', () => ({
  init: jest.fn(),
  captureException: jest.fn(),
  captureMessage: jest.fn(),
  setTag: jest.fn(),
  setUser: jest.fn(),
  addBreadcrumb: jest.fn(),
  withScope: jest.fn(),
  startTransaction: jest.fn(),
  getCurrentHub: jest.fn(() => ({
    captureException: jest.fn(),
    captureMessage: jest.fn(),
  })),
}));

import TokenService, {
  TokenData,
  TokenResponse,
} from '~/services/TokenService';
import { KeychainService } from '~/utils/keyChain';

// Mock all dependencies
jest.mock('~/services/AuthService');
jest.mock('~/services/SentryService');
jest.mock('~/utils/keyChain');
jest.mock('~/services/LogoutService');
jest.mock('~/services/notifications/NotificationsService', () => ({
  clearOneSignalHandlers: jest.fn(),
  logoutOneSignalUser: jest.fn(),
}));

const mockedKeychainService = KeychainService as jest.Mocked<
  typeof KeychainService
>;

describe('TokenService', () => {
  const mockTokenData: TokenData = {
    accessToken: 'mock-access-token',
    refreshToken: 'mock-refresh-token',
    tokenType: 'Bearer',
  };

  const mockTokenResponse: TokenResponse = {
    access_token: 'mock-access-token',
    refresh_token: 'mock-refresh-token',
    token_type: 'Bearer',
  };

  beforeEach(() => {
    jest.clearAllMocks();
    // Reset the singleton instance for each test
    (TokenService as any).instance = null;
  });

  afterEach(() => {
    jest.clearAllTimers();
  });

  afterAll(() => {
    jest.restoreAllMocks();
  });

  describe('initialize', () => {
    it('should initialize successfully with stored tokens', async () => {
      mockedKeychainService.getTokenFromKeychain.mockResolvedValue({
        username: 'rapid_medical_tokens',
        password: JSON.stringify(mockTokenData),
        service: 'rapid_medical_tokens',
        storage: 'keychain' as any,
      });

      const result = await TokenService.initialize();

      expect(result).toBe(true);
      expect(mockedKeychainService.getTokenFromKeychain).toHaveBeenCalledTimes(
        1,
      );
    });

    it('should return false when no tokens are stored', async () => {
      mockedKeychainService.getTokenFromKeychain.mockResolvedValue(null);

      const result = await TokenService.initialize();

      expect(result).toBe(false);
      expect(mockedKeychainService.getTokenFromKeychain).toHaveBeenCalledTimes(
        1,
      );
    });
  });

  describe('storeTokens', () => {
    it('should store tokens successfully', async () => {
      mockedKeychainService.setTokensInKeychain.mockResolvedValue(true);

      await TokenService.storeTokens(mockTokenResponse);

      expect(mockedKeychainService.setTokensInKeychain).toHaveBeenCalledWith(
        mockTokenData,
      );
    });
  });

  describe('getAccessToken', () => {
    it('should return access token when tokens are already loaded', async () => {
      // Set up current tokens
      (TokenService as any).currentTokens = mockTokenData;

      const result = await TokenService.getAccessToken();

      expect(result).toBe('mock-access-token');
      expect(mockedKeychainService.getTokenFromKeychain).not.toHaveBeenCalled();
    });

    it('should load tokens from keychain and return access token', async () => {
      (TokenService as any).currentTokens = null;
      mockedKeychainService.getTokenFromKeychain.mockResolvedValue({
        username: 'rapid_medical_tokens',
        password: JSON.stringify(mockTokenData),
        service: 'rapid_medical_tokens',
        storage: 'keychain' as any,
      });

      const result = await TokenService.getAccessToken();

      expect(result).toBe('mock-access-token');
      expect(mockedKeychainService.getTokenFromKeychain).toHaveBeenCalledTimes(
        1,
      );
    });
  });

  describe('getRefreshToken', () => {
    it('should return refresh token when tokens are already loaded', async () => {
      (TokenService as any).currentTokens = mockTokenData;

      const result = await TokenService.getRefreshToken();

      expect(result).toBe('mock-refresh-token');
    });
  });

  describe('clearTokens', () => {
    it('should clear tokens successfully', async () => {
      (TokenService as any).currentTokens = mockTokenData;
      mockedKeychainService.removeTokenFromKeychain.mockResolvedValue(true);

      await TokenService.clearTokens();

      expect((TokenService as any).currentTokens).toBe(null);
      expect(mockedKeychainService.removeTokenFromKeychain).toHaveBeenCalled();
    });
  });
});
