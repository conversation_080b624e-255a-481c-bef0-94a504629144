import React from 'react';
import {
  StopTypeInfo,
  getStopStepperPillColors,
  getServiceType,
  getCoordinates,
  getAddressString,
  buildStopTasks,
  isValidDate,
  isValidStop,
  sortStopsByTime,
} from '~/utils/stops';
import { StopType, StopWithRelations } from '~/types/stops.types';
import { Parcel } from '~/types/parcel.types';
import { Service } from '~/types/service.types';
import colors from '~/styles/colors';

// Mock luxon
jest.mock('luxon', () => ({
  DateTime: {
    fromISO: jest.fn(),
  },
}));

// Mock React components
jest.mock('~/components/icons', () => ({
  ArrowDown: ({ color }: { color: string }) => (
    <div data-testid="arrow-down" style={{ color }} />
  ),
  ArrowUp: ({ color }: { color: string }) => (
    <div data-testid="arrow-up" style={{ color }} />
  ),
  ArrowUpDown: ({ color }: { color: string }) => (
    <div data-testid="arrow-up-down" style={{ color }} />
  ),
  Service: ({ color }: { color: string }) => (
    <div data-testid="service-icon" style={{ color }} />
  ),
}));

describe('stops utilities', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('StopTypeInfo', () => {
    it('should have correct configuration for all stop types', () => {
      expect(StopTypeInfo[StopType.Delivery]).toEqual({
        title: expect.any(String),
        icon: expect.any(Object),
        color: colors.darkBlue500,
      });

      expect(StopTypeInfo[StopType.Pickup]).toEqual({
        title: expect.any(String),
        icon: expect.any(Object),
        color: colors.darkBlue500,
      });

      expect(StopTypeInfo[StopType.Service]).toEqual({
        title: expect.any(String),
        icon: expect.any(Object),
        color: colors.darkBlue500,
      });

      expect(StopTypeInfo[StopType.Exchange]).toEqual({
        title: expect.any(String),
        icon: expect.any(Object),
        color: colors.darkBlue500,
      });

      expect(StopTypeInfo[StopType.Meet]).toEqual({
        title: '',
        icon: undefined,
        color: colors.darkBlue500,
      });

      expect(StopTypeInfo[StopType.Flight]).toEqual({
        title: '',
        icon: undefined,
        color: colors.darkBlue500,
      });

      expect(StopTypeInfo[StopType.Start]).toEqual({
        title: expect.any(String),
        icon: expect.any(Object),
        color: colors.darkBlue500,
      });

      expect(StopTypeInfo[StopType.End]).toEqual({
        title: expect.any(String),
        icon: expect.any(Object),
        color: colors.darkBlue500,
      });

      expect(StopTypeInfo[StopType.Break]).toEqual({
        title: '',
        icon: undefined,
        color: colors.darkBlue500,
      });
    });
  });

  describe('getStopStepperPillColors', () => {
    it('should return default colors when status is null', () => {
      const [backgroundColor, textColor] = getStopStepperPillColors(
        null as any,
      );
      expect(backgroundColor).toBe(colors.blue50);
      expect(textColor).toBe(colors.darkBlue500);
    });

    it('should return default colors when status is undefined', () => {
      const [backgroundColor, textColor] = getStopStepperPillColors(
        undefined as any,
      );
      expect(backgroundColor).toBe(colors.blue50);
      expect(textColor).toBe(colors.darkBlue500);
    });

    it('should return correct colors for ARRIVED status', () => {
      const [backgroundColor, textColor] = getStopStepperPillColors('ARRIVED');
      expect(backgroundColor).toBe(colors.blue50);
      expect(textColor).toBe(colors.darkBlue500);
    });

    it('should return correct colors for SCHEDULED status', () => {
      const [backgroundColor, textColor] =
        getStopStepperPillColors('SCHEDULED');
      expect(backgroundColor).toBe(colors.backgroundLight);
      expect(textColor).toBe(colors.darkGray);
    });

    it('should return correct colors for COMPLETE status', () => {
      const [backgroundColor, textColor] = getStopStepperPillColors('COMPLETE');
      expect(backgroundColor).toBe(colors.greenLight);
      expect(textColor).toBe(colors.greenDark);
    });

    it('should return correct colors for ON_SCHEDULE status', () => {
      const [backgroundColor, textColor] =
        getStopStepperPillColors('ON_SCHEDULE');
      expect(backgroundColor).toBe(colors.yellowLight);
      expect(textColor).toBe(colors.yellowDark);
    });

    it('should return correct colors for OPEN status', () => {
      const [backgroundColor, textColor] = getStopStepperPillColors('OPEN');
      expect(backgroundColor).toBe(colors.yellowLight);
      expect(textColor).toBe(colors.yellowDark);
    });

    it('should return correct colors for LATE status', () => {
      const [backgroundColor, textColor] = getStopStepperPillColors('LATE');
      expect(backgroundColor).toBe(colors.yellowLight);
      expect(textColor).toBe(colors.yellowDark);
    });

    it('should return correct colors for INACTIVE status', () => {
      const [backgroundColor, textColor] = getStopStepperPillColors('INACTIVE');
      expect(backgroundColor).toBe(colors.red25);
      expect(textColor).toBe(colors.red600);
    });

    it('should return default colors for unknown status', () => {
      const [backgroundColor, textColor] = getStopStepperPillColors(
        'UNKNOWN_STATUS' as any,
      );
      expect(backgroundColor).toBe(colors.blue50);
      expect(textColor).toBe(colors.darkBlue500);
    });
  });

  describe('getServiceType', () => {
    it('should return Pickup for pickup service type', () => {
      expect(getServiceType('Pickup')).toBe(StopType.Pickup);
      expect(getServiceType('pickup')).toBe(StopType.Pickup);
      expect(getServiceType('PICKUP')).toBe(StopType.Pickup);
      expect(getServiceType(' Pickup ')).toBe(StopType.Pickup);
    });

    it('should return Delivery for delivery service type', () => {
      expect(getServiceType('Delivery')).toBe(StopType.Delivery);
      expect(getServiceType('delivery')).toBe(StopType.Delivery);
      expect(getServiceType('DELIVERY')).toBe(StopType.Delivery);
      expect(getServiceType(' Delivery ')).toBe(StopType.Delivery);
    });

    it('should return Service for service service type', () => {
      expect(getServiceType('Service')).toBe(StopType.Service);
      expect(getServiceType('service')).toBe(StopType.Service);
      expect(getServiceType('SERVICE')).toBe(StopType.Service);
      expect(getServiceType(' Service ')).toBe(StopType.Service);
    });

    it('should return undefined for null service type', () => {
      expect(getServiceType(null)).toBeUndefined();
    });

    it('should return undefined for unknown service type', () => {
      expect(getServiceType('Unknown')).toBeUndefined();
      expect(getServiceType('Exchange')).toBeUndefined();
      expect(getServiceType('Meet')).toBeUndefined();
    });

    it('should handle service types with extra spaces', () => {
      expect(getServiceType('  Pickup  ')).toBe(StopType.Pickup);
      expect(getServiceType('Delivery   ')).toBe(StopType.Delivery);
      expect(getServiceType('   Service')).toBe(StopType.Service);
    });
  });

  describe('getCoordinates', () => {
    it('should return coordinates from Stop_Coordinates fields when available', () => {
      const stop = {
        Id: '1',
        Stop_Coordinates__Latitude__s: 40.7128,
        Stop_Coordinates__Longitude__s: -74.006,
        Coordinates__c: '40.0000,-74.0000', // This should be ignored
      } as unknown as StopWithRelations;

      const result = getCoordinates(stop);
      expect(result).toEqual({ latitude: 40.7128, longitude: -74.006 });
    });

    it('should parse coordinates from Coordinates__c when Stop_Coordinates are not available', () => {
      const stop = {
        Id: '1',
        Stop_Coordinates__Latitude__s: null,
        Stop_Coordinates__Longitude__s: null,
        Coordinates__c: '40.7128,-74.0060',
      } as unknown as StopWithRelations;

      const result = getCoordinates(stop);
      expect(result).toEqual({ latitude: 40.7128, longitude: -74.006 });
    });

    it('should return null when no coordinates are available', () => {
      const stop = {
        Id: '1',
        Stop_Coordinates__Latitude__s: null,
        Stop_Coordinates__Longitude__s: null,
        Coordinates__c: null,
      } as unknown as StopWithRelations;

      const result = getCoordinates(stop);
      expect(result).toBeNull();
    });

    it('should return null when Coordinates__c is invalid', () => {
      const stop = {
        Id: '1',
        Stop_Coordinates__Latitude__s: null,
        Stop_Coordinates__Longitude__s: null,
        Coordinates__c: 'invalid,coordinates',
      } as unknown as StopWithRelations;

      const result = getCoordinates(stop);
      expect(result).toBeNull();
    });

    it('should return null when Coordinates__c has empty values', () => {
      const stop = {
        Id: '1',
        Stop_Coordinates__Latitude__s: null,
        Stop_Coordinates__Longitude__s: null,
        Coordinates__c: ',',
      } as unknown as StopWithRelations;

      const result = getCoordinates(stop);
      expect(result).toBeNull();
    });

    it('should return null when Coordinates__c has NaN values', () => {
      const stop = {
        Id: '1',
        Stop_Coordinates__Latitude__s: null,
        Stop_Coordinates__Longitude__s: null,
        Coordinates__c: 'NaN,NaN',
      } as unknown as StopWithRelations;

      const result = getCoordinates(stop);
      expect(result).toBeNull();
    });

    it('should return null when Coordinates__c has zero values', () => {
      const stop = {
        Id: '1',
        Stop_Coordinates__Latitude__s: null,
        Stop_Coordinates__Longitude__s: null,
        Coordinates__c: '0,0',
      } as unknown as StopWithRelations;

      const result = getCoordinates(stop);
      expect(result).toBeNull();
    });
  });

  describe('getAddressString', () => {
    it('should prioritize Address__c over individual address fields', () => {
      const stop = {
        Id: '1',
        Address__c: '123 Main St, New York, NY 10001',
        Address_1__c: '456 Other St',
        City__c: 'Los Angeles',
        State__c: 'CA',
        Postal_Code__c: '90210',
      } as unknown as StopWithRelations;

      const result = getAddressString(stop);
      expect(result).toBe('123 Main St, New York, NY 10001');
    });

    it('should build address from individual fields when Address__c is not available', () => {
      const stop = {
        Id: '1',
        Address__c: null,
        Address_1__c: '123 Main St',
        Address_2__c: 'Suite 100',
        City__c: 'New York',
        State__c: 'NY',
        Postal_Code__c: '10001',
      } as unknown as StopWithRelations;

      const result = getAddressString(stop);
      expect(result).toBe('123 Main St, Suite 100, New York, NY, 10001');
    });

    it('should build address without Address_2__c when not available', () => {
      const stop = {
        Id: '1',
        Address__c: null,
        Address_1__c: '123 Main St',
        Address_2__c: null,
        City__c: 'New York',
        State__c: 'NY',
        Postal_Code__c: '10001',
      } as unknown as StopWithRelations;

      const result = getAddressString(stop);
      expect(result).toBe('123 Main St, New York, NY, 10001');
    });

    it('should return empty string when no address fields are available', () => {
      const stop = {
        Id: '1',
        Address__c: null,
        Address_1__c: '',
        Address_2__c: null,
        City__c: '',
        State__c: '',
        Postal_Code__c: '',
      } as unknown as StopWithRelations;

      const result = getAddressString(stop);
      expect(result).toBe('');
    });

    it('should handle partial address information', () => {
      const stop = {
        Id: '1',
        Address__c: null,
        Address_1__c: '123 Main St',
        Address_2__c: null,
        City__c: '',
        State__c: 'NY',
        Postal_Code__c: '',
      } as unknown as StopWithRelations;

      const result = getAddressString(stop);
      expect(result).toBe('123 Main St, NY');
    });
  });

  describe('buildStopTasks', () => {
    const mockParcel: Parcel = {
      Id: 'parcel-1',
      Pickup__c: 'stop-1',
      Delivery__c: 'stop-2',
      Requires_Signature__c: true,
      Reference_Required__c: false,
      Parcel_Type_Definition__c: 'Standard',
      Parcel_Type_Name__c: 'Package',
      Quantity__c: 1,
    };

    const mockService: Service = {
      Id: 'service-1',
      Name: 'Test Service',
      Stop__c: 'stop-1',
      Daily_Schedule__c: 'schedule-1',
      Service_Type_Name__c: 'Service',
      Completed_Time__c: null,
      attributes: { type: 'Service', url: '/services/1' },
    };

    it('should build pickup task when isPickup is true', () => {
      const result = buildStopTasks({
        isPickup: true,
        isDelivery: false,
        isService: false,
        pickupParcels: [mockParcel],
        deliveryParcels: [],
        stopServices: [],
      });

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        id: 'pickup',
        type: StopType.Pickup,
        data: [mockParcel],
        icon: expect.any(Object),
      });
    });

    it('should build delivery task when isDelivery is true', () => {
      const result = buildStopTasks({
        isPickup: false,
        isDelivery: true,
        isService: false,
        pickupParcels: [],
        deliveryParcels: [mockParcel],
        stopServices: [],
      });

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        id: 'delivery',
        type: StopType.Delivery,
        data: [mockParcel],
        icon: expect.any(Object),
      });
    });

    it('should build service task when isService is true', () => {
      const result = buildStopTasks({
        isPickup: false,
        isDelivery: false,
        isService: true,
        pickupParcels: [],
        deliveryParcels: [],
        stopServices: [mockService],
      });

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        id: 'service-1',
        type: StopType.Service,
        data: mockService,
        icon: expect.any(Object),
      });
    });

    it('should build pickup task when no other tasks are specified', () => {
      const result = buildStopTasks({
        isPickup: false,
        isDelivery: false,
        isService: false,
        pickupParcels: [mockParcel],
        deliveryParcels: [],
        stopServices: [],
      });

      expect(result).toHaveLength(0);
    });

    it('should build multiple tasks when multiple types are true', () => {
      const result = buildStopTasks({
        isPickup: true,
        isDelivery: true,
        isService: true,
        pickupParcels: [mockParcel],
        deliveryParcels: [mockParcel],
        stopServices: [mockService],
      });

      expect(result).toHaveLength(3);
      expect(result[0].id).toBe('pickup');
      expect(result[1].id).toBe('delivery');
      expect(result[2].id).toBe('service-1');
    });

    it('should handle pickup service type correctly', () => {
      const pickupService: Service = {
        ...mockService,
        Service_Type_Name__c: 'Pickup',
      };

      const result = buildStopTasks({
        isPickup: false,
        isDelivery: false,
        isService: true,
        pickupParcels: [],
        deliveryParcels: [],
        stopServices: [pickupService],
      });

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        id: 'service-1',
        type: StopType.Pickup,
        data: pickupService,
        icon: expect.any(Object),
      });
    });

    it('should prioritize pickup task over pickup service when both exist', () => {
      const pickupService: Service = {
        ...mockService,
        Service_Type_Name__c: 'Pickup',
      };

      const result = buildStopTasks({
        isPickup: true,
        isDelivery: false,
        isService: true,
        pickupParcels: [mockParcel],
        deliveryParcels: [],
        stopServices: [pickupService, mockService],
      });

      expect(result).toHaveLength(2);
      expect(result[0].id).toBe('pickup');
      expect(result[0].type).toBe(StopType.Pickup);
      expect(result[0].data).toEqual([mockParcel]);
      expect(result[1].id).toBe('service-1');
      expect(result[1].type).toBe(StopType.Service);
    });

    it('should handle unknown service types', () => {
      const unknownService: Service = {
        ...mockService,
        Service_Type_Name__c: 'Unknown',
      };

      const result = buildStopTasks({
        isPickup: false,
        isDelivery: false,
        isService: true,
        pickupParcels: [],
        deliveryParcels: [],
        stopServices: [unknownService],
      });

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        id: 'service-1',
        type: StopType.Service,
        data: unknownService,
        icon: expect.any(Object),
      });
    });
  });

  describe('isValidDate', () => {
    const { DateTime } = require('luxon');

    it('should return true for valid ISO date string', () => {
      const mockDateTime = {
        isValid: true,
      };
      DateTime.fromISO.mockReturnValue(mockDateTime);

      const result = isValidDate('2023-12-25T14:30:00.000Z');
      expect(result).toBe(true);
      expect(DateTime.fromISO).toHaveBeenCalledWith('2023-12-25T14:30:00.000Z');
    });

    it('should return false for invalid date string', () => {
      const mockDateTime = {
        isValid: false,
      };
      DateTime.fromISO.mockReturnValue(mockDateTime);

      const result = isValidDate('invalid-date');
      expect(result).toBe(false);
    });

    it('should handle DateTime parsing errors', () => {
      DateTime.fromISO.mockImplementation(() => {
        throw new Error('Parsing error');
      });

      expect(() => isValidDate('2023-12-25T14:30:00.000Z')).toThrow(
        'Parsing error',
      );
    });
  });

  describe('isValidStop', () => {
    it('should return true for valid stop object', () => {
      const validStop = {
        Id: 'stop-1',
        Name: 'Test Stop',
      } as unknown as StopWithRelations;

      const result = isValidStop(validStop);
      expect(result).toBe(true);
    });

    it('should return false for null', () => {
      const result = isValidStop(null);
      expect(result).toBeFalsy();
    });

    it('should return false for undefined', () => {
      const result = isValidStop(undefined);
      expect(result).toBeFalsy();
    });

    it('should return false for non-object', () => {
      const result = isValidStop('not an object');
      expect(result).toBeFalsy();
    });

    it('should return false for object without Id', () => {
      const invalidStop = {
        Name: 'Test Stop',
      };

      const result = isValidStop(invalidStop);
      expect(result).toBeFalsy();
    });

    it('should return false for object with non-string Id', () => {
      const invalidStop = {
        Id: 123,
        Name: 'Test Stop',
      };

      const result = isValidStop(invalidStop);
      expect(result).toBeFalsy();
    });

    it('should return false for object with empty string Id', () => {
      const invalidStop = {
        Id: '',
        Name: 'Test Stop',
      };

      const result = isValidStop(invalidStop);
      expect(result).toBeFalsy();
    });
  });

  describe('sortStopsByTime', () => {
    it('should sort stops by Stop_Time_Preferred__c in ascending order', () => {
      const stops: StopWithRelations[] = [
        {
          Id: 'stop-2',
          Stop_Time_Preferred__c: '14:30',
        } as StopWithRelations,
        {
          Id: 'stop-1',
          Stop_Time_Preferred__c: '09:00',
        } as StopWithRelations,
        {
          Id: 'stop-3',
          Stop_Time_Preferred__c: '16:45',
        } as StopWithRelations,
      ];

      const result = sortStopsByTime(stops);

      expect(result).toHaveLength(3);
      expect(result[0].Id).toBe('stop-1');
      expect(result[1].Id).toBe('stop-2');
      expect(result[2].Id).toBe('stop-3');
    });

    it('should handle stops with null Stop_Time_Preferred__c', () => {
      const stops: StopWithRelations[] = [
        {
          Id: 'stop-2',
          Stop_Time_Preferred__c: '14:30',
        } as StopWithRelations,
        {
          Id: 'stop-1',
          Stop_Time_Preferred__c: null,
        } as StopWithRelations,
        {
          Id: 'stop-3',
          Stop_Time_Preferred__c: '16:45',
        } as StopWithRelations,
      ];

      const result = sortStopsByTime(stops);

      expect(result).toHaveLength(3);
      expect(result[0].Id).toBe('stop-1'); // null values come first
      expect(result[1].Id).toBe('stop-2');
      expect(result[2].Id).toBe('stop-3');
    });

    it('should handle stops with empty string Stop_Time_Preferred__c', () => {
      const stops: StopWithRelations[] = [
        {
          Id: 'stop-2',
          Stop_Time_Preferred__c: '14:30',
        } as StopWithRelations,
        {
          Id: 'stop-1',
          Stop_Time_Preferred__c: '',
        } as StopWithRelations,
        {
          Id: 'stop-3',
          Stop_Time_Preferred__c: '16:45',
        } as StopWithRelations,
      ];

      const result = sortStopsByTime(stops);

      expect(result).toHaveLength(3);
      expect(result[0].Id).toBe('stop-1'); // empty strings come first
      expect(result[1].Id).toBe('stop-2');
      expect(result[2].Id).toBe('stop-3');
    });

    it('should not mutate the original array', () => {
      const stops: StopWithRelations[] = [
        {
          Id: 'stop-2',
          Stop_Time_Preferred__c: '14:30',
        } as StopWithRelations,
        {
          Id: 'stop-1',
          Stop_Time_Preferred__c: '09:00',
        } as StopWithRelations,
      ];

      const originalStops = [...stops];
      const result = sortStopsByTime(stops);

      expect(result).not.toBe(stops);
      expect(stops).toEqual(originalStops);
    });

    it('should return empty array for empty input', () => {
      const result = sortStopsByTime([]);
      expect(result).toEqual([]);
    });

    it('should handle single stop', () => {
      const stops: StopWithRelations[] = [
        {
          Id: 'stop-1',
          Stop_Time_Preferred__c: '14:30',
        } as StopWithRelations,
      ];

      const result = sortStopsByTime(stops);
      expect(result).toEqual(stops);
    });
  });
});
