import {
  mapActivityType,
  calculateDistanceBetweenCoordinatesInMeters,
  isValidCoordinate,
  getValidGeofenceDistance,
  getEffectiveGeofenceDistance,
  metersToMiles,
} from '~/utils/location';
import { MotionActivity } from 'react-native-background-geolocation';

describe('location utils', () => {
  describe('mapActivityType', () => {
    it('returns "Still" for null activity', () => {
      expect(mapActivityType(null)).toBe('Still');
    });

    it('returns "Still" for undefined activity', () => {
      expect(mapActivityType(undefined)).toBe('Still');
    });

    it('maps in_vehicle to "In Vehicle"', () => {
      expect(mapActivityType('in_vehicle')).toBe('In Vehicle');
    });

    it('maps on_foot to "On Foot"', () => {
      expect(mapActivityType('on_foot')).toBe('On Foot');
    });

    it('maps walking to "On Foot"', () => {
      expect(mapActivityType('walking')).toBe('On Foot');
    });

    it('maps running to "On Foot"', () => {
      expect(mapActivityType('running')).toBe('On Foot');
    });

    it('maps still to "Still"', () => {
      expect(mapActivityType('still')).toBe('Still');
    });

    it('maps on_bicycle to "In Vehicle"', () => {
      expect(mapActivityType('on_bicycle')).toBe('In Vehicle');
    });

    it('maps unknown to "Still"', () => {
      expect(mapActivityType('unknown')).toBe('Still');
    });

    it('returns "Still" for unmapped activity types', () => {
      expect(mapActivityType('flying' as MotionActivity['type'])).toBe('Still');
      expect(mapActivityType('swimming' as MotionActivity['type'])).toBe(
        'Still',
      );
    });
  });

  describe('calculateDistanceBetweenCoordinatesInMeters', () => {
    it('calculates distance between two points correctly', () => {
      // New York City coordinates
      const nycLat = 40.7128;
      const nycLon = -74.006;

      // Los Angeles coordinates
      const laLat = 34.0522;
      const laLon = -118.2437;

      const distance = calculateDistanceBetweenCoordinatesInMeters(
        nycLat,
        nycLon,
        laLat,
        laLon,
      );

      // Distance should be approximately 3935 km (3,935,000 meters)
      expect(distance).toBeGreaterThan(3900000);
      expect(distance).toBeLessThan(4000000);
    });

    it('returns 0 for identical coordinates', () => {
      const lat = 40.7128;
      const lon = -74.006;

      const distance = calculateDistanceBetweenCoordinatesInMeters(
        lat,
        lon,
        lat,
        lon,
      );

      expect(distance).toBe(0);
    });

    it('calculates distance for very close points', () => {
      // Two points very close to each other (within meters)
      const lat1 = 40.7128;
      const lon1 = -74.006;
      const lat2 = 40.7129;
      const lon2 = -74.0061;

      const distance = calculateDistanceBetweenCoordinatesInMeters(
        lat1,
        lon1,
        lat2,
        lon2,
      );

      expect(distance).toBeGreaterThan(0);
      expect(distance).toBeLessThan(100); // Should be less than 100 meters
    });

    it('calculates distance for antipodal points', () => {
      // Points on opposite sides of the Earth
      const lat1 = 40.7128;
      const lon1 = -74.006;
      const lat2 = -40.7128;
      const lon2 = 105.994; // 180 degrees from lon1

      const distance = calculateDistanceBetweenCoordinatesInMeters(
        lat1,
        lon1,
        lat2,
        lon2,
      );

      // Should be approximately half the Earth's circumference
      expect(distance).toBeGreaterThan(19000000); // ~19,000 km
      expect(distance).toBeLessThan(21000000); // ~21,000 km
    });

    it('handles negative coordinates', () => {
      const lat1 = -40.7128;
      const lon1 = -74.006;
      const lat2 = -34.0522;
      const lon2 = -118.2437;

      const distance = calculateDistanceBetweenCoordinatesInMeters(
        lat1,
        lon1,
        lat2,
        lon2,
      );

      expect(distance).toBeGreaterThan(0);
    });

    it('handles coordinates crossing the 180/-180 meridian', () => {
      const lat1 = 40.7128;
      const lon1 = 179.9;
      const lat2 = 40.7128;
      const lon2 = -179.9;

      const distance = calculateDistanceBetweenCoordinatesInMeters(
        lat1,
        lon1,
        lat2,
        lon2,
      );

      expect(distance).toBeGreaterThan(0);
    });
  });

  describe('isValidCoordinate', () => {
    it('returns false for null coordinate', () => {
      expect(isValidCoordinate(null)).toBe(false);
    });

    it('returns false for undefined coordinate', () => {
      expect(isValidCoordinate(undefined)).toBe(false);
    });

    it('returns false for coordinate with missing lat', () => {
      expect(isValidCoordinate({ lon: 0 })).toBe(false);
    });

    it('returns false for coordinate with missing lon', () => {
      expect(isValidCoordinate({ lat: 0 })).toBe(false);
    });

    it('returns false for coordinate with invalid lat (too high)', () => {
      expect(isValidCoordinate({ lat: 91, lon: 0 })).toBe(false);
    });

    it('returns false for coordinate with invalid lat (too low)', () => {
      expect(isValidCoordinate({ lat: -91, lon: 0 })).toBe(false);
    });

    it('returns false for coordinate with invalid lon (too high)', () => {
      expect(isValidCoordinate({ lat: 0, lon: 181 })).toBe(false);
    });

    it('returns false for coordinate with invalid lon (too low)', () => {
      expect(isValidCoordinate({ lat: 0, lon: -181 })).toBe(false);
    });

    it('returns true for valid coordinates', () => {
      expect(isValidCoordinate({ lat: 0, lon: 0 })).toBe(true);
      expect(isValidCoordinate({ lat: 90, lon: 180 })).toBe(true);
      expect(isValidCoordinate({ lat: -90, lon: -180 })).toBe(true);
      expect(isValidCoordinate({ lat: 40.7128, lon: -74.006 })).toBe(true);
    });

    it('returns false for non-numeric coordinates', () => {
      expect(isValidCoordinate({ lat: '40.7128' as any, lon: -74.006 })).toBe(
        false,
      );
      expect(isValidCoordinate({ lat: 40.7128, lon: '-74.0060' as any })).toBe(
        false,
      );
    });

    it('returns false for NaN coordinates', () => {
      expect(isValidCoordinate({ lat: NaN, lon: 0 })).toBe(false);
      expect(isValidCoordinate({ lat: 0, lon: NaN })).toBe(false);
    });

    it('returns false for infinite coordinates', () => {
      expect(isValidCoordinate({ lat: Infinity, lon: 0 })).toBe(false);
      expect(isValidCoordinate({ lat: 0, lon: -Infinity })).toBe(false);
    });
  });

  describe('getValidGeofenceDistance', () => {
    it('returns undefined for null input', () => {
      expect(getValidGeofenceDistance(null)).toBeUndefined();
    });

    it('returns undefined for undefined input', () => {
      expect(getValidGeofenceDistance(undefined)).toBeUndefined();
    });

    it('returns undefined for empty string', () => {
      expect(getValidGeofenceDistance('')).toBeUndefined();
    });

    it('returns undefined for whitespace string', () => {
      expect(getValidGeofenceDistance('   ')).toBeUndefined();
    });

    it('returns undefined for zero', () => {
      expect(getValidGeofenceDistance(0)).toBeUndefined();
    });

    it('returns undefined for negative number', () => {
      expect(getValidGeofenceDistance(-10)).toBeUndefined();
    });

    it('returns undefined for negative string', () => {
      expect(getValidGeofenceDistance('-10')).toBeUndefined();
    });

    it('returns undefined for non-numeric string', () => {
      expect(getValidGeofenceDistance('abc')).toBeUndefined();
    });

    it('returns undefined for NaN', () => {
      expect(getValidGeofenceDistance(NaN)).toBeUndefined();
    });

    it('returns undefined for Infinity', () => {
      expect(getValidGeofenceDistance(Infinity)).toBeUndefined();
    });

    it('returns the value for valid positive number', () => {
      expect(getValidGeofenceDistance(100)).toBe(100);
      expect(getValidGeofenceDistance(0.5)).toBe(0.5);
    });

    it('returns the parsed value for valid positive string', () => {
      expect(getValidGeofenceDistance('100')).toBe(100);
      expect(getValidGeofenceDistance('0.5')).toBe(0.5);
      expect(getValidGeofenceDistance('  50  ')).toBe(50);
    });

    it('handles very large numbers', () => {
      expect(getValidGeofenceDistance(1000000)).toBe(1000000);
      expect(getValidGeofenceDistance('1000000')).toBe(1000000);
    });

    it('handles decimal numbers', () => {
      expect(getValidGeofenceDistance(10.5)).toBe(10.5);
      expect(getValidGeofenceDistance('10.5')).toBe(10.5);
    });
  });

  describe('getEffectiveGeofenceDistance', () => {
    const mockStop = {
      Location__r: { Geofencing_Distance__c: '100' },
      Geofencing_Distance__c: '200',
    };

    const mockRoute = {
      Geofencing_Distance__c: '300',
      Route__r: { Geofencing_Distance__c: '400' },
    };

    it('returns first valid distance from stop.Location__r.Geofencing_Distance__c', () => {
      const result = getEffectiveGeofenceDistance({
        stop: mockStop,
        route: mockRoute,
      });
      expect(result).toBe(100);
    });

    it('returns second valid distance when first is invalid', () => {
      const stopWithInvalidFirst = {
        ...mockStop,
        Location__r: { Geofencing_Distance__c: null },
      };

      const result = getEffectiveGeofenceDistance({
        stop: stopWithInvalidFirst,
        route: mockRoute,
      });
      expect(result).toBe(200);
    });

    it('returns third valid distance when first two are invalid', () => {
      const stopWithInvalidFirstTwo = {
        ...mockStop,
        Location__r: { Geofencing_Distance__c: null },
        Geofencing_Distance__c: null,
      };

      const result = getEffectiveGeofenceDistance({
        stop: stopWithInvalidFirstTwo,
        route: mockRoute,
      });
      expect(result).toBe(300);
    });

    it('returns fourth valid distance when first three are invalid', () => {
      const stopWithInvalidFirstThree = {
        ...mockStop,
        Location__r: { Geofencing_Distance__c: null },
        Geofencing_Distance__c: null,
      };

      const routeWithInvalidFirstThree = {
        ...mockRoute,
        Geofencing_Distance__c: null,
      };

      const result = getEffectiveGeofenceDistance({
        stop: stopWithInvalidFirstThree,
        route: routeWithInvalidFirstThree,
      });
      expect(result).toBe(400);
    });

    it('returns undefined when all distances are invalid', () => {
      const invalidStop = {
        Location__r: { Geofencing_Distance__c: null },
        Geofencing_Distance__c: null,
      };

      const invalidRoute = {
        Geofencing_Distance__c: null,
        Route__r: { Geofencing_Distance__c: null },
      };

      const result = getEffectiveGeofenceDistance({
        stop: invalidStop,
        route: invalidRoute,
      });
      expect(result).toBeUndefined();
    });

    it('handles null stop and route', () => {
      const result = getEffectiveGeofenceDistance({
        stop: null,
        route: null,
      });
      expect(result).toBeUndefined();
    });

    it('handles undefined stop and route', () => {
      const result = getEffectiveGeofenceDistance({
        stop: undefined,
        route: undefined,
      });
      expect(result).toBeUndefined();
    });

    it('handles missing nested properties', () => {
      const stopWithoutLocation = {
        Geofencing_Distance__c: '200',
      };

      const routeWithoutRoute = {
        Geofencing_Distance__c: '300',
      };

      const result = getEffectiveGeofenceDistance({
        stop: stopWithoutLocation,
        route: routeWithoutRoute,
      });
      expect(result).toBe(200);
    });

    it('handles string and number values', () => {
      const stopWithMixedTypes = {
        Location__r: { Geofencing_Distance__c: '100' },
        Geofencing_Distance__c: 200,
      };

      const routeWithMixedTypes = {
        Geofencing_Distance__c: '300',
        Route__r: { Geofencing_Distance__c: 400 },
      };

      const result = getEffectiveGeofenceDistance({
        stop: stopWithMixedTypes,
        route: routeWithMixedTypes,
      });
      expect(result).toBe(100);
    });
  });

  describe('metersToMiles', () => {
    it('converts meters to miles correctly', () => {
      expect(metersToMiles(1609.344)).toBe(1); // 1 mile
      expect(metersToMiles(3218.688)).toBe(2); // 2 miles
      expect(metersToMiles(804.672)).toBe(0.5); // 0.5 miles
    });

    it('rounds to 3 decimal places', () => {
      expect(metersToMiles(1000)).toBe(0.621); // 1000 meters ≈ 0.621 miles
      expect(metersToMiles(500)).toBe(0.311); // 500 meters ≈ 0.311 miles
    });

    it('handles zero meters', () => {
      expect(metersToMiles(0)).toBe(0);
    });

    it('handles very small distances', () => {
      expect(metersToMiles(1)).toBe(0.001); // 1 meter ≈ 0.001 miles
      expect(metersToMiles(0.1)).toBe(0); // Very small distance rounds to 0
    });

    it('handles very large distances', () => {
      expect(metersToMiles(1000000)).toBe(621.371); // 1 million meters ≈ 621.371 miles
    });

    it('throws error for negative distance', () => {
      expect(() => metersToMiles(-100)).toThrow('Distance cannot be negative');
    });

    it('handles decimal meters', () => {
      expect(metersToMiles(1609.344)).toBe(1); // Exactly 1 mile
      expect(metersToMiles(1609.3441)).toBe(1); // Slightly more than 1 mile
    });

    it('handles edge cases around rounding', () => {
      // Test values that might cause rounding issues
      expect(metersToMiles(1609.343)).toBe(1); // Just under 1 mile
      expect(metersToMiles(1609.345)).toBe(1); // Just over 1 mile
    });
  });
});
