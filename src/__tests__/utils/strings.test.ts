import {
  interpolateString,
  parseBoolean,
  normalizeBarcodeValue,
} from '~/utils/strings';
import { formatTimeRemaining } from '~/utils/dateAndTime';

describe('strings utils', () => {
  describe('interpolateString', () => {
    it('interpolates simple string with single variable', () => {
      const template = 'Hello {{name}}!';
      const values = { name: 'World' };
      const result = interpolateString(template, values);
      expect(result).toBe('Hello World!');
    });

    it('interpolates string with multiple variables', () => {
      const template = 'Hello {{firstName}} {{lastName}}!';
      const values = { firstName: 'John', lastName: 'Doe' };
      const result = interpolateString(template, values);
      expect(result).toBe('Hello John Doe!');
    });

    it('interpolates string with numeric values', () => {
      const template = 'Count: {{count}}, Price: {{price}}';
      const values = { count: 5, price: 10.99 };
      const result = interpolateString(template, values);
      expect(result).toBe('Count: 5, Price: 10.99');
    });

    it('leaves unmatched placeholders unchanged', () => {
      const template = 'Hello {{name}}, welcome to {{place}}!';
      const values = { name: 'John' };
      const result = interpolateString(template, values);
      expect(result).toBe('Hello John, welcome to {{place}}!');
    });

    it('handles empty values object', () => {
      const template = 'Hello {{name}}!';
      const values = {};
      const result = interpolateString(template, values);
      expect(result).toBe('Hello {{name}}!');
    });

    it('handles null and undefined values', () => {
      const template = 'Name: {{name}}, Age: {{age}}';
      const values = { name: 'null', age: 'undefined' };
      const result = interpolateString(template, values);
      expect(result).toBe('Name: null, Age: undefined');
    });

    it('handles empty string values', () => {
      const template = 'Hello {{name}}!';
      const values = { name: '' };
      const result = interpolateString(template, values);
      expect(result).toBe('Hello !');
    });

    it('handles zero values', () => {
      const template = 'Count: {{count}}';
      const values = { count: 0 };
      const result = interpolateString(template, values);
      expect(result).toBe('Count: 0');
    });

    it('handles template with no placeholders', () => {
      const template = 'Hello World!';
      const values = { name: 'John' };
      const result = interpolateString(template, values);
      expect(result).toBe('Hello World!');
    });

    it('handles nested curly braces in template', () => {
      const template = 'Hello {{name}}, {not a placeholder}';
      const values = { name: 'John' };
      const result = interpolateString(template, values);
      expect(result).toBe('Hello John, {not a placeholder}');
    });

    it('handles special characters in variable names', () => {
      const template = 'Hello {{user-name}}!';
      const values = { 'user-name': 'John' };
      const result = interpolateString(template, values);
      expect(result).toBe('Hello John!');
    });

    it('handles whitespace in variable names', () => {
      const template = 'Hello {{ user name }}!';
      const values = { ' user name ': 'John' };
      const result = interpolateString(template, values);
      expect(result).toBe('Hello John!');
    });

    it('handles complex nested templates', () => {
      const template = '{{greeting}} {{name}}, your {{item}} costs {{price}}';
      const values = {
        greeting: 'Hello',
        name: 'John',
        item: 'book',
        price: '$10',
      };
      const result = interpolateString(template, values);
      expect(result).toBe('Hello John, your book costs $10');
    });
  });

  describe('formatTimeRemaining', () => {
    it('should format seconds into MM:SS format', () => {
      expect(formatTimeRemaining(65)).toBe('01:05');
      expect(formatTimeRemaining(120)).toBe('02:00');
      expect(formatTimeRemaining(45)).toBe('00:45');
    });

    it('should handle zero seconds', () => {
      expect(formatTimeRemaining(0)).toBe('00:00');
    });

    it('should return default value for undefined input', () => {
      expect(formatTimeRemaining(undefined)).toBe('00:00');
    });

    it('should pad single digit minutes and seconds with zeros', () => {
      expect(formatTimeRemaining(5)).toBe('00:05');
      expect(formatTimeRemaining(305)).toBe('05:05');
    });
  });

  describe('parseBoolean', () => {
    it('returns true for boolean true', () => {
      expect(parseBoolean(true)).toBe(true);
    });

    it('returns false for boolean false', () => {
      expect(parseBoolean(false)).toBe(false);
    });

    it('returns true for string "true"', () => {
      expect(parseBoolean('true')).toBe(true);
    });

    it('returns true for string "TRUE"', () => {
      expect(parseBoolean('TRUE')).toBe(true);
    });

    it('returns true for string "True"', () => {
      expect(parseBoolean('True')).toBe(true);
    });

    it('returns false for string "false"', () => {
      expect(parseBoolean('false')).toBe(false);
    });

    it('returns false for string "FALSE"', () => {
      expect(parseBoolean('FALSE')).toBe(false);
    });

    it('returns false for string "False"', () => {
      expect(parseBoolean('False')).toBe(false);
    });

    it('returns true for string with whitespace " true "', () => {
      expect(parseBoolean(' true ')).toBe(true);
    });

    it('returns false for string with whitespace " false "', () => {
      expect(parseBoolean(' false ')).toBe(false);
    });

    it('returns false for undefined', () => {
      expect(parseBoolean(undefined)).toBe(false);
    });

    it('returns false for null', () => {
      expect(parseBoolean(null as any)).toBe(false);
    });

    it('returns false for empty string', () => {
      expect(parseBoolean('')).toBe(false);
    });

    it('returns false for whitespace only string', () => {
      expect(parseBoolean('   ')).toBe(false);
    });

    it('returns false for non-boolean strings', () => {
      expect(parseBoolean('yes')).toBe(false);
      expect(parseBoolean('no')).toBe(false);
      expect(parseBoolean('1')).toBe(false);
      expect(parseBoolean('0')).toBe(false);
      expect(parseBoolean('on')).toBe(false);
      expect(parseBoolean('off')).toBe(false);
    });
  });

  describe('normalizeBarcodeValue', () => {
    it('returns empty string for empty input', () => {
      expect(normalizeBarcodeValue('')).toBe('');
    });

    it('returns empty string for null/undefined input', () => {
      expect(normalizeBarcodeValue(null as any)).toBe('');
      expect(normalizeBarcodeValue(undefined as any)).toBe('');
    });

    it('trims whitespace from input', () => {
      expect(normalizeBarcodeValue(' 1234567890123 ')).toBe('1234567890123');
    });

    it('returns original value for non-EAN-13 type', () => {
      const value = '1234567890123';
      expect(normalizeBarcodeValue(value, 'qr')).toBe(value);
      expect(normalizeBarcodeValue(value, 'code-128')).toBe(value);
      expect(normalizeBarcodeValue(value, 'upc-a')).toBe(value);
    });

    it('returns original value for EAN-13 without leading zero', () => {
      const value = '1234567890123';
      expect(normalizeBarcodeValue(value, 'ean-13')).toBe(value);
    });

    it('normalizes valid UPC-A embedded in EAN-13', () => {
      const ean13Value = '0123456789012'; // EAN-13 with leading 0
      const expectedUpcA = '123456789012'; // UPC-A after removing leading 0
      expect(normalizeBarcodeValue(ean13Value, 'ean-13')).toBe(expectedUpcA);
    });

    it('handles EAN-13 with leading zero and non-numeric characters', () => {
      const value = '0ABC123456789'; // Not all digits after removing leading 0
      expect(normalizeBarcodeValue(value, 'ean-13')).toBe(value);
    });

    it('handles EAN-13 with leading zero but wrong length', () => {
      const value = '012345678901'; // 11 digits after removing leading 0 (not 12)
      expect(normalizeBarcodeValue(value, 'ean-13')).toBe(value);
    });

    it('handles EAN-13 with leading zero but too long', () => {
      const value = '01234567890123'; // 13 digits after removing leading 0 (not 12)
      expect(normalizeBarcodeValue(value, 'ean-13')).toBe(value);
    });

    it('handles edge case with exactly 12 digits after removing leading zero', () => {
      const ean13Value = '0000000000000'; // All zeros
      const expectedUpcA = '000000000000'; // 12 zeros
      expect(normalizeBarcodeValue(ean13Value, 'ean-13')).toBe(expectedUpcA);
    });

    it('handles edge case with mixed digits after removing leading zero', () => {
      const ean13Value = '0123456789012';
      const expectedUpcA = '123456789012';
      expect(normalizeBarcodeValue(ean13Value, 'ean-13')).toBe(expectedUpcA);
    });

    it('handles EAN-13 without type parameter', () => {
      const value = '0123456789012';
      expect(normalizeBarcodeValue(value)).toBe(value);
    });

    it('handles EAN-13 with empty type parameter', () => {
      const value = '0123456789012';
      expect(normalizeBarcodeValue(value, '')).toBe(value);
    });

    it('handles case-sensitive EAN-13 type', () => {
      const ean13Value = '0123456789012';
      const expectedUpcA = '123456789012';
      expect(normalizeBarcodeValue(ean13Value, 'ean-13')).toBe(expectedUpcA);
      // Case-insensitive versions should not normalize
      expect(normalizeBarcodeValue(ean13Value, 'EAN-13')).toBe(ean13Value);
      expect(normalizeBarcodeValue(ean13Value, 'Ean-13')).toBe(ean13Value);
    });

    it('handles special characters in barcode value', () => {
      const value = '0123456789012';
      expect(normalizeBarcodeValue(value, 'ean-13')).toBe('123456789012');
    });

    it('handles barcode value with spaces', () => {
      const value = ' 0123456789012 ';
      expect(normalizeBarcodeValue(value, 'ean-13')).toBe('123456789012');
    });

    it('handles barcode value with tabs and newlines', () => {
      const value = '\t0123456789012\n';
      expect(normalizeBarcodeValue(value, 'ean-13')).toBe('123456789012');
    });
  });
});
