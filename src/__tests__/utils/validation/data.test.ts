import { validateThatDataIsNotEmpty } from '~/utils/validation/data';

describe('validateThatDataIsNotEmpty', () => {
  it('returns false for empty string', () => {
    expect(validateThatDataIsNotEmpty('')).toBe(false);
  });
  it('returns false for null', () => {
    expect(validateThatDataIsNotEmpty(null as any)).toBe(false);
  });
  it('returns false for undefined', () => {
    expect(validateThatDataIsNotEmpty(undefined as any)).toBe(false);
  });
  it('returns true for non-empty string', () => {
    expect(validateThatDataIsNotEmpty('hello')).toBe(true);
    expect(validateThatDataIsNotEmpty(' ')).toBe(true);
    expect(validateThatDataIsNotEmpty('0')).toBe(true);
  });
});
