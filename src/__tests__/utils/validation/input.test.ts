import * as input from '~/utils/validation/input';

describe('input validation utils', () => {
  describe('checkIfValidEmail', () => {
    it('validates correct emails', () => {
      expect(input.checkIfValidEmail('<EMAIL>')).toBe(true);
      expect(input.checkIfValidEmail('<EMAIL>')).toBe(true);
      expect(input.checkIfValidEmail('<EMAIL>')).toBe(true);
    });
    it('invalidates incorrect emails', () => {
      expect(input.checkIfValidEmail('test@')).toBe(false);
      expect(input.checkIfValidEmail('test@.com')).toBe(false);
      expect(input.checkIfValidEmail('test@com')).toBe(false);
      expect(input.checkIfValidEmail('test.com')).toBe(false);
      expect(input.checkIfValidEmail('<EMAIL>')).toBe(false);
      expect(input.checkIfValidEmail('')).toBe(false);
      expect(input.checkIfValidEmail('test@domain,com')).toBe(false);
      expect(input.checkIfValidEmail('test@ domain.com')).toBe(false);
    });
  });

  describe('getEmailValidationMessage', () => {
    it('returns null for valid email', () => {
      expect(input.getEmailValidationMessage('<EMAIL>')).toBeNull();
    });
    it('returns error for empty string', () => {
      expect(input.getEmailValidationMessage('')).toBe('Email cannot be empty');
    });
    it('returns error for whitespace string', () => {
      expect(input.getEmailValidationMessage('   ')).toBe(
        'Email cannot be empty',
      );
    });
    it('returns error for email with spaces', () => {
      expect(input.getEmailValidationMessage('test @example.com')).toBe(
        'Email cannot include spaces',
      );
    });
    it('returns error for invalid email', () => {
      expect(input.getEmailValidationMessage('test@.com')).toBe(
        'Enter a valid email address',
      );
    });
    it('returns null for null input', () => {
      expect(input.getEmailValidationMessage(null as any)).toBeNull();
    });
  });

  describe('getPasswordValidationMessage', () => {
    it('returns null for valid password', () => {
      expect(input.getPasswordValidationMessage('abcdefgh')).toBeNull();
      expect(input.getPasswordValidationMessage('12345678')).toBeNull();
      expect(input.getPasswordValidationMessage('password!')).toBeNull();
    });
    it('returns error for empty password', () => {
      expect(input.getPasswordValidationMessage('')).toBe(
        'Password cannot be empty',
      );
    });
    it('returns error for password with spaces', () => {
      expect(input.getPasswordValidationMessage('abc defgh')).toBe(
        'Password cannot include spaces',
      );
    });
    it('returns error for short password', () => {
      expect(input.getPasswordValidationMessage('abc')).toBe(
        'Password needs to be at least 8 characters',
      );
      expect(input.getPasswordValidationMessage('1234567')).toBe(
        'Password needs to be at least 8 characters',
      );
    });
  });

  describe('isRequiredValid', () => {
    it('returns true for non-empty string', () => {
      expect(input.isRequiredValid('hello')).toBe(true);
    });
    it('returns false for empty string', () => {
      expect(input.isRequiredValid('')).toBe(false);
      expect(input.isRequiredValid('   ')).toBe(false);
    });
    it('returns false for null/undefined', () => {
      expect(input.isRequiredValid(null)).toBe(false);
      expect(input.isRequiredValid(undefined)).toBe(false);
    });
    it('returns true for non-empty array', () => {
      expect(input.isRequiredValid([1, 2, 3])).toBe(true);
    });
    it('returns false for empty array', () => {
      expect(input.isRequiredValid([])).toBe(false);
    });
    it('returns true for non-empty object', () => {
      expect(input.isRequiredValid({ a: 1 })).toBe(true);
    });
    it('returns false for empty object', () => {
      expect(input.isRequiredValid({})).toBe(false);
    });
  });

  describe('isMinLengthValid', () => {
    it('returns true if string meets min length', () => {
      expect(input.isMinLengthValid('abc', 2)).toBe(true);
      expect(input.isMinLengthValid('abc', 3)).toBe(true);
    });
    it('returns false if string is too short', () => {
      expect(input.isMinLengthValid('ab', 3)).toBe(false);
    });
  });

  describe('isArrayMinLengthValid', () => {
    it('returns true if array meets min length', () => {
      expect(input.isArrayMinLengthValid([1, 2, 3], 2)).toBe(true);
      expect(input.isArrayMinLengthValid([1, 2], 2)).toBe(true);
    });
    it('returns false if array is too short', () => {
      expect(input.isArrayMinLengthValid([1], 2)).toBe(false);
      expect(input.isArrayMinLengthValid([], 1)).toBe(false);
    });
  });

  describe('isMaxLengthValid', () => {
    it('returns true if string is within max length', () => {
      expect(input.isMaxLengthValid('abc', 3)).toBe(true);
      expect(input.isMaxLengthValid('ab', 3)).toBe(true);
    });
    it('returns false if string is too long', () => {
      expect(input.isMaxLengthValid('abcd', 3)).toBe(false);
    });
  });

  describe('isRegexValid', () => {
    it('returns true if string matches pattern', () => {
      expect(input.isRegexValid('abc123', '^[a-z0-9]+$')).toBe(true);
      expect(input.isRegexValid('123', '^\\d+$')).toBe(true);
    });
    it('returns false if string does not match pattern', () => {
      expect(input.isRegexValid('abc!', '^[a-z0-9]+$')).toBe(false);
      expect(input.isRegexValid('abc', '^\\d+$')).toBe(false);
    });
  });

  describe('isPhoneNumberValid', () => {
    it('returns true for valid phone numbers', () => {
      expect(input.isPhoneNumberValid('1234567890')).toBe(true);
      expect(input.isPhoneNumberValid('0123456789')).toBe(true);
    });
    it('returns false for phone numbers with letters or symbols', () => {
      expect(input.isPhoneNumberValid('************')).toBe(false);
      expect(input.isPhoneNumberValid('phone')).toBe(false);
      expect(input.isPhoneNumberValid('************')).toBe(false);
      expect(input.isPhoneNumberValid('')).toBe(false);
      expect(input.isPhoneNumberValid('   ')).toBe(false);
    });
  });

  describe('isEmailValid', () => {
    it('returns true for valid emails', () => {
      expect(input.isEmailValid('<EMAIL>')).toBe(true);
    });
    it('returns false for invalid emails', () => {
      expect(input.isEmailValid('test@')).toBe(false);
      expect(input.isEmailValid('')).toBe(false);
      expect(input.isEmailValid('   ')).toBe(false);
    });
  });

  describe('isSSNValid', () => {
    it('returns true for valid SSN', () => {
      expect(input.isSSNValid('***********')).toBe(true);
    });
    it('returns false for invalid SSN', () => {
      expect(input.isSSNValid('123456789')).toBe(false);
      expect(input.isSSNValid('123-45-678')).toBe(false);
      expect(input.isSSNValid('')).toBe(false);
      expect(input.isSSNValid('   ')).toBe(false);
      expect(input.isSSNValid('abc-de-ghij')).toBe(false);
    });
  });

  describe('isFileSizeValid', () => {
    it('returns true if file size is within limit', () => {
      expect(input.isFileSizeValid(2, 5)).toBe(true);
      expect(input.isFileSizeValid(5, 5)).toBe(true);
    });
    it('returns false if file size exceeds limit', () => {
      expect(input.isFileSizeValid(6, 5)).toBe(false);
    });
  });

  describe('isFileTypeAllowed', () => {
    it('returns true for allowed file types', () => {
      expect(input.isFileTypeAllowed('photo.jpg', ['jpg', 'png'])).toBe(true);
      expect(input.isFileTypeAllowed('document.PDF', ['pdf', 'doc'])).toBe(
        true,
      );
      expect(input.isFileTypeAllowed('archive.tar.gz', ['gz', 'zip'])).toBe(
        true,
      );
    });
    it('returns false for disallowed file types', () => {
      expect(input.isFileTypeAllowed('photo.bmp', ['jpg', 'png'])).toBe(false);
      expect(input.isFileTypeAllowed('file', ['txt'])).toBe(false);
      expect(input.isFileTypeAllowed('file.', ['txt'])).toBe(false);
    });
    it('returns false for no extension', () => {
      expect(input.isFileTypeAllowed('file', ['txt'])).toBe(false);
    });
    it('returns false for empty allowedExtensions', () => {
      expect(input.isFileTypeAllowed('photo.jpg', [])).toBe(false);
    });
  });
});
