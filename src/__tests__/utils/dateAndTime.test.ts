import {
  getDeviceLocale,
  getDeviceTimeZone,
  getFormattedTime,
  getFormattedTimeWithoutTimeZone,
  formatTimeRemaining,
  getTimeOfDay,
  getFormattedTimeUsingOffset,
  ONE_SECOND_IN_MS,
} from '~/utils/dateAndTime';

// Mock luxon
jest.mock('luxon', () => ({
  DateTime: {
    fromISO: jest.fn(),
  },
}));

// Mock Intl.DateTimeFormat
const mockIntl = {
  DateTimeFormat: jest.fn(),
};

Object.defineProperty(global, 'Intl', {
  value: mockIntl,
  writable: true,
});

describe('dateAndTime utilities', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getDeviceLocale', () => {
    it('returns device locale when available', () => {
      mockIntl.DateTimeFormat.mockReturnValue({
        resolvedOptions: () => ({ locale: 'en-US' }),
      });

      const result = getDeviceLocale();
      expect(result).toBe('en-US');
    });

    it('returns en-US as fallback when error occurs', () => {
      mockIntl.DateTimeFormat.mockImplementation(() => {
        throw new Error('Intl not supported');
      });

      const consoleSpy = jest
        .spyOn(console, 'error')
        .mockImplementation(() => {});
      const result = getDeviceLocale();

      expect(result).toBe('en-US');
      expect(consoleSpy).toHaveBeenCalledWith(
        'Error getting device locale:',
        expect.any(Error),
      );
      consoleSpy.mockRestore();
    });
  });

  describe('getDeviceTimeZone', () => {
    it('returns device timezone when available', () => {
      mockIntl.DateTimeFormat.mockReturnValue({
        resolvedOptions: () => ({ timeZone: 'America/New_York' }),
      });

      const result = getDeviceTimeZone();
      expect(result).toBe('America/New_York');
    });

    it('returns UTC as fallback when error occurs', () => {
      mockIntl.DateTimeFormat.mockImplementation(() => {
        throw new Error('Intl not supported');
      });

      const consoleSpy = jest
        .spyOn(console, 'error')
        .mockImplementation(() => {});
      const result = getDeviceTimeZone();

      expect(result).toBe('UTC');
      expect(consoleSpy).toHaveBeenCalledWith(
        'dateAndTime.ts: getDeviceTimeZone(): Error getting device time zone:',
        expect.any(Error),
      );
      consoleSpy.mockRestore();
    });
  });

  describe('getFormattedTime', () => {
    const { DateTime } = require('luxon');

    beforeEach(() => {
      // Mock device locale and timezone
      mockIntl.DateTimeFormat.mockReturnValue({
        resolvedOptions: () => ({
          locale: 'en-US',
          timeZone: 'America/New_York',
        }),
      });
    });

    it('formats time with timezone when withTimeZone is true', () => {
      const mockDateTime = {
        isValid: true,
        toFormat: jest.fn().mockReturnValue('2:30 PM EST'),
      };
      DateTime.fromISO.mockReturnValue(mockDateTime);

      const result = getFormattedTime('2023-12-25T14:30:00.000Z');
      expect(result).toBe('2:30 PM EST');
    });

    it('formats time without timezone when withTimeZone is false', () => {
      const mockDateTime = {
        isValid: true,
        toFormat: jest.fn().mockReturnValue('2:30 PM'),
      };
      DateTime.fromISO.mockReturnValue(mockDateTime);

      const result = getFormattedTime('2023-12-25T14:30:00.000Z', false);
      expect(result).toBe('2:30 PM');
    });

    it('returns invalid date message for null input', () => {
      const result = getFormattedTime(null);
      expect(result).toBe('Invalid date');
    });

    it('returns invalid date message for undefined input', () => {
      const result = getFormattedTime(undefined);
      expect(result).toBe('Invalid date');
    });

    it('returns invalid date message for empty string', () => {
      const result = getFormattedTime('');
      expect(result).toBe('Invalid date');
    });

    it('returns invalid date message for invalid date string', () => {
      const mockDateTime = {
        isValid: false,
      };
      DateTime.fromISO.mockReturnValue(mockDateTime);

      const consoleSpy = jest
        .spyOn(console, 'warn')
        .mockImplementation(() => {});
      const result = getFormattedTime('invalid-date-string');

      expect(result).toBe('Invalid date');
      expect(consoleSpy).toHaveBeenCalledWith(
        'Invalid date string: invalid-date-string',
      );
      consoleSpy.mockRestore();
    });

    it('handles DateTime parsing errors gracefully', () => {
      DateTime.fromISO.mockImplementation(() => {
        throw new Error('Parsing error');
      });

      const consoleSpy = jest
        .spyOn(console, 'error')
        .mockImplementation(() => {});

      const result = getFormattedTime('2023-12-25T14:30:00.000Z');
      expect(result).toBe('Invalid date');
      expect(consoleSpy).toHaveBeenCalledWith(
        'Error formatting time:',
        expect.any(Error),
      );
      consoleSpy.mockRestore();
    });
  });

  describe('getFormattedTimeWithoutTimeZone', () => {
    it('calls getFormattedTime with withTimeZone set to false', () => {
      const { DateTime } = require('luxon');
      const mockDateTime = {
        isValid: true,
        toFormat: jest.fn().mockReturnValue('2:30 PM'),
      };
      DateTime.fromISO.mockReturnValue(mockDateTime);

      const result = getFormattedTimeWithoutTimeZone(
        '2023-12-25T14:30:00.000Z',
      );
      expect(result).toBe('2:30 PM');
    });
  });

  describe('formatTimeRemaining', () => {
    it('formats seconds correctly', () => {
      expect(formatTimeRemaining(65)).toBe('01:05');
      expect(formatTimeRemaining(125)).toBe('02:05');
      expect(formatTimeRemaining(0)).toBe('00:00');
    });

    it('pads single digits with zeros', () => {
      expect(formatTimeRemaining(5)).toBe('00:05');
      expect(formatTimeRemaining(360)).toBe('06:00');
    });

    it('returns 00:00 for negative values', () => {
      expect(formatTimeRemaining(-10)).toBe('00:00');
    });

    it('returns 00:00 for undefined input', () => {
      expect(formatTimeRemaining(undefined)).toBe('00:00');
    });

    it('returns 00:00 for null input', () => {
      expect(formatTimeRemaining(null as any)).toBe('00:00');
    });

    it('handles large values correctly', () => {
      expect(formatTimeRemaining(3661)).toBe('61:01'); // 1 hour, 1 minute, 1 second
    });
  });

  describe('getTimeOfDay', () => {
    beforeEach(() => {
      jest.useFakeTimers();
    });

    afterEach(() => {
      jest.useRealTimers();
    });

    it('returns morning for hours 5-11', () => {
      // Set time to 8 AM
      jest.setSystemTime(new Date(2023, 0, 1, 8, 0, 0));
      expect(getTimeOfDay()).toBe('morning');
    });

    it('returns afternoon for hours 12-16', () => {
      // Set time to 2 PM
      jest.setSystemTime(new Date(2023, 0, 1, 14, 0, 0));
      expect(getTimeOfDay()).toBe('afternoon');
    });

    it('returns evening for hours 17-23 and 0-4', () => {
      // Set time to 8 PM
      jest.setSystemTime(new Date(2023, 0, 1, 20, 0, 0));
      expect(getTimeOfDay()).toBe('evening');

      // Set time to 2 AM
      jest.setSystemTime(new Date(2023, 0, 1, 2, 0, 0));
      expect(getTimeOfDay()).toBe('evening');
    });
  });

  describe('getFormattedTimeUsingOffset', () => {
    it('formats time with positive offset', async () => {
      const result = await getFormattedTimeUsingOffset(
        '2023-12-25T14:30:00.000Z',
        5,
      );
      expect(result).toMatch(/^\d{1,2}:\d{2} (AM|PM)$/);
    });

    it('formats time with negative offset', async () => {
      const result = await getFormattedTimeUsingOffset(
        '2023-12-25T14:30:00.000Z',
        -3,
      );
      expect(result).toMatch(/^\d{1,2}:\d{2} (AM|PM)$/);
    });

    it('handles midnight correctly', async () => {
      const result = await getFormattedTimeUsingOffset(
        '2023-12-25T00:00:00.000Z',
        0,
      );
      expect(result).toBe('12:00 AM');
    });

    it('handles noon correctly', async () => {
      const result = await getFormattedTimeUsingOffset(
        '2023-12-25T12:00:00.000Z',
        0,
      );
      expect(result).toBe('12:00 PM');
    });

    it('returns N/A for null input', async () => {
      const result = await getFormattedTimeUsingOffset(null, 0);
      expect(result).toBe('N/A');
    });

    it('returns N/A for undefined input', async () => {
      const result = await getFormattedTimeUsingOffset(undefined, 0);
      expect(result).toBe('N/A');
    });

    it('returns N/A for empty string', async () => {
      const result = await getFormattedTimeUsingOffset('', 0);
      expect(result).toBe('N/A');
    });

    it('handles numeric input by converting to string', async () => {
      // Convert timestamp to ISO string format that parseDate expects
      const timestamp = 1703520000000;
      const date = new Date(timestamp);
      const isoString = date.toISOString();

      const result = await getFormattedTimeUsingOffset(isoString, 0);
      expect(result).toMatch(/^\d{1,2}:\d{2} (AM|PM)$/);
    });

    it('pads minutes with zero when needed', async () => {
      const result = await getFormattedTimeUsingOffset(
        '2023-12-25T14:05:00.000Z',
        0,
      );
      expect(result).toBe('2:05 PM');
    });
  });

  describe('constants', () => {
    it('exports ONE_SECOND_IN_MS constant', () => {
      expect(ONE_SECOND_IN_MS).toBe(1000);
    });
  });
});
