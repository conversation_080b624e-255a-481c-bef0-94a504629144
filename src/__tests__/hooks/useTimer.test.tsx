import { renderHook } from '@testing-library/react-native';
import { useTimer } from '~/hooks/useTimer';

describe('useTimer', () => {
  beforeEach(() => {
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.clearAllTimers();
    jest.useRealTimers();
  });

  afterAll(() => {
    jest.useRealTimers();
  });

  it('should not start timer when isRunning is false', () => {
    const onTick = jest.fn();
    renderHook(() =>
      useTimer({
        isRunning: false,
        onTick,
      }),
    );

    jest.advanceTimersByTime(5000);
    expect(onTick).not.toHaveBeenCalled();
  });

  it('should start timer when isRunning is true', () => {
    const onTick = jest.fn();
    renderHook(() =>
      useTimer({
        isRunning: true,
        onTick,
      }),
    );

    jest.advanceTimersByTime(1000);
    expect(onTick).toHaveBeenCalledTimes(1);
  });

  it('should call onTick at specified interval', () => {
    const onTick = jest.fn();
    renderHook(() =>
      useTimer({
        isRunning: true,
        onTick,
        interval: 500,
      }),
    );

    jest.advanceTimersByTime(2000);
    expect(onTick).toHaveBeenCalledTimes(4);
  });

  it('should clear interval when component unmounts', () => {
    const onTick = jest.fn();
    const { unmount } = renderHook(() =>
      useTimer({
        isRunning: true,
        onTick,
      }),
    );

    unmount();
    jest.advanceTimersByTime(1000);
    expect(onTick).not.toHaveBeenCalled();
  });

  it('should clear old interval and create new one when interval changes', () => {
    const onTick = jest.fn();
    const { rerender } = renderHook(
      ({ interval }) =>
        useTimer({
          isRunning: true,
          onTick,
          interval,
        }),
      { initialProps: { interval: 1000 } },
    );

    jest.advanceTimersByTime(1000);
    expect(onTick).toHaveBeenCalledTimes(1);

    rerender({ interval: 500 });
    jest.advanceTimersByTime(1000);
    expect(onTick).toHaveBeenCalledTimes(3); // 2 more calls at 500ms interval
  });

  it('should clear interval when isRunning changes to false', () => {
    const onTick = jest.fn();
    const { rerender } = renderHook(
      ({ isRunning }) =>
        useTimer({
          isRunning,
          onTick,
        }),
      { initialProps: { isRunning: true } },
    );

    jest.advanceTimersByTime(1000);
    expect(onTick).toHaveBeenCalledTimes(1);

    rerender({ isRunning: false });
    jest.advanceTimersByTime(1000);
    expect(onTick).toHaveBeenCalledTimes(1); // No additional calls
  });

  it('should use default interval of 1000ms when not specified', () => {
    const onTick = jest.fn();
    renderHook(() =>
      useTimer({
        isRunning: true,
        onTick,
      }),
    );

    jest.advanceTimersByTime(2000);
    expect(onTick).toHaveBeenCalledTimes(2);
  });
});
