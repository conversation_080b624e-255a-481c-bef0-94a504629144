import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import InfoItem from '~/components/info/InfoItem';
import Clipboard from '@react-native-clipboard/clipboard';
import LocationPinOutlined from '~/components/icons/LocationPinOutlined';
import Notes from '~/components/icons/Notes';

// Mock Clipboard
jest.mock('@react-native-clipboard/clipboard', () => ({
  setString: jest.fn(),
}));

// Mock useToast
const mockShowToast = jest.fn();
jest.mock('~/components/toast/ToastProvider', () => ({
  useToast: () => ({
    showToast: mockShowToast,
  }),
}));

describe('InfoItem', () => {
  const addressProps = {
    title: 'Delivery Address',
    infoText: '123 Main Street, NY',
    icon: <LocationPinOutlined />,
  };

  const notesProps = {
    title: 'Dropoff Notes',
    infoText: 'Leave at the front door.',
    icon: <Notes />,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders title and infoText correctly', () => {
    const { getByTestId } = render(<InfoItem {...addressProps} />);

    expect(getByTestId('InfoItem.Title.Label').props.children).toBe(
      addressProps.title,
    );
    expect(getByTestId('InfoItem.Info.text').props.children).toBe(
      addressProps.infoText,
    );
  });

  it('copies text to clipboard and shows toast when enableCopy is true', () => {
    const { getByTestId } = render(
      <InfoItem {...addressProps} enableCopy={true} />,
    );

    fireEvent.press(getByTestId('InfoItem.Root'));

    expect(Clipboard.setString).toHaveBeenCalledWith(addressProps.infoText);
    expect(mockShowToast).toHaveBeenCalledWith(
      expect.objectContaining({
        type: 'success',
        message: expect.any(String),
        duration: 3000,
      }),
    );
  });

  it('does not copy to clipboard or show toast when enableCopy is false', () => {
    const { getByTestId } = render(
      <InfoItem {...notesProps} enableCopy={false} />,
    );

    fireEvent.press(getByTestId('InfoItem.Root'));

    expect(Clipboard.setString).not.toHaveBeenCalled();
    expect(mockShowToast).not.toHaveBeenCalled();
  });

  it('renders icon properly', () => {
    const { getByTestId } = render(<InfoItem {...addressProps} />);
    expect(getByTestId('InfoItem.Title.icon')).toBeTruthy();
  });
});
