import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import FilledButton from '~/components/buttons/FilledButton';
import colors from '~/styles/colors';
import { Text } from 'react-native';

describe('FilledButton', () => {
  const defaultProps = {
    id: 'test-button',
    title: 'Test Button',
    onClick: jest.fn(),
    style: null,
    color: 'primary' as const,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders correctly with primary color', () => {
    const { getByTestId, getByText } = render(
      <FilledButton {...defaultProps} />,
    );

    const button = getByTestId('test-button');
    expect(button).toBeTruthy();
    expect(getByText('Test Button')).toBeTruthy();
  });

  it('renders correctly with secondary color', () => {
    const { getByTestId, getByText } = render(
      <FilledButton {...defaultProps} color="secondary" />,
    );

    const button = getByTestId('test-button');
    expect(button).toBeTruthy();
    expect(getByText('Test Button')).toBeTruthy();
  });

  it('calls onClick handler when pressed', () => {
    const { getByTestId } = render(<FilledButton {...defaultProps} />);

    fireEvent.press(getByTestId('test-button'));
    expect(defaultProps.onClick).toHaveBeenCalledTimes(1);
  });

  it('renders in disabled state', () => {
    const { getByTestId } = render(
      <FilledButton {...defaultProps} isDisabled={true} />,
    );

    const button = getByTestId('test-button');
    expect(button.props.accessibilityState.disabled).toBe(true);
  });

  it('applies custom style to button', () => {
    const customStyle = { backgroundColor: 'purple' };
    const { getByTestId } = render(
      <FilledButton {...defaultProps} style={customStyle} />,
    );

    const button = getByTestId('test-button');
    expect(button).toBeTruthy();
  });

  it('applies custom container style', () => {
    const customContainerStyle = { margin: 20 };
    const { getByTestId } = render(
      <FilledButton {...defaultProps} containerStyle={customContainerStyle} />,
    );

    const button = getByTestId('test-button');
    expect(button).toBeTruthy();
  });

  it('applies custom text color', () => {
    const customTextColor = colors.grey600;
    const { getByText } = render(
      <FilledButton
        {...defaultProps}
        textColor={customTextColor}
        color="primary"
      />,
    );

    const buttonText = getByText('Test Button');
    expect(buttonText).toBeTruthy();
  });

  it('renders with left icon', () => {
    const leftIcon = <Text testID="left-icon">L</Text>;
    const { getByTestId } = render(
      <FilledButton {...defaultProps} iconLeft={leftIcon} />,
    );

    expect(getByTestId('left-icon')).toBeTruthy();
  });

  it('renders with right icon', () => {
    const rightIcon = <Text testID="right-icon">R</Text>;
    const { getByTestId } = render(
      <FilledButton {...defaultProps} iconRight={rightIcon} />,
    );

    expect(getByTestId('right-icon')).toBeTruthy();
  });

  it('applies different margin when icons are present', () => {
    const leftIcon = <Text testID="left-icon">L</Text>;
    const rightIcon = <Text testID="right-icon">R</Text>;
    const { getByText } = render(
      <FilledButton
        {...defaultProps}
        iconLeft={leftIcon}
        iconRight={rightIcon}
      />,
    );

    const buttonText = getByText('Test Button');
    expect(buttonText).toBeTruthy();
  });

  it('handles numberOfLines and ellipsizeMode for text', () => {
    const { getByText } = render(<FilledButton {...defaultProps} />);

    const buttonText = getByText('Test Button');
    expect(buttonText.props.numberOfLines).toBe(1);
    expect(buttonText.props.ellipsizeMode).toBe('tail');
  });
});
