import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import OutlinedButton from '~/components/buttons/OutlinedButton';
import colors from '~/styles/colors';

describe('OutlinedButton', () => {
  const defaultProps = {
    title: 'Test Button',
    onClick: jest.fn(),
    style: null,
    color: 'primary',
    isDisabled: false,
    isLoading: false,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders correctly with primary color', () => {
    const { getByText } = render(<OutlinedButton {...defaultProps} />);
    expect(getByText('Test Button')).toBeTruthy();
  });

  it('renders correctly with secondary color', () => {
    const { getByText } = render(
      <OutlinedButton {...defaultProps} color="secondary" />,
    );
    expect(getByText('Test Button')).toBeTruthy();
  });

  it('calls onClick handler when pressed', () => {
    const { getByText } = render(<OutlinedButton {...defaultProps} />);
    fireEvent.press(getByText('Test Button'));
    expect(defaultProps.onClick).toHaveBeenCalledTimes(1);
  });

  it('renders in disabled state', () => {
    const { getByTestId } = render(
      <OutlinedButton {...defaultProps} isDisabled={true} />,
    );
    const button = getByTestId('button');
    expect(button.props.accessibilityState.disabled).toBe(true);
  });

  it('renders in loading state', () => {
    const { getByTestId } = render(
      <OutlinedButton {...defaultProps} isLoading={true} />,
    );
    const button = getByTestId('button');
    expect(button.props.accessibilityState.busy).toBe(true);
  });

  it('applies custom style to button container', () => {
    const customStyle = { margin: 20 };
    const { getByTestId } = render(
      <OutlinedButton {...defaultProps} style={customStyle} />,
    );
    const buttonWrapper = getByTestId('RNE_BUTTON_WRAPPER');
    const styles = buttonWrapper.props.style;
    expect(styles[2]).toEqual(
      expect.arrayContaining([expect.objectContaining(customStyle)]),
    );
  });

  it('has correct border color for primary button', () => {
    const { getByTestId } = render(
      <OutlinedButton {...defaultProps} color="primary" />,
    );
    const button = getByTestId('button');
    const buttonStyle = button.props.children[0].props.style;
    expect(buttonStyle).toEqual(
      expect.objectContaining({
        borderColor: colors.red600,
      }),
    );
  });

  it('has correct text color for primary button', () => {
    const { getByText } = render(
      <OutlinedButton {...defaultProps} color="primary" />,
    );
    const buttonText = getByText('Test Button');
    expect(buttonText.props.style).toEqual(
      expect.objectContaining({
        color: colors.red600,
      }),
    );
  });

  it('has correct border color for secondary button', () => {
    const { getByTestId } = render(
      <OutlinedButton {...defaultProps} color="secondary" />,
    );
    const button = getByTestId('button');
    const buttonStyle = button.props.children[0].props.style;
    expect(buttonStyle).toEqual(
      expect.objectContaining({
        borderColor: colors.grey600,
      }),
    );
  });

  it('has correct text color for secondary button', () => {
    const { getByText } = render(
      <OutlinedButton {...defaultProps} color="secondary" />,
    );
    const buttonText = getByText('Test Button');
    expect(buttonText.props.style).toEqual(
      expect.objectContaining({
        color: colors.grey600,
      }),
    );
  });

  it('does not call onClick when disabled is true', () => {
    const { getByText } = render(
      <OutlinedButton {...defaultProps} isDisabled={true} />,
    );
    fireEvent.press(getByText('Test Button'));
    expect(defaultProps.onClick).not.toHaveBeenCalled();
  });
});
