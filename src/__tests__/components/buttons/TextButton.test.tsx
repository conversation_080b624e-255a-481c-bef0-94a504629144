import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import TextButton from '~/components/buttons/TextButton';
import { StyleSheet } from 'react-native';

describe('TextButton Component', () => {
  const mockTitle = 'Test Button';
  const mockOnClick = jest.fn();
  const mockStyle = StyleSheet.create({
    customStyle: {
      padding: 10,
    },
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders correctly with required props', () => {
    const { getByText } = render(
      <TextButton title={mockTitle} onClick={mockOnClick} isDisabled={false} />,
    );

    expect(getByText(mockTitle)).toBeTruthy();
  });

  it('calls onClick handler when pressed', () => {
    const { getByText } = render(
      <TextButton title={mockTitle} onClick={mockOnClick} isDisabled={false} />,
    );

    fireEvent.press(getByText(mockTitle));
    expect(mockOnClick).toHaveBeenCalled();
  });

  it('does not call onClick handler when disabled', () => {
    const { getByText } = render(
      <TextButton title={mockTitle} onClick={mockOnClick} isDisabled={true} />,
    );

    const button = getByText(mockTitle).parent;
    if (!button) {
      throw new Error('Button parent not found');
    }
    fireEvent.press(button);
    expect(mockOnClick).not.toHaveBeenCalled();
  });

  it('applies custom style when provided', () => {
    const { getByTestId } = render(
      <TextButton
        title={mockTitle}
        onClick={mockOnClick}
        isDisabled={false}
        style={mockStyle.customStyle}
        testID="text-button"
      />,
    );

    const touchable = getByTestId('text-button');
    expect(touchable.props.style).toEqual(
      expect.objectContaining(mockStyle.customStyle),
    );
  });

  it('applies red text color by default', () => {
    const { getByText } = render(
      <TextButton title={mockTitle} onClick={mockOnClick} isDisabled={false} />,
    );

    const text = getByText(mockTitle);
    expect(text.props.style).toEqual(
      expect.objectContaining({
        color: '#e34b4b',
      }),
    );
  });
});
