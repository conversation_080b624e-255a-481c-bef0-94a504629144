import React from 'react';
import { render, fireEvent, act } from '@testing-library/react-native';
import BottomSheetModalWithRadioButton, {
  BottomSheetItemType,
} from '~/components/modals/BottomSheetWithRadioButtonsList';
import { AddParcelProvider } from '~/screens/parcel/AddParcelProvider';
import { Stop } from '~/types/stops.types';

// Mock Realm and ParcelManager
jest.mock('~/services/sync/parcelManager', () => ({
  ParcelManager: {
    createParcel: jest.fn().mockResolvedValue({
      success: true,
      parcelId: 'test-id',
    }),
  },
}));

// Mock the Realm database
const mockWrite = jest.fn(cb => cb());
const mockRealm = {
  write: mockWrite,
  create: jest.fn(),
  objects: jest.fn(() => ({
    filtered: jest.fn(() => []),
  })),
  close: jest.fn(),
};

jest.mock('~/db/realm/utils/realm.config', () =>
  jest.fn().mockResolvedValue(mockRealm),
);

jest.mock('react-native-modal', () => {
  return ({ children }: { children: React.ReactNode }) => <>{children}</>;
});

jest.mock('react-native-safe-area-context', () => ({
  useSafeAreaInsets: jest.fn().mockReturnValue({ bottom: 10 }),
}));

jest.mock('~/assets/icons', () => ({
  SortIcon: function MockSortIcon() {
    return 'MockSortIcon';
  },
}));

describe('BottomSheetModalWithRadioButton', () => {
  beforeEach(() => {
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.clearAllTimers();
    jest.useRealTimers();
    mockRealm.close();
  });

  afterAll(() => {
    jest.useRealTimers();
    mockRealm.close();
  });

  const mockItems: BottomSheetItemType[] = [
    { id: '1', title: 'Item 1', subtitle: 'Subtitle 1', rightText: 'Right 1' },
    { id: '2', title: 'Item 2', subtitle: 'Subtitle 2', rightText: 'Right 2' },
  ];
  const mockOnClose = jest.fn();
  const mockOnItemSelected = jest.fn();

  // Mock stop data required by AddParcelProvider
  const mockStop1: Stop = {
    Id: 'test-id',
    attributes: {
      type: 'Stop__c',
      url: '/services/data/v50.0/sobjects/Stop__c/test-id',
    },
    LastModifiedDate: new Date().toISOString(),
    Name: 'Test Stop',
    Stop_Time_Min__c: null,
    Stop_Time_Preferred__c: null,
    Stop_Time_Max__c: null,
    ETA__c: null,
    Type__c: 'Delivery',
    Address_1__c: '123 Test St',
    Address_2__c: null,
    City__c: 'Test City',
    Postal_Code__c: '12345',
    State__c: 'TS',
    Status__c: 'SCHEDULED',
    Stop_Coordinates__Latitude__s: 0,
    Stop_Coordinates__Longitude__s: 0,
    Notes__c: null,
    Notes_Other__c: null,
    POD__c: null,
    POD_Comments__c: null,
    Customer_Reference_1__c: null,
    Customer_Reference_2__c: null,
    Pieces__c: 0,
    Summary__c: 'Test Summary',
    Arrival_Time__c: null,
    Completed_Time__c: null,
    Geofencing_Distance__c: null,
    Location__r: {
      attributes: {
        type: 'Location__c',
        url: '/services/data/v50.0/sobjects/Location__c/test-loc',
      },
      Arrival_Verification_Method__c: null,
      Name: 'Test Location',
      Coordinates__Latitude__s: 0,
      Coordinates__Longitude__s: 0,
      Driver_Wait_Time_Duration__c: null,
      Geofencing_Distance__c: null,
      Business_Hours__c: null,
    },
    Coordinates__c: '0,0',
    Wait_Time_Minutes__c: null,
    SNO_Bypass_DateTime__c: null,
    SNO_Driver_Waiting__c: false,
    SNO_Tags__c: null,
    No_Perimeter_Check_Notes__c: null,
    Pickup_Contact__c: null,
    Pickup_Comments__c: null,
    Delivery_Contact__c: null,
    Delivery_Comments__c: null,
    Driver_Initiated_Early_Departure__c: false,
    Service_Comments__c: '',
  };

  const mockStop2: Stop = {
    ...mockStop1,
    Id: 'test-id-2',
    attributes: {
      type: 'Stop__c',
      url: '/services/data/v50.0/sobjects/Stop__c/test-id-2',
    },
    Summary__c: 'Test Summary 2',
    Arrival_Time__c: null,
    Completed_Time__c: null,
    Geofencing_Distance__c: null,
    Location__r: {
      attributes: {
        type: 'Location__c',
        url: '/services/data/v50.0/sobjects/Location__c/test-loc',
      },
      Arrival_Verification_Method__c: null,
      Name: 'Test Location 2',
      Coordinates__Latitude__s: 0.1,
      Coordinates__Longitude__s: 0.1,
      Driver_Wait_Time_Duration__c: null,
      Geofencing_Distance__c: null,
      Business_Hours__c: null,
    },
  };

  const mockStopList = [mockStop1, mockStop2];

  const renderWithProvider = (ui: React.ReactElement) => {
    return render(
      <AddParcelProvider stop={mockStop1} stopList={mockStopList}>
        {ui}
      </AddParcelProvider>,
    );
  };

  it('renders correctly when visible', () => {
    const { getByText } = renderWithProvider(
      <BottomSheetModalWithRadioButton
        items={mockItems}
        selectedId={null}
        isVisible={true}
        title="Select an Item"
        onClose={mockOnClose}
        onItemSelected={mockOnItemSelected}
      />,
    );

    expect(getByText('Select an Item')).toBeTruthy();
    expect(getByText('Item 1')).toBeTruthy();
    expect(getByText('Subtitle 1')).toBeTruthy();
    expect(getByText('Right 1')).toBeTruthy();
  });

  it('calls onItemSelected when an item is selected', async () => {
    const { getByText } = renderWithProvider(
      <BottomSheetModalWithRadioButton
        items={mockItems}
        selectedId={null}
        isVisible={true}
        title="Select an Item"
        onClose={mockOnClose}
        onItemSelected={mockOnItemSelected}
      />,
    );

    await act(async () => {
      fireEvent.press(getByText('Item 1'));
    });

    expect(mockOnItemSelected).toHaveBeenCalledWith('1');
  });

  it('displays sorting button when enabled', () => {
    const { getByTestId } = renderWithProvider(
      <BottomSheetModalWithRadioButton
        items={mockItems}
        selectedId={null}
        isVisible={true}
        title="Select an Item"
        showSortButton={true}
        onClose={mockOnClose}
        onItemSelected={mockOnItemSelected}
      />,
    );

    expect(getByTestId('sort-action-button')).toBeTruthy();
  });

  it('sorts items when sorting option is selected', async () => {
    const { getByTestId, getByText, rerender } = renderWithProvider(
      <BottomSheetModalWithRadioButton
        items={mockItems}
        selectedId={null}
        isVisible={true}
        title="Select an Item"
        showSortButton={true}
        onClose={mockOnClose}
        onItemSelected={mockOnItemSelected}
      />,
    );

    await act(async () => {
      fireEvent.press(getByTestId('sort-action-button'));
    });

    rerender(
      <AddParcelProvider stop={mockStop1} stopList={mockStopList}>
        <BottomSheetModalWithRadioButton
          items={[...mockItems].reverse()}
          selectedId={null}
          isVisible={true}
          title="Select an Item"
          showSortButton={true}
          onClose={mockOnClose}
          onItemSelected={mockOnItemSelected}
        />
      </AddParcelProvider>,
    );

    expect(getByText('Item 2')).toBeTruthy();
    expect(getByText('Subtitle 2')).toBeTruthy();
  });
});
