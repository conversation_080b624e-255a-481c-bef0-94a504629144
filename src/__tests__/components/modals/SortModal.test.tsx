import React from 'react';
import { render, fireEvent, act } from '@testing-library/react-native';
import { BottomSheetItemType } from '~/components/modals/BottomSheetWithRadioButtonsList';
import SortModal from '~/components/modals/SortModal';

// Mock react-native-modal to prevent animation-related act warnings
jest.mock('react-native-modal', () => props => {
  return <>{props.isVisible ? props.children : null}</>;
});

jest.mock('react-native-safe-area-context', () => ({
  useSafeAreaInsets: () => ({ bottom: 0 }),
}));

describe('SortModal', () => {
  const mockSortingOptions: BottomSheetItemType[] = [
    {
      id: 'ascending',
      title: 'A to Z',
      subtitle: 'Sorted in alphabetical order',
    },
    {
      id: 'descending',
      title: 'Z to A',
      subtitle: 'Sorted in reverse alphabetical order',
    },
    {
      id: 'next_available_stops',
      title: 'Next available stops',
      subtitle: 'Sorted by closest time to current time',
    },
  ];
  const mockSortingOptionSelected = jest.fn();
  const mockOnBackPressed = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders sorting options correctly', async () => {
    const { getByText } = render(
      <SortModal
        sortingOptions={mockSortingOptions}
        onSelect={mockSortingOptionSelected}
        selectedOptionId="ascending"
        onBackPressed={mockOnBackPressed}
      />,
    );

    await act(async () => {
      expect(getByText('A to Z')).toBeTruthy();
      expect(getByText('Z to A')).toBeTruthy();
      expect(getByText('Next available stops')).toBeTruthy();
    });
  });

  it('calls sortingOptionSelected when an option is selected', async () => {
    const { getByText } = render(
      <SortModal
        sortingOptions={mockSortingOptions}
        onSelect={mockSortingOptionSelected}
        selectedOptionId="ascending"
        onBackPressed={mockOnBackPressed}
      />,
    );

    await act(async () => {
      fireEvent.press(getByText('Z to A'));
    });

    expect(mockSortingOptionSelected).toHaveBeenCalledWith('descending');
  });

  it('calls onBackPressed when back button is pressed', async () => {
    const { getByTestId } = render(
      <SortModal
        sortingOptions={mockSortingOptions}
        onSelect={mockSortingOptionSelected}
        selectedOptionId="ascending"
        onBackPressed={mockOnBackPressed}
      />,
    );

    await act(async () => {
      fireEvent.press(getByTestId('back-button'));
    });

    expect(mockOnBackPressed).toHaveBeenCalled();
  });
});
