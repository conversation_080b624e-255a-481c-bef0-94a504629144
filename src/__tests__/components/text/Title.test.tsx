import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import Title from '~/components/text/Title';
import { Text } from 'react-native';

describe('Title Component', () => {
  const mockIcon = <Text>Icon</Text>;
  const mockTitle = 'Test Title';
  const mockSubtitle = 'Test Subtitle';
  const mockTextButtonTitle = 'Button';
  const mockProgressText = '1/4';

  it('renders correctly with required props', () => {
    const { getByText } = render(<Title title={mockTitle} icon={mockIcon} />);

    expect(getByText(mockTitle)).toBeTruthy();
    expect(getByText('Icon')).toBeTruthy();
  });

  it('renders subtitle when provided', () => {
    const { getByText } = render(
      <Title title={mockTitle} icon={mockIcon} subtitle={mockSubtitle} />,
    );

    expect(getByText(mockSubtitle)).toBeTruthy();
  });

  it('renders text button when textButtonVisible is true', () => {
    const mockOnPress = jest.fn();
    const { getByText } = render(
      <Title
        title={mockTitle}
        icon={mockIcon}
        textButtonVisible={true}
        textButtonTitle={mockTextButtonTitle}
        onTextButtonPress={mockOnPress}
      />,
    );

    const button = getByText(mockTextButtonTitle);
    expect(button).toBeTruthy();

    fireEvent.press(button);
    expect(mockOnPress).toHaveBeenCalled();
  });

  it('renders progress text when showProgressText is true', () => {
    const { getByText } = render(
      <Title
        title={mockTitle}
        icon={mockIcon}
        showProgressText={true}
        progressText={mockProgressText}
      />,
    );

    expect(getByText(mockProgressText)).toBeTruthy();
  });

  it('does not render text button when textButtonVisible is false', () => {
    const { queryByText } = render(
      <Title
        title={mockTitle}
        icon={mockIcon}
        textButtonVisible={false}
        textButtonTitle={mockTextButtonTitle}
      />,
    );

    expect(queryByText(mockTextButtonTitle)).toBeNull();
  });

  it('does not render progress text when showProgressText is false', () => {
    const { queryByText } = render(
      <Title
        title={mockTitle}
        icon={mockIcon}
        showProgressText={false}
        progressText={mockProgressText}
      />,
    );

    expect(queryByText(mockProgressText)).toBeNull();
  });

  it('does not render subtitle when not provided', () => {
    const { queryByText } = render(<Title title={mockTitle} icon={mockIcon} />);

    expect(queryByText(mockSubtitle)).toBeNull();
  });
});
