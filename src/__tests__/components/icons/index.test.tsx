import React from 'react';
import { render } from '@testing-library/react-native';
import * as Icons from '~/components/icons';

describe('Icon Components', () => {
  const iconNames = Object.keys(Icons);

  // Icons that require special props
  const specialIcons = ['CircleBackground', 'MeshBackground'];

  // Regular icons that follow the standard SvgProps interface
  const regularIcons = iconNames.filter(name => !specialIcons.includes(name));

  // Test each regular icon component
  for (const iconName of regularIcons) {
    const IconComponent = Icons[iconName as keyof typeof Icons];

    describe(`${iconName} Icon`, () => {
      it('renders with default props', () => {
        const { getByTestId } = render(
          <IconComponent testID={`${iconName.toLowerCase()}-icon`} />,
        );
        const icon = getByTestId(`${iconName.toLowerCase()}-icon`);
        expect(icon).toBeTruthy();
      });

      it('renders with custom color', () => {
        const customColor = '#FF0000';
        const { getByTestId } = render(
          <IconComponent
            testID={`${iconName.toLowerCase()}-icon`}
            color={customColor}
          />,
        );
        const icon = getByTestId(`${iconName.toLowerCase()}-icon`);
        expect(icon).toBeTruthy();
      });

      it('renders with custom size', () => {
        const customSize = 32;
        const { getByTestId } = render(
          <IconComponent
            testID={`${iconName.toLowerCase()}-icon`}
            size={customSize}
          />,
        );
        const icon = getByTestId(`${iconName.toLowerCase()}-icon`);
        expect(icon).toBeTruthy();
      });

      it('renders with custom color and size', () => {
        const customColor = '#00FF00';
        const customSize = 48;
        const { getByTestId } = render(
          <IconComponent
            testID={`${iconName.toLowerCase()}-icon`}
            color={customColor}
            size={customSize}
          />,
        );
        const icon = getByTestId(`${iconName.toLowerCase()}-icon`);
        expect(icon).toBeTruthy();
      });

      it('accepts additional SVG props', () => {
        const { getByTestId } = render(
          <IconComponent
            testID={`${iconName.toLowerCase()}-icon`}
            accessibilityLabel={`${iconName} icon`}
            accessible={true}
          />,
        );
        const icon = getByTestId(`${iconName.toLowerCase()}-icon`);
        expect(icon).toBeTruthy();
      });
    });
  }

  // Test special icons with their unique props
  describe('Special Icon Tests', () => {
    describe('CircleBackground', () => {
      it('renders with default props', () => {
        const { getByTestId } = render(
          <Icons.CircleBackground>
            <Icons.Arrow testID="arrow-inside" />
          </Icons.CircleBackground>,
        );
        const arrow = getByTestId('arrow-inside');
        expect(arrow).toBeTruthy();
      });

      it('renders with custom colors', () => {
        const { getByTestId } = render(
          <Icons.CircleBackground outlineColor="#FF0000" fillColor="#00FF00">
            <Icons.Arrow testID="arrow-inside" />
          </Icons.CircleBackground>,
        );
        const arrow = getByTestId('arrow-inside');
        expect(arrow).toBeTruthy();
      });
    });

    describe('MeshBackground', () => {
      it('renders with default props', () => {
        const { getByTestId } = render(
          <Icons.MeshBackground>
            <Icons.Arrow testID="arrow-inside" />
          </Icons.MeshBackground>,
        );
        const arrow = getByTestId('arrow-inside');
        expect(arrow).toBeTruthy();
      });

      it('renders with custom color', () => {
        const { getByTestId } = render(
          <Icons.MeshBackground color="#FF0000">
            <Icons.Arrow testID="arrow-inside" />
          </Icons.MeshBackground>,
        );
        const arrow = getByTestId('arrow-inside');
        expect(arrow).toBeTruthy();
      });

      it('renders with children', () => {
        const { getByTestId } = render(
          <Icons.MeshBackground>
            <Icons.Arrow testID="arrow-inside" />
          </Icons.MeshBackground>,
        );
        const arrow = getByTestId('arrow-inside');
        expect(arrow).toBeTruthy();
      });
    });
  });

  // Test specific icons with unique default sizes
  describe('Specific Icon Tests', () => {
    it('CloudSlashFilled renders with custom default size', () => {
      const { getByTestId } = render(
        <Icons.CloudSlashFilled testID="cloud-slash-filled-icon" />,
      );
      const icon = getByTestId('cloud-slash-filled-icon');
      expect(icon).toBeTruthy();
    });

    it('CloudSlashOutlined renders correctly', () => {
      const { getByTestId } = render(
        <Icons.CloudSlashOutlined testID="cloud-slash-outlined-icon" />,
      );
      const icon = getByTestId('cloud-slash-outlined-icon');
      expect(icon).toBeTruthy();
    });

    it('StopWatch renders with custom default size', () => {
      const { getByTestId } = render(
        <Icons.StopWatch testID="stopwatch-icon" />,
      );
      const icon = getByTestId('stopwatch-icon');
      expect(icon).toBeTruthy();
    });
  });
});
