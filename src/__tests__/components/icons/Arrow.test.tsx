import React from 'react';
import { render } from '@testing-library/react-native';
import Arrow from '~/components/icons/Arrow';

describe('Arrow Icon', () => {
  it('renders with default props', () => {
    const { getByTestId } = render(<Arrow testID="arrow-icon" />);
    const icon = getByTestId('arrow-icon');
    expect(icon).toBeTruthy();
  });

  it('renders with custom color', () => {
    const customColor = '#FF0000';
    const { getByTestId } = render(
      <Arrow testID="arrow-icon" color={customColor} />,
    );
    const icon = getByTestId('arrow-icon');
    expect(icon).toBeTruthy();
  });

  it('renders with custom size', () => {
    const customSize = 32;
    const { getByTestId } = render(
      <Arrow testID="arrow-icon" size={customSize} />,
    );
    const icon = getByTestId('arrow-icon');
    expect(icon).toBeTruthy();
  });

  it('renders with custom color and size', () => {
    const customColor = '#00FF00';
    const customSize = 48;
    const { getByTestId } = render(
      <Arrow testID="arrow-icon" color={customColor} size={customSize} />,
    );
    const icon = getByTestId('arrow-icon');
    expect(icon).toBeTruthy();
  });

  it('uses default color when not provided', () => {
    const { getByTestId } = render(<Arrow testID="arrow-icon" />);
    const icon = getByTestId('arrow-icon');
    expect(icon).toBeTruthy();
  });

  it('uses default size when not provided', () => {
    const { getByTestId } = render(<Arrow testID="arrow-icon" />);
    const icon = getByTestId('arrow-icon');
    expect(icon).toBeTruthy();
  });
});
