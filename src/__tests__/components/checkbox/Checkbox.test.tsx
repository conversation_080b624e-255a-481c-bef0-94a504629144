import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import CheckboxGroup from '~/components/checkbox/CheckboxGroup';

describe('CheckboxGroup', () => {
  const options = [
    { label: 'Option 1', value: 'option1' },
    { label: 'Option 2', value: 'option2' },
  ];

  it('should check the checkbox when clicked', () => {
    const { getByTestId } = render(
      <CheckboxGroup options={options} onAllChecked={() => {}} />,
    );

    const checkbox1 = getByTestId('CheckboxGroup.Checkbox.option1.Container');

    fireEvent.press(checkbox1);

    expect(getByTestId('CheckboxGroup.Checkbox.option1.Checkbox')).toBeTruthy();
  });

  it('should uncheck the checkbox when clicked again', () => {
    const { getByTestId } = render(
      <CheckboxGroup
        options={options}
        selectedOptionValues={['option1']}
        onAllChecked={() => {}}
      />,
    );

    const checkbox1 = getByTestId('CheckboxGroup.Checkbox.option1.Container');

    fireEvent.press(checkbox1);

    expect(getByTestId('CheckboxGroup.Checkbox.option1.Checkbox')).toBeTruthy();
  });

  it('should call onAllCheckedChange with true when all checkboxes are checked', () => {
    const mockOnAllCheckedChange = jest.fn();
    const { getByTestId } = render(
      <CheckboxGroup
        options={options}
        selectedOptionValues={['option1', 'option2']}
        onAllChecked={mockOnAllCheckedChange}
      />,
    );

    fireEvent.press(getByTestId('CheckboxGroup.Checkbox.option1.Container'));
    fireEvent.press(getByTestId('CheckboxGroup.Checkbox.option2.Container'));

    expect(mockOnAllCheckedChange).toHaveBeenCalledWith(true);
  });
});
