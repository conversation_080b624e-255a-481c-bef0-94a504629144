import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import ProofOfService from '~/components/ProofOfService';

// Mock the camera components
jest.mock('~/components/camera/PhotoCaptureWrapper', () => {
  return ({ _imageTitle, _imageData, onCapture }: any) => {
    const _React = require('react');
    return _React.createElement('View', {
      testID: 'photo-capture-wrapper',
      onPress: () => onCapture('mocked-image-data'),
    });
  };
});

// Mock TextInput component
jest.mock('~/components/inputs/TextInput', () => {
  return ({
    value,
    onChangeText,
    placeholder,
    testID,
    onBlur,
    id,
    defaultValue,
  }: any) => {
    const _React = require('react');
    const { TextInput } = require('react-native');
    return _React.createElement(TextInput, {
      value,
      onChangeText,
      placeholder,
      testID: testID || id,
      onBlur,
      defaultValue,
    });
  };
});

// Mock CardWrapper component
jest.mock('~/components/cards/CardWrapper', () => {
  return ({ children, id }: any) => {
    const _React = require('react');
    return _React.createElement('View', { testID: `card-${id}` }, children);
  };
});

describe('ProofOfService', () => {
  const defaultProps = {
    imageData: '',
    onCaptureClick: jest.fn(),
    onCommentChange: jest.fn(),
    commentValue: '',
    onProofOfServiceChange: jest.fn(),
    proofOfServiceValue: '',
    onBlur: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders without crashing', () => {
    const { getByTestId } = render(<ProofOfService {...defaultProps} />);

    expect(getByTestId('card-ProofOfService')).toBeTruthy();
    expect(getByTestId('photo-capture-wrapper')).toBeTruthy();
    expect(getByTestId('ProofOfService.TextInput.pod')).toBeTruthy();
    expect(getByTestId('ProofOfService.TextInput.comment')).toBeTruthy();
  });

  it('renders with image data', () => {
    const propsWithImage = {
      ...defaultProps,
      imageData: 'data:image/png;base64,mocked-image',
    };

    const { getByTestId } = render(<ProofOfService {...propsWithImage} />);

    expect(getByTestId('photo-capture-wrapper')).toBeTruthy();
  });

  it('handles photo capture', () => {
    const { getByTestId } = render(<ProofOfService {...defaultProps} />);

    const photoCapture = getByTestId('photo-capture-wrapper');
    fireEvent.press(photoCapture);

    expect(defaultProps.onCaptureClick).toHaveBeenCalledWith(
      'mocked-image-data',
    );
  });

  it('handles proof of service text input changes', () => {
    const { getByTestId } = render(<ProofOfService {...defaultProps} />);

    const podInput = getByTestId('ProofOfService.TextInput.pod');
    fireEvent.changeText(podInput, 'John Doe');

    expect(defaultProps.onProofOfServiceChange).toHaveBeenCalledWith(
      'John Doe',
    );
  });

  it('handles comment text input changes', () => {
    const { getByTestId } = render(<ProofOfService {...defaultProps} />);

    const commentInput = getByTestId('ProofOfService.TextInput.comment');
    fireEvent.changeText(commentInput, 'Test comment');

    expect(defaultProps.onCommentChange).toHaveBeenCalledWith('Test comment');
  });

  it('handles text input blur events', () => {
    const { getByTestId } = render(<ProofOfService {...defaultProps} />);

    const podInput = getByTestId('ProofOfService.TextInput.pod');
    const commentInput = getByTestId('ProofOfService.TextInput.comment');

    fireEvent(podInput, 'blur');
    fireEvent(commentInput, 'blur');

    expect(defaultProps.onBlur).toHaveBeenCalledTimes(2);
  });

  it('displays initial values in text inputs', () => {
    const propsWithValues = {
      ...defaultProps,
      proofOfServiceValue: 'Initial POD',
      commentValue: 'Initial comment',
    };

    const { getByTestId } = render(<ProofOfService {...propsWithValues} />);

    const podInput = getByTestId('ProofOfService.TextInput.pod');
    const commentInput = getByTestId('ProofOfService.TextInput.comment');

    expect(podInput.props.defaultValue).toBe('Initial POD');
    expect(commentInput.props.defaultValue).toBe('Initial comment');
  });

  it('handles empty string inputs', () => {
    const { getByTestId } = render(<ProofOfService {...defaultProps} />);

    const podInput = getByTestId('ProofOfService.TextInput.pod');
    const commentInput = getByTestId('ProofOfService.TextInput.comment');

    fireEvent.changeText(podInput, '');
    fireEvent.changeText(commentInput, '');

    expect(defaultProps.onProofOfServiceChange).toHaveBeenCalledWith('');
    expect(defaultProps.onCommentChange).toHaveBeenCalledWith('');
  });

  it('handles special characters in text inputs', () => {
    const { getByTestId } = render(<ProofOfService {...defaultProps} />);

    const podInput = getByTestId('ProofOfService.TextInput.pod');
    const commentInput = getByTestId('ProofOfService.TextInput.comment');

    fireEvent.changeText(podInput, 'John@Doe#123');
    fireEvent.changeText(commentInput, 'Special chars: !@#$%^&*()');

    expect(defaultProps.onProofOfServiceChange).toHaveBeenCalledWith(
      'John@Doe#123',
    );
    expect(defaultProps.onCommentChange).toHaveBeenCalledWith(
      'Special chars: !@#$%^&*()',
    );
  });

  it('handles long text inputs', () => {
    const { getByTestId } = render(<ProofOfService {...defaultProps} />);

    const commentInput = getByTestId('ProofOfService.TextInput.comment');
    const longText = 'A'.repeat(1000);

    fireEvent.changeText(commentInput, longText);

    expect(defaultProps.onCommentChange).toHaveBeenCalledWith(longText);
  });

  it('handles multiple photo captures', () => {
    const { getByTestId } = render(<ProofOfService {...defaultProps} />);

    const photoCapture = getByTestId('photo-capture-wrapper');

    fireEvent.press(photoCapture);
    fireEvent.press(photoCapture);
    fireEvent.press(photoCapture);

    expect(defaultProps.onCaptureClick).toHaveBeenCalledTimes(3);
  });

  it('handles rapid text input changes', () => {
    const { getByTestId } = render(<ProofOfService {...defaultProps} />);

    const podInput = getByTestId('ProofOfService.TextInput.pod');

    fireEvent.changeText(podInput, 'A');
    fireEvent.changeText(podInput, 'AB');
    fireEvent.changeText(podInput, 'ABC');
    fireEvent.changeText(podInput, 'ABCD');

    expect(defaultProps.onProofOfServiceChange).toHaveBeenCalledTimes(4);
    expect(defaultProps.onProofOfServiceChange).toHaveBeenLastCalledWith(
      'ABCD',
    );
  });
});
