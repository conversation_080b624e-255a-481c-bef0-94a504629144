import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import CheckboxList from '~/components/select/CheckBox';

// Mock @rneui/base Icon component
jest.mock('@rneui/base', () => ({
  Icon: ({ name, type, color, size, testID, onPress }: any) => {
    const _React = require('react');
    return _React.createElement('View', {
      testID,
      onPress,
      style: { name, type, color, size },
    });
  },
}));

describe('CheckboxList', () => {
  const mockItems = ['Item 1', 'Item 2', 'Item 3'];
  const mockOnAllChecked = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders all items with unchecked state', () => {
    const { getByTestId } = render(
      <CheckboxList items={mockItems} onAllChecked={mockOnAllChecked} />,
    );

    mockItems.forEach(item => {
      const itemKey = item.replace(/\s+/g, '_');
      expect(getByTestId(`CheckboxList.${itemKey}`)).toBeTruthy();
      expect(getByTestId(`CheckboxList.Icon.${itemKey}`)).toBeTruthy();
    });
  });

  it('toggles item selection when pressed', () => {
    const { getByTestId } = render(
      <CheckboxList items={mockItems} onAllChecked={mockOnAllChecked} />,
    );

    const firstItem = getByTestId('CheckboxList.Item_1');
    fireEvent.press(firstItem);

    // Should call onAllChecked with false since only one item is selected
    expect(mockOnAllChecked).toHaveBeenCalledWith(false);
  });

  it('calls onAllChecked with true when all items are selected', () => {
    const { getByTestId } = render(
      <CheckboxList items={mockItems} onAllChecked={mockOnAllChecked} />,
    );

    // Select all items
    mockItems.forEach(item => {
      const itemKey = item.replace(/\s+/g, '_');
      fireEvent.press(getByTestId(`CheckboxList.${itemKey}`));
    });

    // Should call onAllChecked with true when all items are selected
    expect(mockOnAllChecked).toHaveBeenLastCalledWith(true);
  });

  it('calls onAllChecked with false when not all items are selected', () => {
    const { getByTestId } = render(
      <CheckboxList items={mockItems} onAllChecked={mockOnAllChecked} />,
    );

    // Select only first two items
    fireEvent.press(getByTestId('CheckboxList.Item_1'));
    fireEvent.press(getByTestId('CheckboxList.Item_2'));

    // Should call onAllChecked with false since not all items are selected
    expect(mockOnAllChecked).toHaveBeenLastCalledWith(false);
  });

  it('handles items with spaces in names correctly', () => {
    const itemsWithSpaces = ['Item One', 'Item Two', 'Item Three'];
    const { getByTestId } = render(
      <CheckboxList items={itemsWithSpaces} onAllChecked={mockOnAllChecked} />,
    );

    expect(getByTestId('CheckboxList.Item_One')).toBeTruthy();
    expect(getByTestId('CheckboxList.Item_Two')).toBeTruthy();
    expect(getByTestId('CheckboxList.Item_Three')).toBeTruthy();
  });

  it('handles empty items array', () => {
    render(<CheckboxList items={[]} onAllChecked={mockOnAllChecked} />);

    // Should not call onAllChecked since there are no items
    expect(mockOnAllChecked).not.toHaveBeenCalled();
  });

  it('handles single item selection correctly', () => {
    const singleItem = ['Single Item'];
    const { getByTestId } = render(
      <CheckboxList items={singleItem} onAllChecked={mockOnAllChecked} />,
    );

    fireEvent.press(getByTestId('CheckboxList.Single_Item'));

    // Should call onAllChecked with true since it's the only item
    expect(mockOnAllChecked).toHaveBeenCalledWith(true);
  });

  it('toggles selection state correctly', () => {
    const { getByTestId } = render(
      <CheckboxList items={mockItems} onAllChecked={mockOnAllChecked} />,
    );

    const firstItem = getByTestId('CheckboxList.Item_1');

    // First press - select
    fireEvent.press(firstItem);
    expect(mockOnAllChecked).toHaveBeenCalledWith(false);

    // Second press - deselect
    fireEvent.press(firstItem);
    expect(mockOnAllChecked).toHaveBeenCalledWith(false);
  });

  it('handles multiple rapid selections', () => {
    const { getByTestId } = render(
      <CheckboxList items={mockItems} onAllChecked={mockOnAllChecked} />,
    );

    // Rapidly select multiple items
    fireEvent.press(getByTestId('CheckboxList.Item_1'));
    fireEvent.press(getByTestId('CheckboxList.Item_2'));
    fireEvent.press(getByTestId('CheckboxList.Item_3'));

    // Should call onAllChecked with true when all items are selected
    expect(mockOnAllChecked).toHaveBeenLastCalledWith(true);
  });
});
