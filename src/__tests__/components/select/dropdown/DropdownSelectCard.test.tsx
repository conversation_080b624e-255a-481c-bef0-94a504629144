import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import DropdownSelectCard from '~/components/select/dropdown/DropdownSelectCard';

// Mock icon components
jest.mock('~/components/icons', () => ({
  Chevron: () => null,
}));

jest.mock('~/components/icons/Checkmark', () => {
  const _React = require('react');
  return () => _React.createElement('View', { testID: 'Checkmark' });
});

describe('DropdownSelectCard', () => {
  const mockProps = {
    label: 'Test Label',
    selectedValue: null,
    placeholderText: 'Select an option',
    items: ['Option 1', 'Option 2', 'Option 3'],
    onValueSelection: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders with label and placeholder text', () => {
    const { getByText } = render(<DropdownSelectCard {...mockProps} />);

    expect(getByText('Test Label')).toBeTruthy();
    expect(getByText('Select an option')).toBeTruthy();
  });

  it('renders with selected value when provided', () => {
    const propsWithSelection = {
      ...mockProps,
      selectedValue: 'Option 1',
    };

    const { getByText, queryByText } = render(
      <DropdownSelectCard {...propsWithSelection} />,
    );

    expect(getByText('Option 1')).toBeTruthy();
    expect(queryByText('Select an option')).toBeNull();
  });

  it('toggles dropdown visibility when pressed', () => {
    const { getByText, queryByText } = render(
      <DropdownSelectCard {...mockProps} />,
    );

    const dropdownContainer = getByText('Select an option');

    // Initially dropdown should be closed
    expect(queryByText('Option 1')).toBeNull();
    expect(queryByText('Option 2')).toBeNull();
    expect(queryByText('Option 3')).toBeNull();

    // Open dropdown
    fireEvent.press(dropdownContainer);
    expect(getByText('Option 1')).toBeTruthy();
    expect(getByText('Option 2')).toBeTruthy();
    expect(getByText('Option 3')).toBeTruthy();

    // Close dropdown
    fireEvent.press(dropdownContainer);
    expect(queryByText('Option 1')).toBeNull();
    expect(queryByText('Option 2')).toBeNull();
    expect(queryByText('Option 3')).toBeNull();
  });

  it('calls onValueSelection when an item is selected', () => {
    const { getByText } = render(<DropdownSelectCard {...mockProps} />);

    const dropdownContainer = getByText('Select an option');
    fireEvent.press(dropdownContainer);

    const option2 = getByText('Option 2');
    fireEvent.press(option2);

    expect(mockProps.onValueSelection).toHaveBeenCalledWith('Option 2');
  });

  it('closes dropdown after item selection', () => {
    const { getByText, queryByText } = render(
      <DropdownSelectCard {...mockProps} />,
    );

    const dropdownContainer = getByText('Select an option');
    fireEvent.press(dropdownContainer);

    const option1 = getByText('Option 1');
    fireEvent.press(option1);

    // Dropdown should be closed after selection
    expect(queryByText('Option 2')).toBeNull();
    expect(queryByText('Option 3')).toBeNull();
  });

  it('shows checkmark for selected item', () => {
    const propsWithSelection = {
      ...mockProps,
      selectedValue: 'Option 2',
    };

    const { getByText, getAllByTestId } = render(
      <DropdownSelectCard {...propsWithSelection} />,
    );

    const dropdownContainer = getByText('Option 2');
    fireEvent.press(dropdownContainer);

    // Should show checkmark for selected item
    const checkmarks = getAllByTestId('Checkmark');
    expect(checkmarks.length).toBeGreaterThan(0);
  });

  it('handles empty items array', () => {
    const propsWithEmptyItems = {
      ...mockProps,
      items: [],
    };

    const { getByText, queryByText } = render(
      <DropdownSelectCard {...propsWithEmptyItems} />,
    );

    const dropdownContainer = getByText('Select an option');
    fireEvent.press(dropdownContainer);

    // Should not render any items
    expect(queryByText('Option 1')).toBeNull();
  });

  it('handles single item in list', () => {
    const propsWithSingleItem = {
      ...mockProps,
      items: ['Single Option'],
    };

    const { getByText } = render(
      <DropdownSelectCard {...propsWithSingleItem} />,
    );

    const dropdownContainer = getByText('Select an option');
    fireEvent.press(dropdownContainer);

    expect(getByText('Single Option')).toBeTruthy();
  });

  it('handles null selectedValue', () => {
    const propsWithNullValue = {
      ...mockProps,
      selectedValue: null,
    };

    const { getByText } = render(
      <DropdownSelectCard {...propsWithNullValue} />,
    );

    expect(getByText('Select an option')).toBeTruthy();
  });

  it('handles undefined selectedValue', () => {
    const propsWithUndefinedValue = {
      ...mockProps,
      selectedValue: undefined,
    };

    const { getByText } = render(
      <DropdownSelectCard {...propsWithUndefinedValue} />,
    );

    expect(getByText('Select an option')).toBeTruthy();
  });

  it('handles long item names', () => {
    const propsWithLongItems = {
      ...mockProps,
      items: ['Very Long Option Name That Might Overflow'],
    };

    const { getByText } = render(
      <DropdownSelectCard {...propsWithLongItems} />,
    );

    const dropdownContainer = getByText('Select an option');
    fireEvent.press(dropdownContainer);

    expect(getByText('Very Long Option Name That Might Overflow')).toBeTruthy();
  });

  it('handles special characters in item names', () => {
    const propsWithSpecialChars = {
      ...mockProps,
      items: ['Option & More', 'Option (Special)', 'Option @ Test'],
    };

    const { getByText } = render(
      <DropdownSelectCard {...propsWithSpecialChars} />,
    );

    const dropdownContainer = getByText('Select an option');
    fireEvent.press(dropdownContainer);

    expect(getByText('Option & More')).toBeTruthy();
    expect(getByText('Option (Special)')).toBeTruthy();
    expect(getByText('Option @ Test')).toBeTruthy();
  });
});
