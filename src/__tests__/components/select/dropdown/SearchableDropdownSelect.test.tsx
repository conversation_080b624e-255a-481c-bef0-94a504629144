import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import SearchableDropdownSelect from '~/components/select/dropdown/SearchableDropdownSelect';

const testID = 'DropdownSelectTest';
const mockOnValueSelection = jest.fn();
const items = [
  { Id: '1', Name: 'Apple' },
  { Id: '2', Name: 'Banana' },
  { Id: '3', Name: 'Cherry' },
];

describe('SearchableDropdownSelect Component', () => {
  it('opens dropdown on tap and displays items', async () => {
    const { getByTestId, queryByTestId, getByText } = render(
      <SearchableDropdownSelect
        id={testID}
        label="Select Fruit"
        selectedValue={null}
        placeholderText="Choose an option"
        searchPlaceHolderText="Search..."
        items={items}
        onValueSelection={mockOnValueSelection}
      />,
    );

    expect(queryByTestId(`${testID}.OptionsContainer`)).toBeFalsy();

    fireEvent.press(getByTestId(`${testID}.DropdownButton`));

    await waitFor(() => {
      expect(getByTestId(`${testID}.OptionsContainer`)).toBeTruthy();
    });

    expect(getByText('Apple')).toBeTruthy();
    expect(getByText('Banana')).toBeTruthy();
    expect(getByText('Cherry')).toBeTruthy();
  });

  it('filters options based on search input', async () => {
    const { getByTestId, getByPlaceholderText, queryByText } = render(
      <SearchableDropdownSelect
        id={testID}
        label="Select Fruit"
        selectedValue={null}
        placeholderText="Choose an option"
        searchPlaceHolderText="Search..."
        items={items}
        onValueSelection={mockOnValueSelection}
      />,
    );

    fireEvent.press(getByTestId(`${testID}.DropdownButton`));

    const searchInput = getByPlaceholderText('Search...');
    fireEvent.changeText(searchInput, 'Ban');

    await waitFor(() => {
      expect(getByTestId(`${testID}.OptionsContainer`)).toBeTruthy();
    });

    expect(queryByText('Apple')).toBeFalsy();
    expect(queryByText('Cherry')).toBeFalsy();
    expect(getByTestId(`${testID}.Option.Banana`)).toBeTruthy();
  });

  it('selects an option and closes dropdown', async () => {
    const { getByTestId, getByText, queryByTestId, rerender } = render(
      <SearchableDropdownSelect
        id={testID}
        label="Select Fruit"
        selectedValue={null}
        placeholderText="Choose an option"
        searchPlaceHolderText="Search..."
        items={items}
        onValueSelection={mockOnValueSelection}
      />,
    );

    fireEvent.press(getByTestId(`${testID}.DropdownButton`));
    fireEvent.press(getByText('Banana'));

    await waitFor(() => {
      expect(mockOnValueSelection).toHaveBeenCalledWith({
        Id: '2',
        Name: 'Banana',
      });
    });

    rerender(
      <SearchableDropdownSelect
        id={testID}
        label="Select Fruit"
        selectedValue={{
          Id: '2',
          Name: 'Banana',
        }}
        placeholderText="Choose an option"
        searchPlaceHolderText="Search..."
        items={items}
        onValueSelection={mockOnValueSelection}
      />,
    );

    expect(queryByTestId(`${testID}.OptionsContainer`)).toBeFalsy();
    expect(getByText('Banana')).toBeTruthy();
  });

  it('clears search query when close button is pressed', async () => {
    const { getByTestId, getByText, getByPlaceholderText } = render(
      <SearchableDropdownSelect
        id={testID}
        label="Select Fruit"
        selectedValue={null}
        placeholderText="Choose an option"
        searchPlaceHolderText="Search..."
        items={[...items, { Id: '4', Name: 'Grapes' }]}
        onValueSelection={mockOnValueSelection}
      />,
    );

    fireEvent.press(getByTestId(`${testID}.DropdownButton`));

    await waitFor(() => {
      expect(getByTestId(`${testID}.OptionsContainer`)).toBeTruthy();
    });

    const searchInput = getByPlaceholderText('Search...');
    fireEvent.changeText(searchInput, 'gra');
    expect(getByText('Grapes')).toBeTruthy();

    fireEvent.press(getByTestId(`${testID}.ClearSearchButton`));

    await waitFor(() => {
      expect(searchInput.props.value).toBe('');
      expect(getByText('Apple')).toBeTruthy();
      expect(getByText('Banana')).toBeTruthy();
      expect(getByText('Cherry')).toBeTruthy();
      expect(getByText('Grapes')).toBeTruthy();
    });
  });
});
