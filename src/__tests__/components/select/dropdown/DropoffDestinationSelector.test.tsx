import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import DropoffDestinationSelector from '~/components/select/dropdown/DropoffDestinationSelector';

// Mock icon components
jest.mock('~/components/icons', () => ({
  Chevron: () => null,
}));

// Mock localization
jest.mock('~/localization/en', () => ({
  select_dropoff_destination: 'Select dropoff destination',
  dropoff_destination: 'Dropoff Destination',
}));

describe('DropoffDestinationSelector', () => {
  const mockDestinations = [
    { id: '1', title: 'Destination 1' },
    { id: '2', title: 'Destination 2' },
    { id: '3', title: 'Destination 3' },
  ];

  const mockOnOpenModal = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders with default placeholder text', () => {
    const { getByText } = render(
      <DropoffDestinationSelector
        destinations={mockDestinations}
        selectedDestination={undefined}
        onOpenModal={mockOnOpenModal}
      />,
    );

    expect(getByText('Dropoff Destination')).toBeTruthy();
    expect(getByText('Select dropoff destination')).toBeTruthy();
  });

  it('renders with selected destination title', () => {
    const { getByText, queryByText } = render(
      <DropoffDestinationSelector
        destinations={mockDestinations}
        selectedDestination="2"
        onOpenModal={mockOnOpenModal}
      />,
    );

    expect(getByText('Destination 2')).toBeTruthy();
    expect(queryByText('Select dropoff destination')).toBeNull();
  });

  it('calls onOpenModal when pressed', () => {
    const { getByText } = render(
      <DropoffDestinationSelector
        destinations={mockDestinations}
        selectedDestination={undefined}
        onOpenModal={mockOnOpenModal}
      />,
    );

    const selector = getByText('Select dropoff destination');
    fireEvent.press(selector);

    expect(mockOnOpenModal).toHaveBeenCalledTimes(1);
  });

  it('handles empty destinations array', () => {
    const { getByText } = render(
      <DropoffDestinationSelector
        destinations={[]}
        selectedDestination={undefined}
        onOpenModal={mockOnOpenModal}
      />,
    );

    expect(getByText('Select dropoff destination')).toBeTruthy();
  });

  it('handles null destinations', () => {
    const { getByText } = render(
      <DropoffDestinationSelector
        destinations={null as any}
        selectedDestination={undefined}
        onOpenModal={mockOnOpenModal}
      />,
    );

    expect(getByText('Select dropoff destination')).toBeTruthy();
  });

  it('handles undefined destinations', () => {
    const { getByText } = render(
      <DropoffDestinationSelector
        destinations={undefined as any}
        selectedDestination={undefined}
        onOpenModal={mockOnOpenModal}
      />,
    );

    expect(getByText('Select dropoff destination')).toBeTruthy();
  });

  it('handles selected destination not found in destinations array', () => {
    const { getByText } = render(
      <DropoffDestinationSelector
        destinations={mockDestinations}
        selectedDestination="999"
        onOpenModal={mockOnOpenModal}
      />,
    );

    // Should fall back to placeholder text when destination not found
    expect(getByText('Select dropoff destination')).toBeTruthy();
  });

  it('updates displayed destination when selectedDestination changes', () => {
    const { getByText, rerender } = render(
      <DropoffDestinationSelector
        destinations={mockDestinations}
        selectedDestination="1"
        onOpenModal={mockOnOpenModal}
      />,
    );

    expect(getByText('Destination 1')).toBeTruthy();

    // Change selection
    rerender(
      <DropoffDestinationSelector
        destinations={mockDestinations}
        selectedDestination="3"
        onOpenModal={mockOnOpenModal}
      />,
    );

    expect(getByText('Destination 3')).toBeTruthy();
  });

  it('handles destinations with special characters in titles', () => {
    const destinationsWithSpecialChars = [
      { id: '1', title: 'Destination & More' },
      { id: '2', title: 'Destination (Special)' },
      { id: '3', title: 'Destination @ Test' },
    ];

    const { getByText } = render(
      <DropoffDestinationSelector
        destinations={destinationsWithSpecialChars}
        selectedDestination="1"
        onOpenModal={mockOnOpenModal}
      />,
    );

    expect(getByText('Destination & More')).toBeTruthy();
  });

  it('handles destinations with long titles', () => {
    const destinationsWithLongTitles = [
      {
        id: '1',
        title: 'Very Long Destination Name That Might Overflow The Container',
      },
    ];

    const { getByText } = render(
      <DropoffDestinationSelector
        destinations={destinationsWithLongTitles}
        selectedDestination="1"
        onOpenModal={mockOnOpenModal}
      />,
    );

    expect(
      getByText('Very Long Destination Name That Might Overflow The Container'),
    ).toBeTruthy();
  });

  it('handles destinations with numeric IDs', () => {
    const destinationsWithNumericIds = [
      { id: 1, title: 'Numeric ID Destination' },
      { id: 2, title: 'Another Numeric ID' },
    ];

    const { getByText } = render(
      <DropoffDestinationSelector
        destinations={destinationsWithNumericIds}
        selectedDestination="1"
        onOpenModal={mockOnOpenModal}
      />,
    );

    // The component doesn't find numeric ID "1" when comparing with string "1", so it shows placeholder
    expect(getByText('Select dropoff destination')).toBeTruthy();
  });

  it('handles empty string selectedDestination', () => {
    const { getByText } = render(
      <DropoffDestinationSelector
        destinations={mockDestinations}
        selectedDestination=""
        onOpenModal={mockOnOpenModal}
      />,
    );

    expect(getByText('Select dropoff destination')).toBeTruthy();
  });

  it('handles null selectedDestination', () => {
    const { getByText } = render(
      <DropoffDestinationSelector
        destinations={mockDestinations}
        selectedDestination={null as any}
        onOpenModal={mockOnOpenModal}
      />,
    );

    expect(getByText('Select dropoff destination')).toBeTruthy();
  });

  it('handles destinations with missing title property', () => {
    const destinationsWithMissingTitle = [
      { id: '1', name: 'Wrong Property Name' },
      { id: '2', title: 'Correct Property Name' },
    ];

    const { getByText } = render(
      <DropoffDestinationSelector
        destinations={destinationsWithMissingTitle}
        selectedDestination="1"
        onOpenModal={mockOnOpenModal}
      />,
    );

    // Should show empty text when title is missing
    expect(getByText('')).toBeTruthy();
  });
});
