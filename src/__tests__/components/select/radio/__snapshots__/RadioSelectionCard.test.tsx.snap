// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`RadioButtonItem matches snapshot when not selected 1`] = `
<View
  accessibilityState={
    {
      "busy": undefined,
      "checked": undefined,
      "disabled": undefined,
      "expanded": undefined,
      "selected": undefined,
    }
  }
  accessibilityValue={
    {
      "max": undefined,
      "min": undefined,
      "now": undefined,
      "text": undefined,
    }
  }
  accessible={true}
  collapsable={false}
  focusable={true}
  nativeID="RadioButtonItem.TouchableOpacity.Test Option"
  onClick={[Function]}
  onResponderGrant={[Function]}
  onResponderMove={[Function]}
  onResponderRelease={[Function]}
  onResponderTerminate={[Function]}
  onResponderTerminationRequest={[Function]}
  onStartShouldSetResponder={[Function]}
  style={
    {
      "alignItems": "center",
      "backgroundColor": "#F6F7F9",
      "borderRadius": 8,
      "elevation": 1,
      "flexDirection": "row",
      "gap": 12,
      "opacity": 1,
      "padding": 12,
      "shadowColor": "#F6F7F9",
      "shadowOffset": {
        "height": 2,
        "width": 0,
      },
      "shadowOpacity": 0.1,
      "shadowRadius": 4,
    }
  }
>
  <View
    style={
      {
        "flex": 1,
        "gap": 4,
      }
    }
  >
    <Text
      numberOfLines={1}
      style={
        {
          "color": "black",
          "fontSize": 16,
          "letterSpacing": -0.1,
        }
      }
    >
      Test Option
    </Text>
    <Text
      numberOfLines={1}
      style={
        {
          "color": "rgba(0, 0, 0, 0.7)",
          "fontFamily": "Satoshi Variable",
          "fontSize": 15,
          "letterSpacing": -0.1,
        }
      }
    >
      Test Subtitle
    </Text>
  </View>
  <Text
    style={
      {
        "color": "rgba(0, 0, 0, 0.7)",
        "fontFamily": "Satoshi Variable",
        "fontSize": 15,
        "letterSpacing": -0.1,
      }
    }
  >
    Test Right Text
  </Text>
  <View
    style={
      {
        "alignItems": "center",
        "backgroundColor": "#FFFFFF",
        "borderColor": "rgba(0, 0, 0, 0.5)",
        "borderRadius": 10,
        "borderWidth": 1,
        "height": 20,
        "justifyContent": "center",
        "marginRight": 12,
        "paddingLeft": 10,
        "width": 20,
      }
    }
  />
</View>
`;

exports[`RadioButtonItem matches snapshot when selected 1`] = `
<View
  accessibilityState={
    {
      "busy": undefined,
      "checked": undefined,
      "disabled": undefined,
      "expanded": undefined,
      "selected": undefined,
    }
  }
  accessibilityValue={
    {
      "max": undefined,
      "min": undefined,
      "now": undefined,
      "text": undefined,
    }
  }
  accessible={true}
  collapsable={false}
  focusable={true}
  nativeID="RadioButtonItem.TouchableOpacity.Test Option"
  onClick={[Function]}
  onResponderGrant={[Function]}
  onResponderMove={[Function]}
  onResponderRelease={[Function]}
  onResponderTerminate={[Function]}
  onResponderTerminationRequest={[Function]}
  onStartShouldSetResponder={[Function]}
  style={
    {
      "alignItems": "center",
      "backgroundColor": "#F6F7F9",
      "borderRadius": 8,
      "elevation": 1,
      "flexDirection": "row",
      "gap": 12,
      "opacity": 1,
      "padding": 12,
      "shadowColor": "#F6F7F9",
      "shadowOffset": {
        "height": 2,
        "width": 0,
      },
      "shadowOpacity": 0.1,
      "shadowRadius": 4,
    }
  }
>
  <View
    style={
      {
        "flex": 1,
        "gap": 4,
      }
    }
  >
    <Text
      numberOfLines={1}
      style={
        {
          "color": "black",
          "fontSize": 16,
          "letterSpacing": -0.1,
        }
      }
    >
      Test Option
    </Text>
    <Text
      numberOfLines={1}
      style={
        {
          "color": "rgba(0, 0, 0, 0.7)",
          "fontFamily": "Satoshi Variable",
          "fontSize": 15,
          "letterSpacing": -0.1,
        }
      }
    >
      Test Subtitle
    </Text>
  </View>
  <Text
    style={
      {
        "color": "rgba(0, 0, 0, 0.7)",
        "fontFamily": "Satoshi Variable",
        "fontSize": 15,
        "letterSpacing": -0.1,
      }
    }
  >
    Test Right Text
  </Text>
  <View
    style={
      {
        "alignItems": "center",
        "backgroundColor": "#FFFFFF",
        "borderColor": "#2f428c",
        "borderRadius": 10,
        "borderWidth": 6,
        "height": 20,
        "justifyContent": "center",
        "marginRight": 12,
        "width": 20,
      }
    }
  />
</View>
`;
