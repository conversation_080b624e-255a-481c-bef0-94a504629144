import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import RadioButtonItem from '~/components/select/radio/RadioButtonItem';

describe('RadioButtonItem', () => {
  const mockOnItemSelected = jest.fn();
  const item = {
    id: '1',
    title: 'Test Option',
    subtitle: 'Test Subtitle',
    rightText: 'Test Right Text',
  };

  beforeEach(() => {
    mockOnItemSelected.mockClear();
  });

  it('renders title, subtitle, and rightText when provided', () => {
    const { getByText } = render(
      <RadioButtonItem
        item={item}
        isSelected={false}
        onItemSelected={mockOnItemSelected}
      />,
    );

    expect(getByText('Test Option')).toBeTruthy();
    expect(getByText('Test Subtitle')).toBeTruthy();
    expect(getByText('Test Right Text')).toBeTruthy();
  });

  it('does not render subtitle when it is empty', () => {
    const modifiedItem = { ...item, subtitle: '' };
    const { queryByText } = render(
      <RadioButtonItem
        item={modifiedItem}
        isSelected={false}
        onItemSelected={mockOnItemSelected}
      />,
    );

    expect(queryByText('Test Subtitle')).toBeNull();
  });

  it('does not render rightText when it is empty', () => {
    const modifiedItem = { ...item, rightText: '' };
    const { queryByText } = render(
      <RadioButtonItem
        item={modifiedItem}
        isSelected={false}
        onItemSelected={mockOnItemSelected}
      />,
    );

    expect(queryByText('Test Right Text')).toBeNull();
  });

  it('calls onItemSelected with the correct id when pressed', () => {
    const { getByText } = render(
      <RadioButtonItem
        item={item}
        isSelected={false}
        onItemSelected={mockOnItemSelected}
      />,
    );

    // fireEvent.press on the title should trigger the onPress on the TouchableOpacity
    fireEvent.press(getByText('Test Option'));
    expect(mockOnItemSelected).toHaveBeenCalledWith('1');
  });

  it('matches snapshot when selected', () => {
    const tree = render(
      <RadioButtonItem
        item={item}
        isSelected={true}
        onItemSelected={mockOnItemSelected}
      />,
    ).toJSON();
    expect(tree).toMatchSnapshot();
  });

  it('matches snapshot when not selected', () => {
    const tree = render(
      <RadioButtonItem
        item={item}
        isSelected={false}
        onItemSelected={mockOnItemSelected}
      />,
    ).toJSON();
    expect(tree).toMatchSnapshot();
  });
});
