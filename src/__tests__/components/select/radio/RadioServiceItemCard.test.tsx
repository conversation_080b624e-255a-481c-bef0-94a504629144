import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import RadioServiceItemCard from '~/components/select/radio/RadioServiceItemCard';
import { StopType } from '~/types/stops.types';

// Mock icon components
jest.mock('~/components/icons', () => ({
  Checkmark: ({ color, size }: any) => {
    const _React = require('react');
    return _React.createElement('View', {
      testID: 'Checkmark',
      style: { color, size },
    });
  },
  CircleBackground: ({ children, fillColor }: any) => {
    const _React = require('react');
    return _React.createElement('View', {
      testID: 'CircleBackground',
      style: { fillColor },
      children,
    });
  },

  Box: ({ size, color }: any) => {
    const _React = require('react');
    return _React.createElement('View', {
      testID: 'Box',
      style: { size, color },
    });
  },
}));

describe('RadioServiceItemCard', () => {
  const mockItem = {
    id: '1',
    type: StopType.Service,
    data: {
      Name: 'Test Service',
      Quantity__c: 5,
      Parcel_Type_Name__c: 'Package',
    },
    icon: 'TestIcon',
  } as any;

  const mockOnItemSelected = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders service item with name and data', () => {
    const { getByText } = render(
      <RadioServiceItemCard
        item={mockItem}
        isSelected={false}
        onItemSelected={mockOnItemSelected}
        isCompleted={false}
      />,
    );

    expect(getByText('Test Service')).toBeTruthy();
  });

  it('renders non-service item with type name', () => {
    const nonServiceItem = {
      id: '2',
      type: 'OtherType',
      data: null,
      icon: 'TestIcon',
    } as any;

    const { getByText } = render(
      <RadioServiceItemCard
        item={nonServiceItem}
        isSelected={false}
        onItemSelected={mockOnItemSelected}
        isCompleted={false}
      />,
    );

    expect(getByText('OtherType')).toBeTruthy();
  });

  it('calls onItemSelected when pressed and not completed', () => {
    const { getByText } = render(
      <RadioServiceItemCard
        item={mockItem}
        isSelected={false}
        onItemSelected={mockOnItemSelected}
        isCompleted={false}
      />,
    );

    const card = getByText('Test Service');
    fireEvent.press(card);

    expect(mockOnItemSelected).toHaveBeenCalledWith(mockItem);
  });

  it('does not call onItemSelected when completed and allowSelectionWhenCompleted is true', () => {
    const { getByText } = render(
      <RadioServiceItemCard
        item={mockItem}
        isSelected={false}
        onItemSelected={mockOnItemSelected}
        isCompleted={true}
        allowSelectionWhenCompleted={true}
      />,
    );

    const card = getByText('Test Service');
    fireEvent.press(card);

    expect(mockOnItemSelected).not.toHaveBeenCalled();
  });

  it('calls onItemSelected when completed but allowSelectionWhenCompleted is false', () => {
    const { getByText } = render(
      <RadioServiceItemCard
        item={mockItem}
        isSelected={false}
        onItemSelected={mockOnItemSelected}
        isCompleted={true}
        allowSelectionWhenCompleted={false}
      />,
    );

    const card = getByText('Test Service');
    fireEvent.press(card);

    expect(mockOnItemSelected).toHaveBeenCalledWith(mockItem);
  });

  it('shows checkmark when completed', () => {
    const { getByTestId } = render(
      <RadioServiceItemCard
        item={mockItem}
        isSelected={false}
        onItemSelected={mockOnItemSelected}
        isCompleted={true}
      />,
    );

    expect(getByTestId('Checkmark')).toBeTruthy();
  });

  it('shows radio button when not completed', () => {
    const { getByTestId } = render(
      <RadioServiceItemCard
        item={mockItem}
        isSelected={false}
        onItemSelected={mockOnItemSelected}
        isCompleted={false}
      />,
    );

    expect(getByTestId('CircleBackground')).toBeTruthy();
  });

  it('handles item with array data', () => {
    const itemWithArrayData = {
      id: '3',
      type: StopType.Service,
      data: [
        {
          Quantity__c: 3,
          Parcel_Type_Name__c: 'Box',
        },
        {
          Quantity__c: 2,
          Parcel_Type_Name__c: 'Envelope',
        },
      ],
      icon: 'TestIcon',
    } as any;

    const { getByText } = render(
      <RadioServiceItemCard
        item={itemWithArrayData}
        isSelected={false}
        onItemSelected={mockOnItemSelected}
        isCompleted={false}
      />,
    );

    expect(getByText('3 Box')).toBeTruthy();
    expect(getByText('2 Envelope')).toBeTruthy();
  });

  it('handles delivery item with Dropoff_Quantity__c', () => {
    const deliveryItemWithDropoffQuantity = {
      id: 'delivery',
      type: StopType.Delivery,
      data: [
        {
          Quantity__c: null,
          Dropoff_Quantity__c: 5,
          Parcel_Type_Name__c: 'Blood',
        },
        {
          Quantity__c: 2,
          Dropoff_Quantity__c: null,
          Parcel_Type_Name__c: 'Envelope',
        },
      ],
      icon: 'TestIcon',
    } as any;

    const { getByText } = render(
      <RadioServiceItemCard
        item={deliveryItemWithDropoffQuantity}
        isSelected={false}
        onItemSelected={mockOnItemSelected}
        isCompleted={false}
      />,
    );

    expect(getByText('5 Blood')).toBeTruthy();
    expect(getByText('2 Envelope')).toBeTruthy();
  });

  it('handles item with missing quantity data', () => {
    const itemWithMissingQuantity = {
      id: '4',
      type: StopType.Service,
      data: [
        {
          Parcel_Type_Name__c: 'Box',
        },
        {
          Quantity__c: 2,
          Parcel_Type_Name__c: 'Envelope',
        },
      ],
      icon: 'TestIcon',
    } as any;

    const { getByText, queryByText } = render(
      <RadioServiceItemCard
        item={itemWithMissingQuantity}
        isSelected={false}
        onItemSelected={mockOnItemSelected}
        isCompleted={false}
      />,
    );

    expect(queryByText('Box')).toBeNull();
    expect(getByText('2 Envelope')).toBeTruthy();
  });

  it('handles delivery item with missing quantity data but has Dropoff_Quantity__c', () => {
    const deliveryItemWithMissingQuantity = {
      id: 'delivery',
      type: StopType.Delivery,
      data: [
        {
          Quantity__c: null,
          Dropoff_Quantity__c: 3,
          Parcel_Type_Name__c: 'Box',
        },
        {
          Quantity__c: null,
          Dropoff_Quantity__c: null,
          Parcel_Type_Name__c: 'Envelope',
        },
      ],
      icon: 'TestIcon',
    } as any;

    const { getByText, queryByText } = render(
      <RadioServiceItemCard
        item={deliveryItemWithMissingQuantity}
        isSelected={false}
        onItemSelected={mockOnItemSelected}
        isCompleted={false}
      />,
    );

    expect(getByText('3 Box')).toBeTruthy();
    expect(queryByText('Envelope')).toBeNull();
  });

  it('handles item with missing parcel type data', () => {
    const itemWithMissingParcelType = {
      id: '5',
      type: StopType.Service,
      data: [
        {
          Quantity__c: 3,
        },
        {
          Quantity__c: 2,
          Parcel_Type_Name__c: 'Envelope',
        },
      ],
      icon: 'TestIcon',
    } as any;

    const { getByText, queryByText } = render(
      <RadioServiceItemCard
        item={itemWithMissingParcelType}
        isSelected={false}
        onItemSelected={mockOnItemSelected}
        isCompleted={false}
      />,
    );

    expect(queryByText('3')).toBeNull();
    expect(getByText('2 Envelope')).toBeTruthy();
  });

  it('handles delivery item with missing parcel type data', () => {
    const deliveryItemWithMissingParcelType = {
      id: 'delivery',
      type: StopType.Delivery,
      data: [
        {
          Quantity__c: null,
          Dropoff_Quantity__c: 3,
        },
        {
          Quantity__c: null,
          Dropoff_Quantity__c: 2,
          Parcel_Type_Name__c: 'Envelope',
        },
      ],
      icon: 'TestIcon',
    } as any;

    const { getByText, queryByText } = render(
      <RadioServiceItemCard
        item={deliveryItemWithMissingParcelType}
        isSelected={false}
        onItemSelected={mockOnItemSelected}
        isCompleted={false}
      />,
    );

    expect(queryByText('3')).toBeNull();
    expect(getByText('2 Envelope')).toBeTruthy();
  });

  it('handles item with empty data array', () => {
    const itemWithEmptyData = {
      id: '6',
      type: StopType.Service,
      data: [],
      icon: 'TestIcon',
    } as any;

    const { getByText } = render(
      <RadioServiceItemCard
        item={itemWithEmptyData}
        isSelected={false}
        onItemSelected={mockOnItemSelected}
        isCompleted={false}
      />,
    );

    expect(getByText('Service')).toBeTruthy();
  });

  it('handles item with null data', () => {
    const itemWithNullData = {
      id: '7',
      type: StopType.Service,
      data: null,
      icon: 'TestIcon',
    } as any;

    const { getByText } = render(
      <RadioServiceItemCard
        item={itemWithNullData}
        isSelected={false}
        onItemSelected={mockOnItemSelected}
        isCompleted={false}
      />,
    );

    expect(getByText('Service')).toBeTruthy();
  });

  it('handles item with undefined data', () => {
    const itemWithUndefinedData = {
      id: '8',
      type: StopType.Service,
      data: undefined,
      icon: 'TestIcon',
    } as any;

    const { getByText } = render(
      <RadioServiceItemCard
        item={itemWithUndefinedData}
        isSelected={false}
        onItemSelected={mockOnItemSelected}
        isCompleted={false}
      />,
    );

    expect(getByText('Service')).toBeTruthy();
  });

  it('handles long service names', () => {
    const itemWithLongName = {
      id: '9',
      type: StopType.Service,
      data: {
        Name: 'Very Long Service Name That Might Overflow The Container',
        Quantity__c: 1,
        Parcel_Type_Name__c: 'Package',
      },
      icon: 'TestIcon',
    } as any;

    const { getByText } = render(
      <RadioServiceItemCard
        item={itemWithLongName}
        isSelected={false}
        onItemSelected={mockOnItemSelected}
        isCompleted={false}
      />,
    );

    expect(
      getByText('Very Long Service Name That Might Overflow The Container'),
    ).toBeTruthy();
  });

  it('handles special characters in service names', () => {
    const itemWithSpecialChars = {
      id: '10',
      type: StopType.Service,
      data: {
        Name: 'Service & More (Special) @ Test',
        Quantity__c: 1,
        Parcel_Type_Name__c: 'Package',
      },
      icon: 'TestIcon',
    } as any;

    const { getByText } = render(
      <RadioServiceItemCard
        item={itemWithSpecialChars}
        isSelected={false}
        onItemSelected={mockOnItemSelected}
        isCompleted={false}
      />,
    );

    expect(getByText('Service & More (Special) @ Test')).toBeTruthy();
  });

  it('handles zero quantity values', () => {
    const itemWithZeroQuantity = {
      id: '11',
      type: StopType.Service,
      data: [
        {
          Quantity__c: 0,
          Parcel_Type_Name__c: 'Box',
        },
        {
          Quantity__c: 2,
          Parcel_Type_Name__c: 'Envelope',
        },
      ],
      icon: 'TestIcon',
    } as any;

    const { getByText, queryByText } = render(
      <RadioServiceItemCard
        item={itemWithZeroQuantity}
        isSelected={false}
        onItemSelected={mockOnItemSelected}
        isCompleted={false}
      />,
    );

    expect(queryByText('0 Box')).toBeNull();
    expect(getByText('2 Envelope')).toBeTruthy();
  });

  it('handles delivery item with zero Dropoff_Quantity__c values', () => {
    const deliveryItemWithZeroQuantity = {
      id: 'delivery',
      type: StopType.Delivery,
      data: [
        {
          Quantity__c: null,
          Dropoff_Quantity__c: 0,
          Parcel_Type_Name__c: 'Box',
        },
        {
          Quantity__c: null,
          Dropoff_Quantity__c: 2,
          Parcel_Type_Name__c: 'Envelope',
        },
      ],
      icon: 'TestIcon',
    } as any;

    const { getByText, queryByText } = render(
      <RadioServiceItemCard
        item={deliveryItemWithZeroQuantity}
        isSelected={false}
        onItemSelected={mockOnItemSelected}
        isCompleted={false}
      />,
    );

    expect(queryByText('0 Box')).toBeNull();
    expect(getByText('2 Envelope')).toBeTruthy();
  });

  it('handles negative quantity values', () => {
    const itemWithNegativeQuantity = {
      id: '12',
      type: StopType.Service,
      data: [
        {
          Quantity__c: -1,
          Parcel_Type_Name__c: 'Box',
        },
        {
          Quantity__c: 2,
          Parcel_Type_Name__c: 'Envelope',
        },
      ],
      icon: 'TestIcon',
    } as any;

    const { getByText } = render(
      <RadioServiceItemCard
        item={itemWithNegativeQuantity}
        isSelected={false}
        onItemSelected={mockOnItemSelected}
        isCompleted={false}
      />,
    );

    // The component might still render negative values, so we check for the positive one
    expect(getByText('2 Envelope')).toBeTruthy();
  });

  it('handles delivery item with negative Dropoff_Quantity__c values', () => {
    const deliveryItemWithNegativeQuantity = {
      id: 'delivery',
      type: StopType.Delivery,
      data: [
        {
          Quantity__c: null,
          Dropoff_Quantity__c: -1,
          Parcel_Type_Name__c: 'Box',
        },
        {
          Quantity__c: null,
          Dropoff_Quantity__c: 2,
          Parcel_Type_Name__c: 'Envelope',
        },
      ],
      icon: 'TestIcon',
    } as any;

    const { getByText } = render(
      <RadioServiceItemCard
        item={deliveryItemWithNegativeQuantity}
        isSelected={false}
        onItemSelected={mockOnItemSelected}
        isCompleted={false}
      />,
    );

    // The component might still render negative values, so we check for the positive one
    expect(getByText('2 Envelope')).toBeTruthy();
  });

  it('displays parcels section when parcel data exists', () => {
    const itemWithParcels = {
      id: 'delivery',
      type: StopType.Delivery,
      data: [
        {
          Quantity__c: 1,
          Parcel_Type_Name__c: 'Blood',
        },
      ],
      icon: 'TestIcon',
    } as any;

    const { getByText, getByTestId } = render(
      <RadioServiceItemCard
        item={itemWithParcels}
        isSelected={false}
        onItemSelected={mockOnItemSelected}
        isCompleted={false}
      />,
    );

    expect(getByTestId('Box')).toBeTruthy();
    expect(getByText('Expected parcels')).toBeTruthy();
    expect(getByText('1 Blood')).toBeTruthy();
  });

  it('handles pickup item with Quantity__c correctly', () => {
    const pickupItem = {
      id: 'pickup',
      type: StopType.Pickup,
      data: [
        {
          Quantity__c: 3,
          Dropoff_Quantity__c: null,
          Parcel_Type_Name__c: 'Box',
        },
        {
          Quantity__c: 2,
          Dropoff_Quantity__c: null,
          Parcel_Type_Name__c: 'Envelope',
        },
      ],
      icon: 'TestIcon',
    } as any;

    const { getByText, getByTestId } = render(
      <RadioServiceItemCard
        item={pickupItem}
        isSelected={false}
        onItemSelected={mockOnItemSelected}
        isCompleted={false}
      />,
    );

    expect(getByTestId('Box')).toBeTruthy();
    expect(getByText('3 Box')).toBeTruthy();
    expect(getByText('2 Envelope')).toBeTruthy();
  });

  it('displays pickup title correctly', () => {
    const pickupItem = {
      id: 'pickup',
      type: StopType.Pickup,
      data: [],
      icon: 'TestIcon',
    } as any;

    const { getByText } = render(
      <RadioServiceItemCard
        item={pickupItem}
        isSelected={false}
        onItemSelected={mockOnItemSelected}
        isCompleted={false}
      />,
    );

    expect(getByText('Pickup')).toBeTruthy();
  });

  it('displays delivery title correctly', () => {
    const deliveryItem = {
      id: 'delivery',
      type: StopType.Delivery,
      data: [],
      icon: 'TestIcon',
    } as any;

    const { getByText } = render(
      <RadioServiceItemCard
        item={deliveryItem}
        isSelected={false}
        onItemSelected={mockOnItemSelected}
        isCompleted={false}
      />,
    );

    expect(getByText('Delivery')).toBeTruthy();
  });

  it('does not display parcels section when no valid parcels exist', () => {
    const itemWithInvalidParcels = {
      id: 'delivery',
      type: StopType.Delivery,
      data: [
        {
          Quantity__c: null,
          Parcel_Type_Name__c: 'Blood',
        },
        {
          Quantity__c: 1,
          Parcel_Type_Name__c: null,
        },
        {
          Quantity__c: 0,
          Parcel_Type_Name__c: 'Box',
        },
      ],
      icon: 'TestIcon',
    } as any;

    const { queryByText, queryByTestId } = render(
      <RadioServiceItemCard
        item={itemWithInvalidParcels}
        isSelected={false}
        onItemSelected={mockOnItemSelected}
        isCompleted={false}
      />,
    );

    expect(queryByText('Expected parcels')).toBeNull();
    expect(queryByTestId('Box')).toBeNull();
  });
});
