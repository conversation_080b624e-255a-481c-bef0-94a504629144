import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import ScanBarcodeCard from '~/components/barcode/ScanBarcodeCard';

// Mock the Scan icon component
jest.mock('~/components/icons/Scan', () => {
  return ({ testID: _testID }: any) => {
    const _React = require('react');
    return _React.createElement('View', { testID: 'scan-icon' });
  };
});

// Mock the TextInput component
jest.mock('~/components/inputs/TextInput', () => {
  return ({ id, testID, value, onChangeText, placeholder }: any) => {
    const _React = require('react');
    return _React.createElement('TextInput', {
      testID: id || testID,
      value,
      onChangeText,
      placeholder,
    });
  };
});

// Mock localization
jest.mock('~/localization/en', () => ({
  barcode: 'Barcode',
  re_scan: 'Rescan',
  scan_barcode_title: 'Scan barcode',
}));

describe('ScanBarcodeCard', () => {
  const defaultProps = {
    barcode: '',
    onScan: jest.fn(),
    setBarcode: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    it('renders without crashing', () => {
      const { getByText } = render(<ScanBarcodeCard {...defaultProps} />);
      expect(getByText('Barcode')).toBeTruthy();
    });

    it('renders the barcode title', () => {
      const { getByText } = render(<ScanBarcodeCard {...defaultProps} />);
      expect(getByText('Barcode')).toBeTruthy();
    });

    it('renders scan placeholder when no barcode is provided', () => {
      const { getByTestId } = render(<ScanBarcodeCard {...defaultProps} />);
      expect(
        getByTestId('ScanBarcodeCard.TouchableOpacity.barcode'),
      ).toBeTruthy();
      expect(getByTestId('scan-icon')).toBeTruthy();
    });

    it('renders TextInput when barcode is provided', () => {
      const props = { ...defaultProps, barcode: '123456789' };
      const { getByTestId } = render(<ScanBarcodeCard {...props} />);
      expect(getByTestId('ScanBarcodeCard.InputField.barcode')).toBeTruthy();
    });

    it('renders re-scan button when barcode is provided', () => {
      const props = { ...defaultProps, barcode: '123456789' };
      const { getByText, getByTestId } = render(<ScanBarcodeCard {...props} />);
      expect(getByText('Rescan')).toBeTruthy();
      expect(
        getByTestId('ScanBarcodeCard.TouchableOpacity.re-scan'),
      ).toBeTruthy();
    });

    it('does not render re-scan button when no barcode is provided', () => {
      const { queryByText, queryByTestId } = render(
        <ScanBarcodeCard {...defaultProps} />,
      );
      expect(queryByText('Rescan')).toBeNull();
      expect(
        queryByTestId('ScanBarcodeCard.TouchableOpacity.re-scan'),
      ).toBeNull();
    });
  });

  describe('User Interactions', () => {
    it('calls onScan when scan placeholder is pressed', () => {
      const { getByTestId } = render(<ScanBarcodeCard {...defaultProps} />);
      const scanButton = getByTestId(
        'ScanBarcodeCard.TouchableOpacity.barcode',
      );

      fireEvent.press(scanButton);

      expect(defaultProps.onScan).toHaveBeenCalledTimes(1);
    });

    it('calls onScan when re-scan button is pressed', () => {
      const props = { ...defaultProps, barcode: '123456789' };
      const { getByTestId } = render(<ScanBarcodeCard {...props} />);
      const rescanButton = getByTestId(
        'ScanBarcodeCard.TouchableOpacity.re-scan',
      );

      fireEvent.press(rescanButton);

      expect(defaultProps.onScan).toHaveBeenCalledTimes(1);
    });

    it('calls setBarcode when TextInput value changes', () => {
      const props = { ...defaultProps, barcode: '123456789' };
      const { getByTestId } = render(<ScanBarcodeCard {...props} />);
      const textInput = getByTestId('ScanBarcodeCard.InputField.barcode');

      fireEvent.changeText(textInput, '987654321');

      expect(defaultProps.setBarcode).toHaveBeenCalledWith('987654321');
    });

    it('handles multiple scan button presses', () => {
      const { getByTestId } = render(<ScanBarcodeCard {...defaultProps} />);
      const scanButton = getByTestId(
        'ScanBarcodeCard.TouchableOpacity.barcode',
      );

      fireEvent.press(scanButton);
      fireEvent.press(scanButton);
      fireEvent.press(scanButton);

      expect(defaultProps.onScan).toHaveBeenCalledTimes(3);
    });

    it('handles multiple re-scan button presses', () => {
      const props = { ...defaultProps, barcode: '123456789' };
      const { getByTestId } = render(<ScanBarcodeCard {...props} />);
      const rescanButton = getByTestId(
        'ScanBarcodeCard.TouchableOpacity.re-scan',
      );

      fireEvent.press(rescanButton);
      fireEvent.press(rescanButton);
      fireEvent.press(rescanButton);

      expect(defaultProps.onScan).toHaveBeenCalledTimes(3);
    });
  });

  describe('State Transitions', () => {
    it('transitions from scan placeholder to TextInput when barcode is provided', () => {
      const { rerender, getByTestId, queryByTestId } = render(
        <ScanBarcodeCard {...defaultProps} />,
      );

      // Initially shows scan placeholder
      expect(
        getByTestId('ScanBarcodeCard.TouchableOpacity.barcode'),
      ).toBeTruthy();
      expect(queryByTestId('ScanBarcodeCard.InputField.barcode')).toBeNull();

      // Rerender with barcode
      rerender(<ScanBarcodeCard {...defaultProps} barcode="123456789" />);

      // Now shows TextInput and re-scan button
      expect(
        queryByTestId('ScanBarcodeCard.TouchableOpacity.barcode'),
      ).toBeNull();
      expect(getByTestId('ScanBarcodeCard.InputField.barcode')).toBeTruthy();
      expect(
        getByTestId('ScanBarcodeCard.TouchableOpacity.re-scan'),
      ).toBeTruthy();
    });

    it('transitions from TextInput to scan placeholder when barcode is cleared', () => {
      const { rerender, getByTestId, queryByTestId } = render(
        <ScanBarcodeCard {...defaultProps} barcode="123456789" />,
      );

      // Initially shows TextInput
      expect(getByTestId('ScanBarcodeCard.InputField.barcode')).toBeTruthy();
      expect(
        queryByTestId('ScanBarcodeCard.TouchableOpacity.barcode'),
      ).toBeNull();

      // Rerender without barcode
      rerender(<ScanBarcodeCard {...defaultProps} barcode="" />);

      // Now shows scan placeholder
      expect(
        getByTestId('ScanBarcodeCard.TouchableOpacity.barcode'),
      ).toBeTruthy();
      expect(queryByTestId('ScanBarcodeCard.InputField.barcode')).toBeNull();
    });
  });

  describe('Edge Cases', () => {
    it('handles empty string barcode', () => {
      const { getByTestId, queryByTestId } = render(
        <ScanBarcodeCard {...defaultProps} barcode="" />,
      );

      expect(
        getByTestId('ScanBarcodeCard.TouchableOpacity.barcode'),
      ).toBeTruthy();
      expect(queryByTestId('ScanBarcodeCard.InputField.barcode')).toBeNull();
    });

    it('handles null barcode', () => {
      const { getByTestId, queryByTestId } = render(
        <ScanBarcodeCard {...defaultProps} barcode={null as any} />,
      );

      expect(
        getByTestId('ScanBarcodeCard.TouchableOpacity.barcode'),
      ).toBeTruthy();
      expect(queryByTestId('ScanBarcodeCard.InputField.barcode')).toBeNull();
    });

    it('handles undefined barcode', () => {
      const { getByTestId, queryByTestId } = render(
        <ScanBarcodeCard {...defaultProps} barcode={undefined as any} />,
      );

      expect(
        getByTestId('ScanBarcodeCard.TouchableOpacity.barcode'),
      ).toBeTruthy();
      expect(queryByTestId('ScanBarcodeCard.InputField.barcode')).toBeNull();
    });

    it('handles very long barcode values', () => {
      const longBarcode = 'A'.repeat(1000);
      const props = { ...defaultProps, barcode: longBarcode };
      const { getByTestId } = render(<ScanBarcodeCard {...props} />);

      expect(getByTestId('ScanBarcodeCard.InputField.barcode')).toBeTruthy();
    });

    it('handles special characters in barcode', () => {
      const specialBarcode = '!@#$%^&*()_+-=[]{}|;:,.<>?';
      const props = { ...defaultProps, barcode: specialBarcode };
      const { getByTestId } = render(<ScanBarcodeCard {...props} />);

      expect(getByTestId('ScanBarcodeCard.InputField.barcode')).toBeTruthy();
    });

    it('handles numeric barcode values', () => {
      const numericBarcode = '12345678901234567890';
      const props = { ...defaultProps, barcode: numericBarcode };
      const { getByTestId } = render(<ScanBarcodeCard {...props} />);

      expect(getByTestId('ScanBarcodeCard.InputField.barcode')).toBeTruthy();
    });
  });

  describe('Accessibility', () => {
    it('has correct testID for scan button', () => {
      const { getByTestId } = render(<ScanBarcodeCard {...defaultProps} />);
      expect(
        getByTestId('ScanBarcodeCard.TouchableOpacity.barcode'),
      ).toBeTruthy();
    });

    it('has correct testID for re-scan button', () => {
      const props = { ...defaultProps, barcode: '123456789' };
      const { getByTestId } = render(<ScanBarcodeCard {...props} />);
      expect(
        getByTestId('ScanBarcodeCard.TouchableOpacity.re-scan'),
      ).toBeTruthy();
    });

    it('has correct testID for TextInput', () => {
      const props = { ...defaultProps, barcode: '123456789' };
      const { getByTestId } = render(<ScanBarcodeCard {...props} />);
      expect(getByTestId('ScanBarcodeCard.InputField.barcode')).toBeTruthy();
    });
  });

  describe('Props Validation', () => {
    it('calls onScan function when provided', () => {
      const onScan = jest.fn();
      const { getByTestId } = render(
        <ScanBarcodeCard {...defaultProps} onScan={onScan} />,
      );

      const scanButton = getByTestId(
        'ScanBarcodeCard.TouchableOpacity.barcode',
      );
      fireEvent.press(scanButton);

      expect(onScan).toHaveBeenCalledTimes(1);
    });

    it('calls setBarcode function when provided', () => {
      const setBarcode = jest.fn();
      const props = { ...defaultProps, barcode: '123456789', setBarcode };
      const { getByTestId } = render(<ScanBarcodeCard {...props} />);

      const textInput = getByTestId('ScanBarcodeCard.InputField.barcode');
      fireEvent.changeText(textInput, 'new-barcode');

      expect(setBarcode).toHaveBeenCalledWith('new-barcode');
    });

    it('displays the correct barcode value in TextInput', () => {
      const barcodeValue = 'TEST123456';
      const props = { ...defaultProps, barcode: barcodeValue };
      const { getByTestId } = render(<ScanBarcodeCard {...props} />);

      const textInput = getByTestId('ScanBarcodeCard.InputField.barcode');
      expect(textInput.props.value).toBe(barcodeValue);
    });
  });
});
