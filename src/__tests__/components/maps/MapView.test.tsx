import React from 'react';
import { render } from '@testing-library/react-native';
import MapView from '~/components/maps/MapView';

jest.mock('@rnmapbox/maps', () => {
  const { View } = require('react-native');

  return {
    MapView: ({ children, testID }: any) => (
      <View testID={testID}>{children}</View>
    ),
    Camera: () => null,
    PointAnnotation: ({ children }: any) => <>{children}</>,
  };
});

jest.mock('~/components/icons/MapPinStop', () => 'MapPinStop');

describe('MapView', () => {
  const coordinate: [number, number] = [-112.0016533, 33.6401096];

  it('renders map container, map, and marker', () => {
    const { getByTestId } = render(<MapView coordinate={coordinate} />);

    expect(getByTestId('MapView.Container')).toBeTruthy();
    expect(getByTestId('MapView.Map')).toBeTruthy();
    expect(getByTestId('MapView.Marker.Pin')).toBeTruthy();
  });
});
