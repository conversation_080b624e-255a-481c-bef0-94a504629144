import React from 'react';
import { render, fireEvent, act, waitFor } from '@testing-library/react-native';
import ToastMessage from '~/components/toast/ToastMessage';
import { ToastProvider, useToast } from '~/components/toast/ToastProvider';
import { Text as RNText } from 'react-native';

// Mock Animated to prevent timing issues in tests
jest.mock('react-native/Libraries/Animated/NativeAnimatedHelper');

const renderWithProvider = (ui: React.ReactElement) => {
  return render(<ToastProvider>{ui}</ToastProvider>);
};

// Test component that uses the useToast hook
const TestComponent = ({
  onShowToast,
}: {
  onShowToast: (toast: any) => void;
}) => {
  const { showToast, hideToast } = useToast();

  React.useEffect(() => {
    onShowToast({ showToast, hideToast });
  }, [onShowToast, showToast, hideToast]);

  return null;
};

describe('ToastMessage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.runOnlyPendingTimers();
    jest.clearAllTimers();
    jest.useRealTimers();
  });

  it('renders success toast message', async () => {
    const { getByTestId } = renderWithProvider(
      <ToastMessage
        type="success"
        message="Success!"
        variant="toast"
        visible
        testID="Toast"
      />,
    );
    await act(async () => {
      jest.runAllTimers();
    });
    expect(getByTestId('Toast.Container')).toBeTruthy();
    expect(getByTestId('Toast.Text.message').props.children).toContain(
      'Success!',
    );
    expect(getByTestId('Toast.Icon')).toBeTruthy();
  });

  it('renders error banner message with linkText', async () => {
    const { getByTestId } = renderWithProvider(
      <ToastMessage
        type="error"
        message="Something went wrong"
        variant="banner"
        linkText="Retry"
        visible
        testID="Toast"
      />,
    );
    await act(async () => {
      jest.runAllTimers();
    });
    expect(getByTestId('Toast.Text.message').props.children).toContain(
      'Something went wrong',
    );
    expect(getByTestId('Toast.Text.linkText').props.children).toBe('Retry');
    expect(getByTestId('Toast.Icon')).toBeTruthy();
  });

  it('renders warning toast message', async () => {
    const { getByTestId } = renderWithProvider(
      <ToastMessage
        type="warning"
        message="Warning message"
        variant="toast"
        visible
        testID="Toast"
      />,
    );
    await act(async () => {
      jest.runAllTimers();
    });
    expect(getByTestId('Toast.Container')).toBeTruthy();
    expect(getByTestId('Toast.Text.message').props.children).toContain(
      'Warning message',
    );
    expect(getByTestId('Toast.Icon')).toBeTruthy();
  });

  it('does not render when visible is false', () => {
    const { queryByTestId } = renderWithProvider(
      <ToastMessage
        type="warning"
        message="Careful!"
        visible={false}
        testID="Toast"
      />,
    );

    expect(queryByTestId('Toast.Container')).toBeNull();
  });

  it('calls onPress when banner is pressed', async () => {
    const mockPress = jest.fn();
    const { getByRole } = renderWithProvider(
      <ToastMessage
        type="warning"
        message="Be aware"
        variant="banner"
        onPress={mockPress}
        visible
        testID="Toast"
      />,
    );
    await act(async () => {
      jest.runAllTimers();
    });
    fireEvent.press(getByRole('button'));
    expect(mockPress).toHaveBeenCalled();
  });

  it('renders toast without linkText', async () => {
    const { getByTestId, queryByTestId } = renderWithProvider(
      <ToastMessage
        type="success"
        message="Simple message"
        variant="toast"
        visible
        testID="Toast"
      />,
    );
    await act(async () => {
      jest.runAllTimers();
    });
    expect(getByTestId('Toast.Container')).toBeTruthy();
    expect(getByTestId('Toast.Text.message').props.children).toContain(
      'Simple message',
    );
    expect(queryByTestId('Toast.Text.linkText')).toBeNull();
  });
});

describe('ToastProvider', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterEach(() => {
    // Clear any remaining timeouts and wait for them to complete
    jest.clearAllTimers();
    // Give time for any pending operations to complete
    return new Promise(resolve => setTimeout(resolve, 100));
  });

  it('renders children without toast initially', () => {
    const { getByText, queryByTestId } = renderWithProvider(
      <RNText>Test Content</RNText>,
    );

    expect(getByText('Test Content')).toBeTruthy();
    expect(queryByTestId('ToastMessage.Container')).toBeNull();
  });

  it('shows toast when showToast is called', async () => {
    let toastFunctions: any;

    const { getByTestId } = renderWithProvider(
      <TestComponent
        onShowToast={functions => {
          toastFunctions = functions;
        }}
      />,
    );

    // Wait for the TestComponent to set up
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    await act(async () => {
      toastFunctions.showToast({
        type: 'success',
        message: 'Test message',
        variant: 'toast',
      });
    });

    // Wait for the toast to render and animate
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 350));
    });

    await waitFor(
      () => {
        expect(getByTestId('ToastMessage.Container')).toBeTruthy();
      },
      { timeout: 1000 },
    );

    expect(getByTestId('ToastMessage.Text.message').props.children).toContain(
      'Test message',
    );
  });

  it('hides toast when hideToast is called', async () => {
    let toastFunctions: any;

    const { getByTestId, queryByTestId } = renderWithProvider(
      <TestComponent
        onShowToast={functions => {
          toastFunctions = functions;
        }}
      />,
    );

    // Wait for the TestComponent to set up
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    await act(async () => {
      toastFunctions.showToast({
        type: 'success',
        message: 'Test message',
        variant: 'toast',
      });
    });

    // Wait for the toast to render
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 350));
    });

    await waitFor(
      () => {
        expect(getByTestId('ToastMessage.Container')).toBeTruthy();
      },
      { timeout: 1000 },
    );

    await act(async () => {
      toastFunctions.hideToast();
    });

    // Wait for the toast to hide
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 350));
    });

    await waitFor(
      () => {
        expect(queryByTestId('ToastMessage.Container')).toBeNull();
      },
      { timeout: 1000 },
    );
  });

  it('replaces existing toast when showToast is called again', async () => {
    let toastFunctions: any;

    const { getByTestId } = renderWithProvider(
      <TestComponent
        onShowToast={functions => {
          toastFunctions = functions;
        }}
      />,
    );

    // Wait for the TestComponent to set up
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    await act(async () => {
      toastFunctions.showToast({
        type: 'success',
        message: 'First message',
        variant: 'toast',
      });
    });

    // Wait for the first toast to render
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 350));
    });

    await waitFor(
      () => {
        expect(
          getByTestId('ToastMessage.Text.message').props.children,
        ).toContain('First message');
      },
      { timeout: 1000 },
    );

    await act(async () => {
      toastFunctions.showToast({
        type: 'error',
        message: 'Second message',
        variant: 'banner',
      });
    });

    // Wait for the second toast to render
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 350));
    });

    await waitFor(
      () => {
        expect(
          getByTestId('ToastMessage.Text.message').props.children,
        ).toContain('Second message');
      },
      { timeout: 1000 },
    );
  });

  it('clears timeout when hideToast is called', async () => {
    let toastFunctions: any;
    const clearTimeoutSpy = jest.spyOn(global, 'clearTimeout');

    renderWithProvider(
      <TestComponent
        onShowToast={functions => {
          toastFunctions = functions;
        }}
      />,
    );

    // Wait for the TestComponent to set up
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    await act(async () => {
      toastFunctions.showToast({
        type: 'success',
        message: 'Test message',
        variant: 'toast',
      });
    });

    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 350));
    });

    await act(async () => {
      toastFunctions.hideToast();
    });

    expect(clearTimeoutSpy).toHaveBeenCalled();
  });

  it('shows toast with all optional props', async () => {
    let toastFunctions: any;
    const mockOnPress = jest.fn();

    const { getByTestId } = renderWithProvider(
      <TestComponent
        onShowToast={functions => {
          toastFunctions = functions;
        }}
      />,
    );

    // Wait for the TestComponent to set up
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    await act(async () => {
      toastFunctions.showToast({
        type: 'warning',
        message: 'Warning with link',
        variant: 'banner',
        linkText: 'Click here',
        onPress: mockOnPress,
        duration: 2000,
      });
    });

    // Wait for the toast to render
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 350));
    });

    await waitFor(
      () => {
        expect(getByTestId('ToastMessage.Container')).toBeTruthy();
      },
      { timeout: 1000 },
    );

    expect(getByTestId('ToastMessage.Text.message').props.children).toContain(
      'Warning with link',
    );
    expect(getByTestId('ToastMessage.Text.linkText').props.children).toBe(
      'Click here',
    );
  });
});

describe('useToast', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Suppress console.error for this specific test
    jest.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  it('throws error when used outside ToastProvider', () => {
    const TestComponentWithoutProvider = () => {
      useToast();
      return null;
    };

    expect(() => {
      render(<TestComponentWithoutProvider />);
    }).toThrow('useToast must be used within a ToastProvider');
  });

  it('provides showToast and hideToast functions', () => {
    let toastFunctions: any;

    renderWithProvider(
      <TestComponent
        onShowToast={functions => {
          toastFunctions = functions;
        }}
      />,
    );

    expect(toastFunctions.showToast).toBeInstanceOf(Function);
    expect(toastFunctions.hideToast).toBeInstanceOf(Function);
  });
});
