import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import DateCard from '~/components/cards/DateCard';

// Mock icon components
jest.mock('~/components/icons/Arrow', () => {
  const _React = require('react');
  return ({ color }: any) =>
    _React.createElement('View', {
      testID: 'Arrow',
      style: { color },
    });
});

jest.mock('~/components/icons/Calendar', () => {
  const _React = require('react');
  return ({ color }: any) =>
    _React.createElement('View', {
      testID: 'Calendar',
      style: { color },
    });
});

// Mock localization
jest.mock('~/localization/en', () => ({
  see_schedule: 'See schedule',
}));

describe('DateCard', () => {
  const mockOnItemPress = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders without crashing', () => {
    const { getByTestId } = render(<DateCard date="2023-01-01" />);
    expect(getByTestId('DateCard.Container')).toBeTruthy();
  });

  it('displays the date prop', () => {
    const { getByText } = render(<DateCard date="2023-01-01" />);
    expect(getByText('2023-01-01')).toBeTruthy();
  });

  it('displays the see schedule text', () => {
    const { getByText } = render(<DateCard date="2023-01-01" />);
    expect(getByText('See schedule')).toBeTruthy();
  });

  it('renders calendar icon', () => {
    const { getByTestId } = render(<DateCard date="2023-01-01" />);
    expect(getByTestId('Calendar')).toBeTruthy();
  });

  it('renders arrow icon', () => {
    const { getByTestId } = render(<DateCard date="2023-01-01" />);
    expect(getByTestId('Arrow')).toBeTruthy();
  });

  it('calls onItemPress when pressed', () => {
    const { getByTestId } = render(
      <DateCard date="2023-01-01" onItemPress={mockOnItemPress} />,
    );

    const container = getByTestId('DateCard.Container');
    fireEvent.press(container);

    expect(mockOnItemPress).toHaveBeenCalledWith('2023-01-01');
  });

  it('does not call onItemPress when not provided', () => {
    const { getByTestId } = render(<DateCard date="2023-01-01" />);

    const container = getByTestId('DateCard.Container');
    fireEvent.press(container);

    expect(mockOnItemPress).not.toHaveBeenCalled();
  });

  it('handles different date formats', () => {
    const { getByText } = render(<DateCard date="2024-12-25" />);
    expect(getByText('2024-12-25')).toBeTruthy();
  });

  it('handles long date strings', () => {
    const longDate = '2023-01-01-very-long-date-string';
    const { getByText } = render(<DateCard date={longDate} />);
    expect(getByText(longDate)).toBeTruthy();
  });

  it('handles special characters in date', () => {
    const specialDate = '2023-01-01 (Special)';
    const { getByText } = render(<DateCard date={specialDate} />);
    expect(getByText(specialDate)).toBeTruthy();
  });

  it('handles empty date string', () => {
    const { getByText } = render(<DateCard date="" />);
    expect(getByText('')).toBeTruthy();
  });

  it('handles numeric date', () => {
    const { getByText } = render(<DateCard date={20230101 as any} />);
    expect(getByText('20230101')).toBeTruthy();
  });

  it('maintains consistent styling with different dates', () => {
    const { getByTestId: getByTestId1 } = render(
      <DateCard date="2023-01-01" />,
    );
    const { getByTestId: getByTestId2 } = render(
      <DateCard date="2024-12-25" />,
    );

    expect(getByTestId1('DateCard.Container')).toBeTruthy();
    expect(getByTestId2('DateCard.Container')).toBeTruthy();
  });

  it('handles rapid press events', () => {
    const { getByTestId } = render(
      <DateCard date="2023-01-01" onItemPress={mockOnItemPress} />,
    );

    const container = getByTestId('DateCard.Container');

    // Rapidly press multiple times
    fireEvent.press(container);
    fireEvent.press(container);
    fireEvent.press(container);

    expect(mockOnItemPress).toHaveBeenCalledTimes(3);
    expect(mockOnItemPress).toHaveBeenCalledWith('2023-01-01');
  });

  it('handles date with spaces', () => {
    const dateWithSpaces = '2023 01 01';
    const { getByText } = render(<DateCard date={dateWithSpaces} />);
    expect(getByText(dateWithSpaces)).toBeTruthy();
  });

  it('handles date with special formatting', () => {
    const formattedDate = '01/01/2023';
    const { getByText } = render(<DateCard date={formattedDate} />);
    expect(getByText(formattedDate)).toBeTruthy();
  });

  it('handles very long date strings', () => {
    const veryLongDate =
      '2023-01-01-this-is-a-very-long-date-string-that-might-overflow';
    const { getByText } = render(<DateCard date={veryLongDate} />);
    expect(getByText(veryLongDate)).toBeTruthy();
  });

  it('handles date with emojis', () => {
    const dateWithEmojis = '2023-01-01 🎉';
    const { getByText } = render(<DateCard date={dateWithEmojis} />);
    expect(getByText(dateWithEmojis)).toBeTruthy();
  });

  it('handles date with HTML-like characters', () => {
    const dateWithHTML = '2023-01-01 <test>';
    const { getByText } = render(<DateCard date={dateWithHTML} />);
    expect(getByText(dateWithHTML)).toBeTruthy();
  });
});
