import React from 'react';
import { render, screen } from '@testing-library/react-native';
import StopInfoItemCard from '~/components/cards/StopInfoItemCard';
import { Camera } from '~/components/icons';

describe('StopInfoItemCard', () => {
  const defaultProps = {
    title: 'Test Title',
    value: 'Test Value',
  };

  it('renders with title and value', () => {
    render(<StopInfoItemCard {...defaultProps} />);

    expect(screen.getByText('Test Title')).toBeTruthy();
    expect(screen.getByText('Test Value')).toBeTruthy();
  });

  it('renders without icon when icon prop is not provided', () => {
    render(<StopInfoItemCard {...defaultProps} />);

    // The component should render without throwing errors when no icon is provided
    expect(screen.getByText('Test Title')).toBeTruthy();
    expect(screen.getByText('Test Value')).toBeTruthy();
  });

  it('renders with icon when icon prop is provided', () => {
    const testIcon = <Camera width={24} height={24} />;

    render(<StopInfoItemCard {...defaultProps} icon={testIcon} />);

    expect(screen.getByText('Test Title')).toBeTruthy();
    expect(screen.getByText('Test Value')).toBeTruthy();
    // The icon should be rendered (we can't easily test the icon itself, but the component should render without errors)
  });

  it('renders with empty string values', () => {
    render(<StopInfoItemCard title="" value="" />);

    // Both title and value are empty strings, so we expect the component to render
    // without throwing errors, even with empty content
    const emptyTextElements = screen.getAllByText('');
    expect(emptyTextElements).toHaveLength(2); // One for title, one for value
  });

  it('renders with long text values', () => {
    const longTitle =
      'This is a very long title that might wrap to multiple lines';
    const longValue =
      'This is a very long value that contains a lot of text and might also wrap to multiple lines';

    render(<StopInfoItemCard title={longTitle} value={longValue} />);

    expect(screen.getByText(longTitle)).toBeTruthy();
    expect(screen.getByText(longValue)).toBeTruthy();
  });

  it('renders with special characters in text', () => {
    const titleWithSpecialChars = 'Title with @#$%^&*() characters';
    const valueWithSpecialChars = 'Value with 🚀 emoji and special chars!';

    render(
      <StopInfoItemCard
        title={titleWithSpecialChars}
        value={valueWithSpecialChars}
      />,
    );

    expect(screen.getByText(titleWithSpecialChars)).toBeTruthy();
    expect(screen.getByText(valueWithSpecialChars)).toBeTruthy();
  });

  it('renders with numeric values as strings', () => {
    render(<StopInfoItemCard title="Count" value="123" />);

    expect(screen.getByText('Count')).toBeTruthy();
    expect(screen.getByText('123')).toBeTruthy();
  });

  it('renders with null icon', () => {
    render(<StopInfoItemCard {...defaultProps} icon={null} />);

    expect(screen.getByText('Test Title')).toBeTruthy();
    expect(screen.getByText('Test Value')).toBeTruthy();
  });

  it('renders with undefined icon', () => {
    render(<StopInfoItemCard {...defaultProps} icon={undefined} />);

    expect(screen.getByText('Test Title')).toBeTruthy();
    expect(screen.getByText('Test Value')).toBeTruthy();
  });

  it('renders with complex icon component', () => {
    const complexIcon = <Camera width={32} height={32} color="red" />;

    render(<StopInfoItemCard {...defaultProps} icon={complexIcon} />);

    expect(screen.getByText('Test Title')).toBeTruthy();
    expect(screen.getByText('Test Value')).toBeTruthy();
  });
});
