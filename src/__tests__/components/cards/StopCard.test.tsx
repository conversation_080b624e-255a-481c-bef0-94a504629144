import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import StopCard from '~/components/cards/StopCard';

// Mock icon components
jest.mock('~/components/icons/Arrow', () => {
  const _React = require('react');
  return ({ color }: any) =>
    _React.createElement('View', {
      testID: 'Arrow',
      style: { color },
    });
});

jest.mock('~/components/icons/StopWatch', () => {
  const _React = require('react');
  return ({ color }: any) =>
    _React.createElement('View', {
      testID: 'StopWatch',
      style: { color },
    });
});

// Mock localization
jest.mock('~/localization/en', () => ({
  see_stop_details: 'See stop details',
  no_start_time_set: 'No start time set',
  invalidData: 'Invalid data',
}));

// Mock date utility
jest.mock('~/utils/dateAndTime', () => ({
  getFormattedTime: jest.fn(time => `Formatted: ${time}`),
}));

describe('StopCard', () => {
  const mockStop = {
    Id: '1',
    Name: 'Test Stop',
    Status__c: 'Active',
    Stop_Time_Preferred__c: '09:00:00',
  } as any;

  const mockStopWithNullTime = {
    Id: '2',
    Name: 'Central Station',
    Status__c: 'Pending',
    Stop_Time_Preferred__c: null,
  } as any;

  const chipColor: [string, string] = ['#000', '#fff'];
  const mockOnItemPress = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders without crashing', () => {
    const { getByTestId } = render(
      <StopCard stop={mockStop} stepperChipColor={chipColor} />,
    );
    expect(getByTestId('StopCard.Container')).toBeTruthy();
  });

  it('displays the stop name', () => {
    const { getByText } = render(
      <StopCard stop={mockStop} stepperChipColor={chipColor} />,
    );
    expect(getByText('Test Stop')).toBeTruthy();
  });

  it('displays the stop status', () => {
    const { getByText } = render(
      <StopCard stop={mockStop} stepperChipColor={chipColor} />,
    );
    expect(getByText('ACTIVE')).toBeTruthy();
  });

  it('displays formatted time when available', () => {
    const { getByText } = render(
      <StopCard stop={mockStop} stepperChipColor={chipColor} />,
    );
    expect(getByText('Formatted: 09:00:00')).toBeTruthy();
  });

  it('displays no start time message when time is null', () => {
    const { getByText } = render(
      <StopCard stop={mockStopWithNullTime} stepperChipColor={chipColor} />,
    );
    expect(getByText('No start time set')).toBeTruthy();
  });

  it('displays see stop details text', () => {
    const { getByText } = render(
      <StopCard stop={mockStop} stepperChipColor={chipColor} />,
    );
    expect(getByText('See stop details')).toBeTruthy();
  });

  it('renders stopwatch icon', () => {
    const { getByTestId } = render(
      <StopCard stop={mockStop} stepperChipColor={chipColor} />,
    );
    expect(getByTestId('StopWatch')).toBeTruthy();
  });

  it('renders arrow icon', () => {
    const { getByTestId } = render(
      <StopCard stop={mockStop} stepperChipColor={chipColor} />,
    );
    expect(getByTestId('Arrow')).toBeTruthy();
  });

  it('calls onItemPress when pressed', () => {
    const { getByTestId } = render(
      <StopCard
        stop={mockStop}
        stepperChipColor={chipColor}
        onItemPress={mockOnItemPress}
      />,
    );

    const container = getByTestId('StopCard.Container');
    fireEvent.press(container);

    expect(mockOnItemPress).toHaveBeenCalledWith(mockStop);
  });

  it('does not call onItemPress when not provided', () => {
    const { getByTestId } = render(
      <StopCard stop={mockStop} stepperChipColor={chipColor} />,
    );

    const container = getByTestId('StopCard.Container');
    fireEvent.press(container);

    expect(mockOnItemPress).not.toHaveBeenCalled();
  });

  it('renders as current stop with background image', () => {
    const { getByTestId } = render(
      <StopCard stop={mockStop} stepperChipColor={chipColor} current={true} />,
    );
    expect(getByTestId('StopCard.Container')).toBeTruthy();
  });

  it('displays white text when current', () => {
    const { getByText } = render(
      <StopCard stop={mockStop} stepperChipColor={chipColor} current={true} />,
    );
    expect(getByText('Test Stop')).toBeTruthy();
    expect(getByText('Formatted: 09:00:00')).toBeTruthy();
  });

  it('displays error component when stop is null', () => {
    const { getByText } = render(
      <StopCard stop={null as any} stepperChipColor={chipColor} />,
    );
    expect(getByText('Invalid data')).toBeTruthy();
  });

  it('displays error component when stop is undefined', () => {
    const { getByText } = render(
      <StopCard stop={undefined as any} stepperChipColor={chipColor} />,
    );
    expect(getByText('Invalid data')).toBeTruthy();
  });

  it('handles stop with missing properties', () => {
    const incompleteStop = { Id: '3' } as any;
    const { getByTestId } = render(
      <StopCard stop={incompleteStop} stepperChipColor={chipColor} />,
    );
    expect(getByTestId('StopCard.Container')).toBeTruthy();
  });

  it('handles stop with empty name', () => {
    const stopWithEmptyName = { ...mockStop, Name: '' };
    const { getByTestId } = render(
      <StopCard stop={stopWithEmptyName} stepperChipColor={chipColor} />,
    );
    expect(getByTestId('StopCard.Container')).toBeTruthy();
  });

  it('handles stop with empty status', () => {
    const stopWithEmptyStatus = { ...mockStop, Status__c: '' };
    const { getByTestId } = render(
      <StopCard stop={stopWithEmptyStatus} stepperChipColor={chipColor} />,
    );
    expect(getByTestId('StopCard.Container')).toBeTruthy();
  });

  it('handles stop with undefined time', () => {
    const stopWithUndefinedTime = {
      ...mockStop,
      Stop_Time_Preferred__c: undefined,
    };
    const { getByText } = render(
      <StopCard stop={stopWithUndefinedTime} stepperChipColor={chipColor} />,
    );
    expect(getByText('Formatted: undefined')).toBeTruthy();
  });

  it('handles stop with empty time string', () => {
    const stopWithEmptyTime = { ...mockStop, Stop_Time_Preferred__c: '' };
    const { getByText } = render(
      <StopCard stop={stopWithEmptyTime} stepperChipColor={chipColor} />,
    );
    expect(getByText('Formatted: ')).toBeTruthy();
  });

  it('handles rapid press events', () => {
    const { getByTestId } = render(
      <StopCard
        stop={mockStop}
        stepperChipColor={chipColor}
        onItemPress={mockOnItemPress}
      />,
    );

    const container = getByTestId('StopCard.Container');

    // Rapidly press multiple times
    fireEvent.press(container);
    fireEvent.press(container);
    fireEvent.press(container);

    expect(mockOnItemPress).toHaveBeenCalledTimes(3);
    expect(mockOnItemPress).toHaveBeenCalledWith(mockStop);
  });

  it('handles stop with very long name', () => {
    const stopWithLongName = {
      ...mockStop,
      Name: 'This is a very long stop name that might overflow the container and need to be handled properly',
    };
    const { getByText } = render(
      <StopCard stop={stopWithLongName} stepperChipColor={chipColor} />,
    );
    expect(getByText(stopWithLongName.Name)).toBeTruthy();
  });

  it('handles stop with special characters in name', () => {
    const stopWithSpecialChars = {
      ...mockStop,
      Name: 'Stop & More (Special) - 123',
    };
    const { getByText } = render(
      <StopCard stop={stopWithSpecialChars} stepperChipColor={chipColor} />,
    );
    expect(getByText(stopWithSpecialChars.Name)).toBeTruthy();
  });

  it('handles stop with emojis in name', () => {
    const stopWithEmojis = {
      ...mockStop,
      Name: '🚉 Central Station 🚉',
    };
    const { getByText } = render(
      <StopCard stop={stopWithEmojis} stepperChipColor={chipColor} />,
    );
    expect(getByText(stopWithEmojis.Name)).toBeTruthy();
  });

  it('handles different stepper chip colors', () => {
    const differentColors: [string, string] = ['#ff0000', '#00ff00'];
    const { getByTestId } = render(
      <StopCard stop={mockStop} stepperChipColor={differentColors} />,
    );
    expect(getByTestId('StopCard.Container')).toBeTruthy();
  });

  it('handles stop with numeric time', () => {
    const stopWithNumericTime = { ...mockStop, Stop_Time_Preferred__c: 90000 };
    const { getByText } = render(
      <StopCard stop={stopWithNumericTime} stepperChipColor={chipColor} />,
    );
    expect(getByText('Formatted: 90000')).toBeTruthy();
  });

  it('handles stop with boolean time', () => {
    const stopWithBooleanTime = { ...mockStop, Stop_Time_Preferred__c: true };
    const { getByText } = render(
      <StopCard stop={stopWithBooleanTime} stepperChipColor={chipColor} />,
    );
    expect(getByText('Formatted: true')).toBeTruthy();
  });

  it('handles stop with object time', () => {
    const stopWithObjectTime = {
      ...mockStop,
      Stop_Time_Preferred__c: { time: '09:00' },
    };
    const { getByText } = render(
      <StopCard stop={stopWithObjectTime} stepperChipColor={chipColor} />,
    );
    expect(getByText('Formatted: [object Object]')).toBeTruthy();
  });
});
