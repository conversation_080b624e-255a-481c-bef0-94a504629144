import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import RouteCard from '~/components/cards/RouteCard';

// Mock icon components
jest.mock('~/components/icons/Arrow', () => {
  const _React = require('react');
  return ({ color }: any) =>
    _React.createElement('View', {
      testID: 'Arrow',
      style: { color },
    });
});

jest.mock('~/components/icons/LocationPinOutlined', () => {
  const _React = require('react');
  return ({ color }: any) =>
    _React.createElement('View', {
      testID: 'LocationPinOutlined',
      style: { color },
    });
});

// Mock localization
jest.mock('~/localization/en', () => ({
  see_route_details: 'See route details',
  route: 'Route',
  invalidData: 'Invalid data',
}));

describe('RouteCard', () => {
  const mockRoute = {
    Id: '1',
    Name: 'Test Route',
    Status__c: 'Active',
    Number_of_Stops__c: 5,
    Planned_Start__c: '2023-01-01',
  } as any;

  const mockRouteWithDifferentStatus = {
    Id: '2',
    Name: 'Route 66',
    Status__c: 'Pending',
    Number_of_Stops__c: 3,
    Planned_Start__c: '2023-01-02',
  } as any;

  const chipColor: [string, string] = ['#000', '#fff'];
  const mockOnItemPress = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders without crashing', () => {
    const { getByTestId } = render(
      <RouteCard route={mockRoute} stepperChipColor={chipColor} />,
    );
    expect(getByTestId('RouteCard.Container')).toBeTruthy();
  });

  it('displays the route name', () => {
    const { getByText } = render(
      <RouteCard route={mockRoute} stepperChipColor={chipColor} />,
    );
    expect(getByText('Test Route')).toBeTruthy();
  });

  it('displays the route status', () => {
    const { getByText } = render(
      <RouteCard route={mockRoute} stepperChipColor={chipColor} />,
    );
    expect(getByText('ACTIVE')).toBeTruthy();
  });

  it('displays route text', () => {
    const { getByText } = render(
      <RouteCard route={mockRoute} stepperChipColor={chipColor} />,
    );
    expect(getByText('Route')).toBeTruthy();
  });

  it('displays see route details text', () => {
    const { getByText } = render(
      <RouteCard route={mockRoute} stepperChipColor={chipColor} />,
    );
    expect(getByText('See route details')).toBeTruthy();
  });

  it('renders location pin icon', () => {
    const { getByTestId } = render(
      <RouteCard route={mockRoute} stepperChipColor={chipColor} />,
    );
    expect(getByTestId('LocationPinOutlined')).toBeTruthy();
  });

  it('renders arrow icon', () => {
    const { getByTestId } = render(
      <RouteCard route={mockRoute} stepperChipColor={chipColor} />,
    );
    expect(getByTestId('Arrow')).toBeTruthy();
  });

  it('calls onItemPress when pressed', () => {
    const { getByTestId } = render(
      <RouteCard
        route={mockRoute}
        stepperChipColor={chipColor}
        onItemPress={mockOnItemPress}
      />,
    );

    const container = getByTestId('RouteCard.Container');
    fireEvent.press(container);

    expect(mockOnItemPress).toHaveBeenCalledWith(mockRoute);
  });

  it('does not call onItemPress when not provided', () => {
    const { getByTestId } = render(
      <RouteCard route={mockRoute} stepperChipColor={chipColor} />,
    );

    const container = getByTestId('RouteCard.Container');
    fireEvent.press(container);

    expect(mockOnItemPress).not.toHaveBeenCalled();
  });

  it('renders as current route with background image', () => {
    const { getByTestId } = render(
      <RouteCard
        route={mockRoute}
        stepperChipColor={chipColor}
        current={true}
      />,
    );
    expect(getByTestId('RouteCard.Container')).toBeTruthy();
  });

  it('displays white text when current', () => {
    const { getByText } = render(
      <RouteCard
        route={mockRoute}
        stepperChipColor={chipColor}
        current={true}
      />,
    );
    expect(getByText('Test Route')).toBeTruthy();
    expect(getByText('Route')).toBeTruthy();
  });

  it('displays error component when route is null', () => {
    const { getByText } = render(
      <RouteCard route={null as any} stepperChipColor={chipColor} />,
    );
    expect(getByText('Invalid data')).toBeTruthy();
  });

  it('displays error component when route is undefined', () => {
    const { getByText } = render(
      <RouteCard route={undefined as any} stepperChipColor={chipColor} />,
    );
    expect(getByText('Invalid data')).toBeTruthy();
  });

  it('handles route with missing properties', () => {
    const incompleteRoute = { Id: '3' } as any;
    const { getByTestId } = render(
      <RouteCard route={incompleteRoute} stepperChipColor={chipColor} />,
    );
    expect(getByTestId('RouteCard.Container')).toBeTruthy();
  });

  it('handles route with empty name', () => {
    const routeWithEmptyName = { ...mockRoute, Name: '' };
    const { getByTestId } = render(
      <RouteCard route={routeWithEmptyName} stepperChipColor={chipColor} />,
    );
    expect(getByTestId('RouteCard.Container')).toBeTruthy();
  });

  it('handles route with empty status', () => {
    const routeWithEmptyStatus = { ...mockRoute, Status__c: '' };
    const { getByTestId } = render(
      <RouteCard route={routeWithEmptyStatus} stepperChipColor={chipColor} />,
    );
    expect(getByTestId('RouteCard.Container')).toBeTruthy();
  });

  it('handles route with null number of stops', () => {
    const routeWithNullStops = { ...mockRoute, Number_of_Stops__c: null };
    const { getByTestId } = render(
      <RouteCard route={routeWithNullStops} stepperChipColor={chipColor} />,
    );
    expect(getByTestId('RouteCard.Container')).toBeTruthy();
  });

  it('handles route with undefined planned start', () => {
    const routeWithUndefinedStart = {
      ...mockRoute,
      Planned_Start__c: undefined,
    };
    const { getByTestId } = render(
      <RouteCard
        route={routeWithUndefinedStart}
        stepperChipColor={chipColor}
      />,
    );
    expect(getByTestId('RouteCard.Container')).toBeTruthy();
  });

  it('handles rapid press events', () => {
    const { getByTestId } = render(
      <RouteCard
        route={mockRoute}
        stepperChipColor={chipColor}
        onItemPress={mockOnItemPress}
      />,
    );

    const container = getByTestId('RouteCard.Container');

    // Rapidly press multiple times
    fireEvent.press(container);
    fireEvent.press(container);
    fireEvent.press(container);

    expect(mockOnItemPress).toHaveBeenCalledTimes(3);
    expect(mockOnItemPress).toHaveBeenCalledWith(mockRoute);
  });

  it('handles route with very long name', () => {
    const routeWithLongName = {
      ...mockRoute,
      Name: 'This is a very long route name that might overflow the container and need to be handled properly',
    };
    const { getByText } = render(
      <RouteCard route={routeWithLongName} stepperChipColor={chipColor} />,
    );
    expect(getByText(routeWithLongName.Name)).toBeTruthy();
  });

  it('handles route with special characters in name', () => {
    const routeWithSpecialChars = {
      ...mockRoute,
      Name: 'Route & More (Special) - 123',
    };
    const { getByText } = render(
      <RouteCard route={routeWithSpecialChars} stepperChipColor={chipColor} />,
    );
    expect(getByText(routeWithSpecialChars.Name)).toBeTruthy();
  });

  it('handles route with emojis in name', () => {
    const routeWithEmojis = {
      ...mockRoute,
      Name: '🚌 Route 66 🚌',
    };
    const { getByText } = render(
      <RouteCard route={routeWithEmojis} stepperChipColor={chipColor} />,
    );
    expect(getByText(routeWithEmojis.Name)).toBeTruthy();
  });

  it('handles different stepper chip colors', () => {
    const differentColors: [string, string] = ['#ff0000', '#00ff00'];
    const { getByTestId } = render(
      <RouteCard route={mockRoute} stepperChipColor={differentColors} />,
    );
    expect(getByTestId('RouteCard.Container')).toBeTruthy();
  });

  it('handles route with numeric number of stops', () => {
    const routeWithNumericStops = { ...mockRoute, Number_of_Stops__c: 10 };
    const { getByTestId } = render(
      <RouteCard route={routeWithNumericStops} stepperChipColor={chipColor} />,
    );
    expect(getByTestId('RouteCard.Container')).toBeTruthy();
  });

  it('handles route with zero number of stops', () => {
    const routeWithZeroStops = { ...mockRoute, Number_of_Stops__c: 0 };
    const { getByTestId } = render(
      <RouteCard route={routeWithZeroStops} stepperChipColor={chipColor} />,
    );
    expect(getByTestId('RouteCard.Container')).toBeTruthy();
  });

  it('handles route with negative number of stops', () => {
    const routeWithNegativeStops = { ...mockRoute, Number_of_Stops__c: -1 };
    const { getByTestId } = render(
      <RouteCard route={routeWithNegativeStops} stepperChipColor={chipColor} />,
    );
    expect(getByTestId('RouteCard.Container')).toBeTruthy();
  });

  it('handles route with string number of stops', () => {
    const routeWithStringStops = { ...mockRoute, Number_of_Stops__c: '5' };
    const { getByTestId } = render(
      <RouteCard route={routeWithStringStops} stepperChipColor={chipColor} />,
    );
    expect(getByTestId('RouteCard.Container')).toBeTruthy();
  });

  it('handles route with boolean number of stops', () => {
    const routeWithBooleanStops = { ...mockRoute, Number_of_Stops__c: true };
    const { getByTestId } = render(
      <RouteCard route={routeWithBooleanStops} stepperChipColor={chipColor} />,
    );
    expect(getByTestId('RouteCard.Container')).toBeTruthy();
  });

  it('handles route with object number of stops', () => {
    const routeWithObjectStops = {
      ...mockRoute,
      Number_of_Stops__c: { count: 5 },
    };
    const { getByTestId } = render(
      <RouteCard route={routeWithObjectStops} stepperChipColor={chipColor} />,
    );
    expect(getByTestId('RouteCard.Container')).toBeTruthy();
  });

  it('handles route with different status values', () => {
    const { getByText } = render(
      <RouteCard
        route={mockRouteWithDifferentStatus}
        stepperChipColor={chipColor}
      />,
    );
    expect(getByText('PENDING')).toBeTruthy();
  });

  it('handles route with numeric planned start', () => {
    const routeWithNumericStart = { ...mockRoute, Planned_Start__c: 20230101 };
    const { getByTestId } = render(
      <RouteCard route={routeWithNumericStart} stepperChipColor={chipColor} />,
    );
    expect(getByTestId('RouteCard.Container')).toBeTruthy();
  });

  it('handles route with boolean planned start', () => {
    const routeWithBooleanStart = { ...mockRoute, Planned_Start__c: true };
    const { getByTestId } = render(
      <RouteCard route={routeWithBooleanStart} stepperChipColor={chipColor} />,
    );
    expect(getByTestId('RouteCard.Container')).toBeTruthy();
  });

  it('handles route with object planned start', () => {
    const routeWithObjectStart = {
      ...mockRoute,
      Planned_Start__c: { date: '2023-01-01' },
    };
    const { getByTestId } = render(
      <RouteCard route={routeWithObjectStart} stepperChipColor={chipColor} />,
    );
    expect(getByTestId('RouteCard.Container')).toBeTruthy();
  });

  it('handles route with empty planned start', () => {
    const routeWithEmptyStart = { ...mockRoute, Planned_Start__c: '' };
    const { getByTestId } = render(
      <RouteCard route={routeWithEmptyStart} stepperChipColor={chipColor} />,
    );
    expect(getByTestId('RouteCard.Container')).toBeTruthy();
  });

  it('handles route with special characters in planned start', () => {
    const routeWithSpecialStart = {
      ...mockRoute,
      Planned_Start__c: '2023-01-01 (Special)',
    };
    const { getByTestId } = render(
      <RouteCard route={routeWithSpecialStart} stepperChipColor={chipColor} />,
    );
    expect(getByTestId('RouteCard.Container')).toBeTruthy();
  });

  it('handles route with emojis in planned start', () => {
    const routeWithEmojiStart = {
      ...mockRoute,
      Planned_Start__c: '2023-01-01 🎉',
    };
    const { getByTestId } = render(
      <RouteCard route={routeWithEmojiStart} stepperChipColor={chipColor} />,
    );
    expect(getByTestId('RouteCard.Container')).toBeTruthy();
  });
});
