import React from 'react';
import { render } from '@testing-library/react-native';
import CardHeader from '~/components/cards/CardHeader';

describe('CardHeader', () => {
  it('renders without crashing', () => {
    const { getByText } = render(<CardHeader name="test-header" />);
    expect(getByText('test-header')).toBeTruthy();
  });

  it('displays the name prop as title', () => {
    const { getByText } = render(<CardHeader name="My Card Title" />);
    expect(getByText('My Card Title')).toBeTruthy();
  });
});
