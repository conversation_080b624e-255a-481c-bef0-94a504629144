import React from 'react';
import { render, fireEvent, waitFor, act } from '@testing-library/react-native';
import { CheckInCard } from '~/components/cards/CheckInCard';

jest.mock('@react-native-async-storage/async-storage', () => ({
  setItem: jest.fn(),
  getItem: jest.fn(),
  removeItem: jest.fn(),
}));

jest.mock('~/hooks/useAppState', () => ({
  useAppState: () => {},
}));

// jest.mock('~/hooks/useTimer', () => ({
//   useTimer: () => ({
//     remainingSeconds: null,
//     setTimer: jest.fn(),
//     resetTimer: jest.fn(),
//   }),
// }));

describe('CheckInCard', () => {
  const defaultProps = {
    title: 'Test Title',
    description: 'Test Description',
    isTimerActive: false,
    isButtonEnabled: true,
    onPress: jest.fn(),
    onComplete: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  it('renders correctly without timer', () => {
    const { getByText } = render(<CheckInCard {...defaultProps} />);
    expect(getByText('Test Title')).toBeTruthy();
    expect(getByText('Test Description')).toBeTruthy();
    expect(getByText('Complete')).toBeTruthy();
  });

  it('calls onPress when button is clicked', () => {
    const { getByText } = render(<CheckInCard {...defaultProps} />);
    fireEvent.press(getByText('Complete'));
    expect(defaultProps.onPress).toHaveBeenCalled();
  });

  it('disables the button if timer is active and button is not enabled', async () => {
    const props = {
      ...defaultProps,
      durationInMinutes: 1,
      isTimerActive: true,
      isButtonEnabled: false,
      startTimestamp: Date.now(),
    };

    const { getByTestId } = render(<CheckInCard {...props} />);

    await waitFor(() => {
      const button = getByTestId('SurveyCheckInCard.Button.Complete');
      expect(button.props.accessibilityState?.disabled).toBe(true);
    });
  });

  it('renders timer when timer is active and has duration/startTimestamp', () => {
    const props = {
      ...defaultProps,
      durationInMinutes: 1,
      isTimerActive: true,
      isButtonEnabled: true,
      startTimestamp: Date.now(),
    };
    const { getByText } = render(<CheckInCard {...props} />);
    // Should render the timer text (e.g., 01:00)
    expect(getByText(/\d{2}:\d{2}/)).toBeTruthy();
  });

  it('calls onComplete when timer reaches zero', async () => {
    jest.useFakeTimers();
    const onComplete = jest.fn();
    const props = {
      ...defaultProps,
      durationInMinutes: 0.01, // ~0.6 seconds
      isTimerActive: true,
      isButtonEnabled: true,
      startTimestamp: Date.now(),
      onComplete,
    };
    render(<CheckInCard {...props} />);
    await act(async () => {
      jest.advanceTimersByTime(1000); // advance 1 second
    });
    expect(onComplete).toHaveBeenCalled();
    jest.useRealTimers();
  });

  it('button is enabled if timer is not active', () => {
    const props = {
      ...defaultProps,
      isTimerActive: false,
      isButtonEnabled: true,
    };
    const { getByTestId } = render(<CheckInCard {...props} />);
    const button = getByTestId('SurveyCheckInCard.Button.Complete');
    expect(button.props.accessibilityState?.disabled).toBe(false);
  });

  it('renders correctly with no durationInMinutes or startTimestamp', () => {
    const props = {
      ...defaultProps,
      durationInMinutes: null,
      startTimestamp: null,
    };
    const { getByText } = render(<CheckInCard {...props} />);
    expect(getByText('Test Title')).toBeTruthy();
    expect(getByText('Test Description')).toBeTruthy();
    expect(getByText('Complete')).toBeTruthy();
  });

  it('renders correctly with durationInMinutes = 0', () => {
    const props = {
      ...defaultProps,
      durationInMinutes: 0,
      isTimerActive: true,
      startTimestamp: Date.now(),
    };
    const { getByText } = render(<CheckInCard {...props} />);
    expect(getByText('Test Title')).toBeTruthy();
    expect(getByText('Test Description')).toBeTruthy();
    expect(getByText('Complete')).toBeTruthy();
  });
});
