import React from 'react';
import { render } from '@testing-library/react-native';
import CardWrapper from '~/components/cards/CardWrapper';

describe('CardWrapper', () => {
  it('renders children and applies id as testID', () => {
    const { getByTestId } = render(
      <CardWrapper id="test-card">
        <React.Fragment>
          <CardWrapper id="child-card">Child Content</CardWrapper>
          <span>Extra</span>
        </React.Fragment>
      </CardWrapper>,
    );
    expect(getByTestId('test-card.Container')).toBeTruthy();
    expect(getByTestId('child-card.Container')).toBeTruthy();
  });
});
