import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import TextInput from '~/components/inputs/TextInput';
import { ValidationType } from '~/types/component.types';
import { Text } from 'react-native';

describe('TextInput', () => {
  const mockOnChangeText = jest.fn();

  const defaultProps = {
    id: 'TextInput.TestLabel',
    label: 'Test Label',
    value: 'Initial Value',
    placeholder: 'Enter text',
    onChangeText: mockOnChangeText,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders label and placeholder correctly', () => {
    const { getByText, getByPlaceholderText } = render(
      <TextInput {...defaultProps} />,
    );

    expect(getByText('Test Label')).toBeTruthy();
    expect(getByPlaceholderText('Enter text')).toBeTruthy();
  });

  it('displays the initial value correctly', () => {
    const { getByDisplayValue } = render(<TextInput {...defaultProps} />);
    expect(getByDisplayValue('Initial Value')).toBeTruthy();
  });

  it('renders with empty initial value', () => {
    const props = {
      ...defaultProps,
      value: '',
    };
    const { getByPlaceholderText } = render(<TextInput {...props} />);
    expect(getByPlaceholderText('Enter text')).toBeTruthy();
  });

  it('renders with different label', () => {
    const props = {
      ...defaultProps,
      label: 'Custom Label',
    };
    const { getByText } = render(<TextInput {...props} />);
    expect(getByText('Custom Label')).toBeTruthy();
  });

  it('renders error message when provided', () => {
    const props = {
      ...defaultProps,
      errorMessage: 'Error Message',
    };
    const { getByText } = render(<TextInput {...props} />);
    expect(getByText('Error Message')).toBeTruthy();
  });

  it('renders hint when provided', () => {
    const props = {
      ...defaultProps,
      hint: 'This is a hint',
    };
    const { getByText } = render(<TextInput {...props} />);
    expect(getByText('This is a hint')).toBeTruthy();
  });

  it('calls onChangeText when text changes', () => {
    const { getByTestId } = render(<TextInput {...defaultProps} />);
    const input = getByTestId('TextInput.TestLabel');
    fireEvent.changeText(input, 'New Value');
    expect(mockOnChangeText).toHaveBeenCalledWith('New Value');
  });

  it('shows validation error when invalid', () => {
    const validations = [
      { type: ValidationType.REQUIRED, validationErrorMsg: 'Required field' },
    ];
    const { getByText, getByTestId } = render(
      <TextInput {...defaultProps} validations={validations} value={''} />,
    );
    const input = getByTestId('TextInput.TestLabel');
    fireEvent.changeText(input, '');
    expect(getByText('Required field')).toBeTruthy();
  });

  it('clears validation error when valid', () => {
    const validations = [
      { type: ValidationType.REQUIRED, validationErrorMsg: 'Required field' },
    ];
    const { queryByText, getByTestId } = render(
      <TextInput {...defaultProps} validations={validations} value={''} />,
    );
    const input = getByTestId('TextInput.TestLabel');
    fireEvent.changeText(input, 'Valid');
    expect(queryByText('Required field')).toBeNull();
  });

  it('renders as multiline when numberOfLines > 1', () => {
    const props = { ...defaultProps, numberOfLines: 3 };
    const { getByTestId } = render(<TextInput {...props} />);
    const input = getByTestId('TextInput.TestLabel');
    expect(input.props.multiline).toBe(true);
  });

  it('renders icon when provided', () => {
    const icon = <Text testID="icon-test">icon</Text>;
    const props = { ...defaultProps, icon };
    const { getByTestId } = render(<TextInput {...props} />);
    expect(getByTestId('icon-test')).toBeTruthy();
  });

  it('enforces maxLength of 255', () => {
    const { getByTestId } = render(<TextInput {...defaultProps} />);
    const input = getByTestId('TextInput.TestLabel');
    expect(input.props.maxLength).toBe(255);
  });

  it('applies containerStyle and inputStyle', () => {
    const containerStyle = { backgroundColor: 'red' };
    const inputStyle = { color: 'blue' };
    const props = { ...defaultProps, containerStyle, inputStyle };
    const { getByPlaceholderText } = render(<TextInput {...props} />);
    // The inputStyle is passed to the Input component's inputStyle prop
    // so we can't directly check the prop on the placeholder, but we can check no error is thrown
    expect(getByPlaceholderText('Enter text')).toBeTruthy();
  });

  it('renders without id, label, value, or onChange', () => {
    const { getByPlaceholderText } = render(
      <TextInput placeholder="No props" />,
    );
    expect(getByPlaceholderText('No props')).toBeTruthy();
  });
});
