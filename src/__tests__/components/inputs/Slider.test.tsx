import React from 'react';
import { render } from '@testing-library/react-native';
import Slider from '~/components/inputs/Slider';

describe('Slider', () => {
  const defaultProps = {
    id: 'LockboxTemperature.Slider',
    min: 50,
    max: 104,
    step: 6,
    defaultValue: 77,
    title: 'Lockbox temperature',
    suffix: '°F' as const,
  };

  it('renders the slider component', () => {
    const { getByTestId } = render(<Slider {...defaultProps} />);
    expect(getByTestId('LockboxTemperature.Slider.Container')).toBeTruthy();
  });

  it('displays the correct title', () => {
    const { getByTestId } = render(<Slider {...defaultProps} />);
    expect(getByTestId('LockboxTemperature.Slider.Title')).toHaveTextContent(
      'Lockbox temperature',
    );
  });

  it('renders the slider thumb', () => {
    const { getByTestId } = render(<Slider {...defaultProps} />);
    expect(getByTestId('LockboxTemperature.Slider.Thumb')).toBeTruthy();
  });

  it('renders the tooltip', () => {
    const { getByTestId } = render(<Slider {...defaultProps} />);
    expect(getByTestId('LockboxTemperature.Slider.Tooltip')).toBeTruthy();
  });

  it('displays the default temperature value', () => {
    const { getByTestId } = render(<Slider {...defaultProps} />);
    expect(getByTestId('LockboxTemperature.Slider.Tooltip')).toHaveTextContent(
      '77°F',
    );
  });

  it('renders the filled track', () => {
    const { getByTestId } = render(<Slider {...defaultProps} />);
    expect(getByTestId('LockboxTemperature.Slider.Track.Filled')).toBeTruthy();
  });

  it('renders the unfilled track', () => {
    const { getByTestId } = render(<Slider {...defaultProps} />);
    expect(
      getByTestId('LockboxTemperature.Slider.Track.Unfilled'),
    ).toBeTruthy();
  });

  it('renders markers correctly', () => {
    const { getByTestId } = render(<Slider {...defaultProps} />);
    expect(getByTestId('LockboxTemperature.Slider.Marker.50')).toBeTruthy();
    expect(getByTestId('LockboxTemperature.Slider.Marker.104')).toBeTruthy();
  });

  it('renders correct number of markers', () => {
    const { getAllByTestId } = render(<Slider {...defaultProps} />);
    // Markers: 50, 56, 62, 68, 74, 80, 86, 92, 98, 104 (step=6)
    expect(getAllByTestId(/LockboxTemperature\.Slider\.Marker\./).length).toBe(
      10,
    );
  });

  it('handles min = max edge case', () => {
    const props = { ...defaultProps, min: 10, max: 10, defaultValue: 10 };
    const { getByTestId } = render(<Slider {...props} />);
    expect(getByTestId('LockboxTemperature.Slider.Tooltip')).toHaveTextContent(
      '10°F',
    );
  });

  it('handles step > (max-min) edge case', () => {
    const props = {
      ...defaultProps,
      min: 0,
      max: 5,
      step: 10,
      defaultValue: 2,
    };
    const { getAllByTestId } = render(<Slider {...props} />);
    // Only one marker at min
    expect(getAllByTestId(/LockboxTemperature\.Slider\.Marker\./).length).toBe(
      1,
    );
  });

  it('clamps defaultValue below min', () => {
    const props = { ...defaultProps, defaultValue: 0 };
    const { getByTestId } = render(<Slider {...props} />);
    expect(getByTestId('LockboxTemperature.Slider.Tooltip')).toHaveTextContent(
      '50°F',
    );
  });

  it('clamps defaultValue above max', () => {
    const props = { ...defaultProps, defaultValue: 200 };
    const { getByTestId } = render(<Slider {...props} />);
    expect(getByTestId('LockboxTemperature.Slider.Tooltip')).toHaveTextContent(
      '104°F',
    );
  });

  it('renders suffix for different values', () => {
    const props = { ...defaultProps, defaultValue: 86 };
    const { getByTestId } = render(<Slider {...props} />);
    expect(getByTestId('LockboxTemperature.Slider.Tooltip')).toHaveTextContent(
      '86°F',
    );
  });

  // Pan gesture simulation is not supported in @testing-library/react-native directly,
  // but we can check that the thumb and tooltip positions update for value changes.
  it('updates thumb and tooltip position for value changes', () => {
    const props = { ...defaultProps, defaultValue: 104 };
    const { getByTestId } = render(<Slider {...props} />);
    // The thumb and tooltip should be at the max position
    expect(getByTestId('LockboxTemperature.Slider.Tooltip')).toHaveTextContent(
      '104°F',
    );
  });

  // Additional comprehensive tests
  it('handles different step values correctly', () => {
    const props = {
      ...defaultProps,
      min: 0,
      max: 100,
      step: 25,
      defaultValue: 50,
    };
    const { getAllByTestId } = render(<Slider {...props} />);
    // Markers: 0, 25, 50, 75, 100 (step=25)
    expect(getAllByTestId(/LockboxTemperature\.Slider\.Marker\./).length).toBe(
      5,
    );
  });

  it('handles negative values', () => {
    const props = {
      ...defaultProps,
      min: -10,
      max: 10,
      step: 5,
      defaultValue: 0,
    };
    const { getByTestId } = render(<Slider {...props} />);
    expect(getByTestId('LockboxTemperature.Slider.Tooltip')).toHaveTextContent(
      '0°F',
    );
  });

  it('handles decimal step values', () => {
    const props = {
      ...defaultProps,
      min: 0,
      max: 1,
      step: 0.25,
      defaultValue: 0.5,
    };
    const { getAllByTestId } = render(<Slider {...props} />);
    // Markers: 0, 0.25, 0.5, 0.75, 1
    expect(getAllByTestId(/LockboxTemperature\.Slider\.Marker\./).length).toBe(
      5,
    );
  });

  it('handles large value ranges', () => {
    const props = {
      ...defaultProps,
      min: 0,
      max: 1000,
      step: 100,
      defaultValue: 500,
    };
    const { getByTestId } = render(<Slider {...props} />);
    expect(getByTestId('LockboxTemperature.Slider.Tooltip')).toHaveTextContent(
      '500°F',
    );
  });

  it('handles min > max edge case', () => {
    const props = { ...defaultProps, min: 100, max: 50, defaultValue: 75 };
    const { getByTestId } = render(<Slider {...props} />);
    // Should clamp to min value
    expect(getByTestId('LockboxTemperature.Slider.Tooltip')).toHaveTextContent(
      '100°F',
    );
  });

  it('handles different suffix values', () => {
    const props = { ...defaultProps, suffix: '%', defaultValue: 50 };
    const { getByTestId } = render(<Slider {...props} />);
    expect(getByTestId('LockboxTemperature.Slider.Tooltip')).toHaveTextContent(
      '50%',
    );
  });

  it('handles empty suffix', () => {
    const props = { ...defaultProps, suffix: '', defaultValue: 77 };
    const { getByTestId } = render(<Slider {...props} />);
    expect(getByTestId('LockboxTemperature.Slider.Tooltip')).toHaveTextContent(
      '77',
    );
  });

  it('handles long title text', () => {
    const props = {
      ...defaultProps,
      title: 'This is a very long title that might wrap to multiple lines',
    };
    const { getByTestId } = render(<Slider {...props} />);
    expect(getByTestId('LockboxTemperature.Slider.Title')).toHaveTextContent(
      'This is a very long title that might wrap to multiple lines',
    );
  });

  it('handles empty title', () => {
    const props = { ...defaultProps, title: '' };
    const { getByTestId } = render(<Slider {...props} />);
    expect(getByTestId('LockboxTemperature.Slider.Title')).toHaveTextContent(
      '',
    );
  });

  it('handles special characters in title', () => {
    const props = { ...defaultProps, title: 'Temperature (°C/°F)' };
    const { getByTestId } = render(<Slider {...props} />);
    expect(getByTestId('LockboxTemperature.Slider.Title')).toHaveTextContent(
      'Temperature (°C/°F)',
    );
  });

  it('handles different id formats', () => {
    const props = { ...defaultProps, id: 'Custom.Slider.ID' };
    const { getByTestId } = render(<Slider {...props} />);
    expect(getByTestId('Custom.Slider.ID.Container')).toBeTruthy();
    expect(getByTestId('Custom.Slider.ID.Thumb')).toBeTruthy();
    expect(getByTestId('Custom.Slider.ID.Tooltip')).toBeTruthy();
  });

  it('handles id with special characters', () => {
    const props = { ...defaultProps, id: 'slider-with-dashes_123' };
    const { getByTestId } = render(<Slider {...props} />);
    expect(getByTestId('slider-with-dashes_123.Container')).toBeTruthy();
  });

  it('handles very small value ranges', () => {
    const props = { ...defaultProps, min: 0, max: 1, step: 1, defaultValue: 0 };
    const { getAllByTestId } = render(<Slider {...props} />);
    // Markers: 0, 1
    expect(getAllByTestId(/LockboxTemperature\.Slider\.Marker\./).length).toBe(
      2,
    );
  });

  it('handles defaultValue at exact min', () => {
    const props = { ...defaultProps, defaultValue: 50 };
    const { getByTestId } = render(<Slider {...props} />);
    expect(getByTestId('LockboxTemperature.Slider.Tooltip')).toHaveTextContent(
      '50°F',
    );
  });

  it('handles defaultValue at exact max', () => {
    const props = { ...defaultProps, defaultValue: 104 };
    const { getByTestId } = render(<Slider {...props} />);
    expect(getByTestId('LockboxTemperature.Slider.Tooltip')).toHaveTextContent(
      '104°F',
    );
  });

  it('handles defaultValue at step boundary', () => {
    const props = { ...defaultProps, defaultValue: 80 };
    const { getByTestId } = render(<Slider {...props} />);
    expect(getByTestId('LockboxTemperature.Slider.Tooltip')).toHaveTextContent(
      '80°F',
    );
  });

  it('handles very large step values', () => {
    const props = {
      ...defaultProps,
      min: 0,
      max: 100,
      step: 50,
      defaultValue: 25,
    };
    const { getAllByTestId } = render(<Slider {...props} />);
    // Markers: 0, 50, 100
    expect(getAllByTestId(/LockboxTemperature\.Slider\.Marker\./).length).toBe(
      3,
    );
  });

  it('handles step equal to range', () => {
    const props = {
      ...defaultProps,
      min: 0,
      max: 100,
      step: 100,
      defaultValue: 50,
    };
    const { getAllByTestId } = render(<Slider {...props} />);
    // Markers: 0, 100
    expect(getAllByTestId(/LockboxTemperature\.Slider\.Marker\./).length).toBe(
      2,
    );
  });

  it('handles step larger than range', () => {
    const props = {
      ...defaultProps,
      min: 0,
      max: 10,
      step: 20,
      defaultValue: 5,
    };
    const { getAllByTestId } = render(<Slider {...props} />);
    // Only min marker
    expect(getAllByTestId(/LockboxTemperature\.Slider\.Marker\./).length).toBe(
      1,
    );
  });

  it('handles floating point precision', () => {
    const props = {
      ...defaultProps,
      min: 0,
      max: 1,
      step: 0.1,
      defaultValue: 0.3,
    };
    const { getByTestId } = render(<Slider {...props} />);
    expect(getByTestId('LockboxTemperature.Slider.Tooltip')).toHaveTextContent(
      '0.3°F',
    );
  });

  it('handles very small step values', () => {
    const props = {
      ...defaultProps,
      min: 0,
      max: 1,
      step: 0.01,
      defaultValue: 0.5,
    };
    const { getAllByTestId } = render(<Slider {...props} />);
    // Should handle many markers gracefully
    expect(
      getAllByTestId(/LockboxTemperature\.Slider\.Marker\./).length,
    ).toBeGreaterThan(0);
  });

  it('handles very small step values (0.001)', () => {
    const props = {
      ...defaultProps,
      min: 0,
      max: 1,
      step: 0.001,
      defaultValue: 0.5,
    };
    const { getByTestId } = render(<Slider {...props} />);
    expect(getByTestId('LockboxTemperature.Slider.Tooltip')).toHaveTextContent(
      '0.5°F',
    );
  });

  it('handles props updates correctly', () => {
    const { getByTestId, rerender } = render(<Slider {...defaultProps} />);
    expect(getByTestId('LockboxTemperature.Slider.Tooltip')).toHaveTextContent(
      '77°F',
    );

    // Update props
    const newProps = { ...defaultProps, defaultValue: 90 };
    rerender(<Slider {...newProps} />);
    expect(getByTestId('LockboxTemperature.Slider.Tooltip')).toHaveTextContent(
      '90°F',
    );
  });

  it('handles multiple prop updates', () => {
    const { getByTestId, rerender } = render(<Slider {...defaultProps} />);

    // Update multiple props
    const newProps = {
      ...defaultProps,
      min: 0,
      max: 200,
      step: 20,
      defaultValue: 100,
      suffix: 'kg',
      title: 'Weight',
    };
    rerender(<Slider {...newProps} />);

    expect(getByTestId('LockboxTemperature.Slider.Tooltip')).toHaveTextContent(
      '100kg',
    );
    expect(getByTestId('LockboxTemperature.Slider.Title')).toHaveTextContent(
      'Weight',
    );
  });

  // Additional comprehensive tests
  it('handles extreme value ranges', () => {
    const props = {
      ...defaultProps,
      min: -1000,
      max: 1000,
      step: 100,
      defaultValue: 0,
    };
    const { getByTestId } = render(<Slider {...props} />);
    expect(getByTestId('LockboxTemperature.Slider.Tooltip')).toHaveTextContent(
      '0°F',
    );
  });

  it('handles very small ranges with decimal steps', () => {
    const props = {
      ...defaultProps,
      min: 0,
      max: 0.1,
      step: 0.01,
      defaultValue: 0.05,
    };
    const { getByTestId } = render(<Slider {...props} />);
    expect(getByTestId('LockboxTemperature.Slider.Tooltip')).toHaveTextContent(
      '0.05°F',
    );
  });

  it('handles step that divides range evenly', () => {
    const props = {
      ...defaultProps,
      min: 0,
      max: 100,
      step: 20,
      defaultValue: 60,
    };
    const { getAllByTestId } = render(<Slider {...props} />);
    // Markers: 0, 20, 40, 60, 80, 100
    expect(getAllByTestId(/LockboxTemperature\.Slider\.Marker\./).length).toBe(
      6,
    );
  });

  it('handles step that does not divide range evenly', () => {
    const props = {
      ...defaultProps,
      min: 0,
      max: 100,
      step: 30,
      defaultValue: 45,
    };
    const { getAllByTestId } = render(<Slider {...props} />);
    // Markers: 0, 30, 60, 90
    expect(getAllByTestId(/LockboxTemperature\.Slider\.Marker\./).length).toBe(
      4,
    );
  });

  it('handles defaultValue at non-step boundary', () => {
    const props = {
      ...defaultProps,
      min: 0,
      max: 100,
      step: 25,
      defaultValue: 37,
    };
    const { getByTestId } = render(<Slider {...props} />);
    expect(getByTestId('LockboxTemperature.Slider.Tooltip')).toHaveTextContent(
      '37°F',
    );
  });

  it('handles very large step values relative to range', () => {
    const props = {
      ...defaultProps,
      min: 0,
      max: 10,
      step: 15,
      defaultValue: 5,
    };
    const { getAllByTestId } = render(<Slider {...props} />);
    // Only min marker since step > range
    expect(getAllByTestId(/LockboxTemperature\.Slider\.Marker\./).length).toBe(
      1,
    );
  });

  it('handles step equal to min value', () => {
    const props = {
      ...defaultProps,
      min: 10,
      max: 100,
      step: 10,
      defaultValue: 50,
    };
    const { getAllByTestId } = render(<Slider {...props} />);
    // Markers: 10, 20, 30, 40, 50, 60, 70, 80, 90, 100
    expect(getAllByTestId(/LockboxTemperature\.Slider\.Marker\./).length).toBe(
      10,
    );
  });

  it('handles step equal to max value', () => {
    const props = {
      ...defaultProps,
      min: 0,
      max: 50,
      step: 50,
      defaultValue: 25,
    };
    const { getAllByTestId } = render(<Slider {...props} />);
    // Markers: 0, 50
    expect(getAllByTestId(/LockboxTemperature\.Slider\.Marker\./).length).toBe(
      2,
    );
  });

  it('handles step that is a fraction of the range', () => {
    const props = {
      ...defaultProps,
      min: 0,
      max: 1,
      step: 0.2,
      defaultValue: 0.6,
    };
    const { getAllByTestId } = render(<Slider {...props} />);
    // Markers: 0, 0.2, 0.4, 0.6, 0.8, 1
    expect(getAllByTestId(/LockboxTemperature\.Slider\.Marker\./).length).toBe(
      6,
    );
  });

  it('handles step that results in many markers (101 total)', () => {
    const props = {
      ...defaultProps,
      min: 0,
      max: 100,
      step: 1,
      defaultValue: 50,
    };
    const { getAllByTestId } = render(<Slider {...props} />);
    // Should handle 101 markers gracefully
    expect(getAllByTestId(/LockboxTemperature\.Slider\.Marker\./).length).toBe(
      101,
    );
  });

  it('handles defaultValue with many decimal places', () => {
    const props = {
      ...defaultProps,
      min: 0,
      max: 1,
      step: 0.1,
      defaultValue: 0.333333,
    };
    const { getByTestId } = render(<Slider {...props} />);
    expect(getByTestId('LockboxTemperature.Slider.Tooltip')).toHaveTextContent(
      '0.333333°F',
    );
  });

  it('handles suffix with special characters', () => {
    const props = { ...defaultProps, suffix: '°C/°F', defaultValue: 25 };
    const { getByTestId } = render(<Slider {...props} />);
    expect(getByTestId('LockboxTemperature.Slider.Tooltip')).toHaveTextContent(
      '50°C/°F',
    );
  });

  it('handles numeric suffix correctly', () => {
    const props = { ...defaultProps, suffix: '123', defaultValue: 50 };
    const { getByTestId } = render(<Slider {...props} />);
    expect(getByTestId('LockboxTemperature.Slider.Tooltip')).toHaveTextContent(
      '50123',
    );
  });

  it('handles suffix with spaces', () => {
    const props = { ...defaultProps, suffix: ' units', defaultValue: 75 };
    const { getByTestId } = render(<Slider {...props} />);
    expect(getByTestId('LockboxTemperature.Slider.Tooltip')).toHaveTextContent(
      '75 units',
    );
  });

  it('handles title with numbers', () => {
    const props = { ...defaultProps, title: 'Temperature 123' };
    const { getByTestId } = render(<Slider {...props} />);
    expect(getByTestId('LockboxTemperature.Slider.Title')).toHaveTextContent(
      'Temperature 123',
    );
  });

  it('handles title with emojis', () => {
    const props = { ...defaultProps, title: '🌡️ Temperature' };
    const { getByTestId } = render(<Slider {...props} />);
    expect(getByTestId('LockboxTemperature.Slider.Title')).toHaveTextContent(
      '🌡️ Temperature',
    );
  });

  it('handles id with numbers only', () => {
    const props = { ...defaultProps, id: '12345' };
    const { getByTestId } = render(<Slider {...props} />);
    expect(getByTestId('12345.Container')).toBeTruthy();
  });

  it('handles id with mixed characters', () => {
    const props = { ...defaultProps, id: 'slider_123-test.456' };
    const { getByTestId } = render(<Slider {...props} />);
    expect(getByTestId('slider_123-test.456.Container')).toBeTruthy();
  });

  it('handles defaultValue exactly at step boundary', () => {
    const props = {
      ...defaultProps,
      min: 0,
      max: 100,
      step: 25,
      defaultValue: 75,
    };
    const { getByTestId } = render(<Slider {...props} />);
    expect(getByTestId('LockboxTemperature.Slider.Tooltip')).toHaveTextContent(
      '75°F',
    );
  });

  it('handles defaultValue between step boundaries', () => {
    const props = {
      ...defaultProps,
      min: 0,
      max: 100,
      step: 25,
      defaultValue: 37,
    };
    const { getByTestId } = render(<Slider {...props} />);
    expect(getByTestId('LockboxTemperature.Slider.Tooltip')).toHaveTextContent(
      '37°F',
    );
  });

  it('handles range with only one possible value', () => {
    const props = {
      ...defaultProps,
      min: 50,
      max: 50,
      step: 1,
      defaultValue: 50,
    };
    const { getByTestId } = render(<Slider {...props} />);
    expect(getByTestId('LockboxTemperature.Slider.Tooltip')).toHaveTextContent(
      '50°F',
    );
  });

  it('handles range with two possible values', () => {
    const props = { ...defaultProps, min: 0, max: 1, step: 1, defaultValue: 0 };
    const { getAllByTestId } = render(<Slider {...props} />);
    expect(getAllByTestId(/LockboxTemperature\.Slider\.Marker\./).length).toBe(
      2,
    );
  });

  it('handles step that is larger than half the range', () => {
    const props = {
      ...defaultProps,
      min: 0,
      max: 10,
      step: 6,
      defaultValue: 5,
    };
    const { getAllByTestId } = render(<Slider {...props} />);
    // Markers: 0, 6
    expect(getAllByTestId(/LockboxTemperature\.Slider\.Marker\./).length).toBe(
      2,
    );
  });

  it('handles step that is exactly half the range', () => {
    const props = {
      ...defaultProps,
      min: 0,
      max: 10,
      step: 5,
      defaultValue: 7,
    };
    const { getAllByTestId } = render(<Slider {...props} />);
    // Markers: 0, 5, 10
    expect(getAllByTestId(/LockboxTemperature\.Slider\.Marker\./).length).toBe(
      3,
    );
  });

  it('handles step that is a third of the range', () => {
    const props = { ...defaultProps, min: 0, max: 9, step: 3, defaultValue: 6 };
    const { getAllByTestId } = render(<Slider {...props} />);
    // Markers: 0, 3, 6, 9
    expect(getAllByTestId(/LockboxTemperature\.Slider\.Marker\./).length).toBe(
      4,
    );
  });

  it('handles range that is not evenly divisible by step', () => {
    const props = { ...defaultProps, min: 0, max: 7, step: 3, defaultValue: 4 };
    const { getAllByTestId } = render(<Slider {...props} />);
    // Markers: 0, 3, 6
    expect(getAllByTestId(/LockboxTemperature\.Slider\.Marker\./).length).toBe(
      3,
    );
  });

  it('handles range that is evenly divisible by step value', () => {
    const props = { ...defaultProps, min: 0, max: 8, step: 2, defaultValue: 6 };
    const { getAllByTestId } = render(<Slider {...props} />);
    // Markers: 0, 2, 4, 6, 8
    expect(getAllByTestId(/LockboxTemperature\.Slider\.Marker\./).length).toBe(
      5,
    );
  });

  it('handles defaultValue at the very beginning of range', () => {
    const props = {
      ...defaultProps,
      min: 10,
      max: 100,
      step: 10,
      defaultValue: 10,
    };
    const { getByTestId } = render(<Slider {...props} />);
    expect(getByTestId('LockboxTemperature.Slider.Tooltip')).toHaveTextContent(
      '10°F',
    );
  });

  it('handles defaultValue at the very end of range', () => {
    const props = {
      ...defaultProps,
      min: 10,
      max: 100,
      step: 10,
      defaultValue: 100,
    };
    const { getByTestId } = render(<Slider {...props} />);
    expect(getByTestId('LockboxTemperature.Slider.Tooltip')).toHaveTextContent(
      '100°F',
    );
  });

  it('handles defaultValue in the middle of range', () => {
    const props = {
      ...defaultProps,
      min: 0,
      max: 100,
      step: 10,
      defaultValue: 50,
    };
    const { getByTestId } = render(<Slider {...props} />);
    expect(getByTestId('LockboxTemperature.Slider.Tooltip')).toHaveTextContent(
      '50°F',
    );
  });

  it('handles range with negative and positive values', () => {
    const props = {
      ...defaultProps,
      min: -50,
      max: 50,
      step: 10,
      defaultValue: 0,
    };
    const { getByTestId } = render(<Slider {...props} />);
    expect(getByTestId('LockboxTemperature.Slider.Tooltip')).toHaveTextContent(
      '0°F',
    );
  });

  it('handles range with all negative values', () => {
    const props = {
      ...defaultProps,
      min: -100,
      max: -10,
      step: 10,
      defaultValue: -50,
    };
    const { getByTestId } = render(<Slider {...props} />);
    expect(getByTestId('LockboxTemperature.Slider.Tooltip')).toHaveTextContent(
      '-50°F',
    );
  });

  it('handles range with all positive values', () => {
    const props = {
      ...defaultProps,
      min: 10,
      max: 100,
      step: 10,
      defaultValue: 55,
    };
    const { getByTestId } = render(<Slider {...props} />);
    expect(getByTestId('LockboxTemperature.Slider.Tooltip')).toHaveTextContent(
      '55°F',
    );
  });

  it('handles step that results in exactly one marker', () => {
    const props = {
      ...defaultProps,
      min: 0,
      max: 5,
      step: 10,
      defaultValue: 2,
    };
    const { getAllByTestId } = render(<Slider {...props} />);
    // Only min marker
    expect(getAllByTestId(/LockboxTemperature\.Slider\.Marker\./).length).toBe(
      1,
    );
  });

  it('handles step that results in exactly two markers', () => {
    const props = {
      ...defaultProps,
      min: 0,
      max: 10,
      step: 10,
      defaultValue: 5,
    };
    const { getAllByTestId } = render(<Slider {...props} />);
    // Markers: 0, 10
    expect(getAllByTestId(/LockboxTemperature\.Slider\.Marker\./).length).toBe(
      2,
    );
  });

  it('handles step that results in exactly three markers', () => {
    const props = {
      ...defaultProps,
      min: 0,
      max: 20,
      step: 10,
      defaultValue: 10,
    };
    const { getAllByTestId } = render(<Slider {...props} />);
    // Markers: 0, 10, 20
    expect(getAllByTestId(/LockboxTemperature\.Slider\.Marker\./).length).toBe(
      3,
    );
  });

  it('handles props update with same values', () => {
    const { getByTestId, rerender } = render(<Slider {...defaultProps} />);
    expect(getByTestId('LockboxTemperature.Slider.Tooltip')).toHaveTextContent(
      '77°F',
    );

    // Update with same values
    rerender(<Slider {...defaultProps} />);
    expect(getByTestId('LockboxTemperature.Slider.Tooltip')).toHaveTextContent(
      '77°F',
    );
  });

  it('handles props update with only title change', () => {
    const { getByTestId, rerender } = render(<Slider {...defaultProps} />);
    expect(getByTestId('LockboxTemperature.Slider.Title')).toHaveTextContent(
      'Lockbox temperature',
    );

    const newProps = { ...defaultProps, title: 'New Title' };
    rerender(<Slider {...newProps} />);
    expect(getByTestId('LockboxTemperature.Slider.Title')).toHaveTextContent(
      'New Title',
    );
  });

  it('handles props update with only suffix change', () => {
    const { getByTestId, rerender } = render(<Slider {...defaultProps} />);
    expect(getByTestId('LockboxTemperature.Slider.Tooltip')).toHaveTextContent(
      '77°F',
    );

    const newProps = { ...defaultProps, suffix: 'kg' };
    rerender(<Slider {...newProps} />);
    expect(getByTestId('LockboxTemperature.Slider.Tooltip')).toHaveTextContent(
      '77kg',
    );
  });

  it('handles props update with only id change', () => {
    const { getByTestId, rerender } = render(<Slider {...defaultProps} />);
    expect(getByTestId('LockboxTemperature.Slider.Container')).toBeTruthy();

    const newProps = { ...defaultProps, id: 'NewSlider' };
    rerender(<Slider {...newProps} />);
    expect(getByTestId('NewSlider.Container')).toBeTruthy();
  });
});
