// customOperators.types.test.ts

import { customOperators } from '~/types/customOperators.types';
import { calculateDistanceBetweenCoordinatesInMeters } from '~/utils/location';

jest.mock('~/utils/location', () => {
  const actualModule = jest.requireActual('~/utils/location');
  return {
    __esModule: true,
    ...actualModule,
    calculateDistanceBetweenCoordinatesInMeters: jest.fn(),
  };
});

describe('customOperators', () => {
  describe('isWithinGeofence', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should return true when distance is within cond.maxRadius', () => {
      (
        calculateDistanceBetweenCoordinatesInMeters as jest.Mock
      ).mockReturnValue(100);
      const cond = {
        reference: { lat: 10, lon: 20 },
        maxRadius: 200,
      };
      const targetCoord = {
        lat: 10.001, // location near reference
        lon: 20.001,
      };

      const result = customOperators.isWithinGeofence(cond, targetCoord);
      expect(result).toBe(true);
      expect(calculateDistanceBetweenCoordinatesInMeters).toHaveBeenCalled();
    });

    it('should return false when distance exceeds cond.maxRadius', () => {
      (
        calculateDistanceBetweenCoordinatesInMeters as jest.Mock
      ).mockReturnValue(1500);
      const cond = {
        reference: { lat: 10, lon: 20 },
        maxRadius: 200,
      };
      const targetCoord = {
        lat: 15,
        lon: 25,
      };

      const result = customOperators.isWithinGeofence(cond, targetCoord);
      expect(result).toBe(false);
      expect(calculateDistanceBetweenCoordinatesInMeters).toHaveBeenCalled();
    });

    it('should return false and log a warning if required data is missing', () => {
      console.warn = jest.fn();

      // Missing 'lon' in reference
      const cond = {
        reference: { lat: 10 },
        maxRadius: 200,
      };
      // Also missing 'lon' in targetCoord
      const targetCoord = {
        lat: 10.5,
      };

      const result = customOperators.isWithinGeofence(cond, targetCoord);
      expect(result).toBe(false);
      expect(console.warn).toHaveBeenCalled();
    });
  });

  describe('isWithinTimeSince', () => {
    const originalDateNow = Date.now;

    beforeAll(() => {
      // Freeze time to a known timestamp for reproducibility
      Date.now = jest.fn(() => 1_650_000_000_000);
    });

    afterAll(() => {
      // Restore Date.now
      Date.now = originalDateNow;
    });

    it('should return true if event occurred within threshold in seconds', () => {
      const cond = {
        field: 'completedAt',
        value: 300, // 5 minutes
      };
      // completedAt is 4 minutes before Date.now
      const data = new Date(1_650_000_000_000 - 4 * 60_000).toISOString();

      const result = customOperators.isWithinTimeSince(cond, data);
      expect(result).toBe(true);
    });

    it('should return false if event occurred before threshold', () => {
      const cond = {
        field: 'completedAt',
        value: 300, // 5 minutes
      };
      // completedAt is 10 minutes before Date.now
      const data = {
        completedAt: new Date(1_650_000_000_000 - 10 * 60_000).toISOString(),
      };

      const result = customOperators.isWithinTimeSince(cond, data);
      expect(result).toBe(false);
    });

    it('should return false and warn if the time field is invalid or missing', () => {
      console.warn = jest.fn();

      const cond = {
        field: 'completedTime',
        value: 300,
      };
      const data = {
        somethingElse: '2025-03-25T12:00:00Z', // missing the 'completedTime' field
      };

      const result = customOperators.isWithinTimeSince(cond, data);
      expect(result).toBe(false);
      expect(console.warn).toHaveBeenCalled();
    });

    it('should return false and warn if cond.value is not a number', () => {
      console.warn = jest.fn();

      const cond = {
        field: 'completedAt',
        value: 'notANumber',
      };
      const data = {
        completedAt: new Date(1_650_000_000_000).toISOString(),
      };

      const result = customOperators.isWithinTimeSince(cond, data);
      expect(result).toBe(false);
      expect(console.warn).toHaveBeenCalled();
    });
  });
});
