import {
  buildScenarioForEntities,
  type EntityObjectInfo,
} from '~/services/protocol/ProtocolScenarioDataService';
import { getRealmInstance } from '~/db/realm/utils/realm.config';

jest.mock('~/db/realm/utils/realm.config', () => ({
  getRealmInstance: jest.fn(),
}));
jest.mock('~/db/realm/schemas/route-summary.schema', () => ({
  RouteSummarySchema: { name: 'Route_Summary__c' },
}));
jest.mock('~/db/realm/schemas/stop.schema', () => ({
  StopSchema: { name: 'Stop__c' },
}));
jest.mock('~/db/realm/schemas/parcel.schema', () => ({
  ParcelSchema: { name: 'Parcel__c' },
}));

describe('ProtocolScenarioDataService', () => {
  let mockRealm: {
    objectForPrimaryKey: jest.Mock;
    close: jest.Mock;
  };

  const mockEntities = {
    route: {
      id: 'route-123',
      name: 'Test Route',
    },
    stop: {
      id: 'stop-456',
      name: 'Test Stop',
    },
    parcel: {
      id: 'parcel-789',
      name: 'Test Parcel',
    },
  };

  beforeEach(() => {
    mockRealm = {
      objectForPrimaryKey: jest.fn().mockReturnValue(null),
      close: jest.fn(),
    };
    (getRealmInstance as jest.Mock).mockResolvedValue(mockRealm);
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.resetAllMocks();
    (getRealmInstance as jest.Mock).mockReset();
  });

  afterAll(() => {
    jest.restoreAllMocks();
  });

  describe('Input Validation', () => {
    it('should handle empty input array', async () => {
      const scenario = await buildScenarioForEntities([]);
      expect(scenario).toEqual({});
      expect(mockRealm.objectForPrimaryKey).not.toHaveBeenCalled();
    });

    it('should return null for invalid entity IDs', async () => {
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation();

      const invalidEntities: EntityObjectInfo[] = [
        { entityName: 'Route_Summary__c', entityId: '' },
        { entityName: 'Stop__c', entityId: null as any },
        { entityName: 'Parcel__c', entityId: undefined as any },
      ];

      const scenario = await buildScenarioForEntities(invalidEntities);
      expect(scenario).toBeNull();
      expect(mockRealm.objectForPrimaryKey).not.toHaveBeenCalled();
      expect(consoleErrorSpy).toHaveBeenCalledWith(
        expect.stringContaining(
          'Failed to fetch Route_Summary__c with id  because entityId is null or empty',
        ),
      );

      consoleErrorSpy.mockRestore();
    });
  });

  describe('Entity Retrieval', () => {
    it('should return empty object for non-existent entities', async () => {
      const scenario = await buildScenarioForEntities([
        { entityName: 'Route_Summary__c', entityId: 'non-existent' },
      ]);
      expect(scenario).toEqual({});
    });

    it('should correctly retrieve and flatten a single entity', async () => {
      mockRealm.objectForPrimaryKey.mockReturnValue(mockEntities.route);

      const scenario = await buildScenarioForEntities([
        { entityName: 'Route_Summary__c', entityId: 'route-123' },
      ]);

      expect(scenario).toEqual({
        'Route_Summary__c.id': 'route-123',
        'Route_Summary__c.name': 'Test Route',
      });
    });

    it('should correctly retrieve and flatten multiple entities', async () => {
      mockRealm.objectForPrimaryKey.mockImplementation((schemaName: string) => {
        const mockData: Record<string, any> = {
          Route_Summary__c: mockEntities.route,
          Stop__c: mockEntities.stop,
          Parcel__c: mockEntities.parcel,
        };
        return mockData[schemaName] || null;
      });

      const scenario = await buildScenarioForEntities([
        { entityName: 'Route_Summary__c', entityId: 'route-123' },
        { entityName: 'Stop__c', entityId: 'stop-456' },
        { entityName: 'Parcel__c', entityId: 'parcel-789' },
      ]);

      expect(scenario).toMatchObject({
        'Route_Summary__c.id': 'route-123',
        'Stop__c.id': 'stop-456',
        'Parcel__c.id': 'parcel-789',
      });

      const expectedPropertyCount =
        Object.keys(mockEntities.route).length +
        Object.keys(mockEntities.stop).length +
        Object.keys(mockEntities.parcel).length;
      expect(Object.keys(scenario)).toHaveLength(expectedPropertyCount);
    });
  });

  describe('Error Handling', () => {
    it('should return null when database error occurs', async () => {
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation();

      mockRealm.objectForPrimaryKey.mockImplementation(() => {
        throw new Error('Database connection error');
      });

      const scenario = await buildScenarioForEntities([
        { entityName: 'Route_Summary__c', entityId: 'route-123' },
      ]);

      expect(scenario).toBeNull();
      expect(consoleErrorSpy).toHaveBeenCalledWith(
        expect.stringContaining(
          'Failed to fetch Route_Summary__c with id route-123',
        ),
        expect.any(Error),
      );

      consoleErrorSpy.mockRestore();
    });

    it('should return null when any entity fails in multi-entity request', async () => {
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation();

      mockRealm.objectForPrimaryKey.mockImplementation((schemaName: string) => {
        if (schemaName === 'Route_Summary__c') {
          throw new Error('Database error');
        }
        return mockEntities.stop;
      });

      const scenario = await buildScenarioForEntities([
        { entityName: 'Route_Summary__c', entityId: 'route-123' },
        { entityName: 'Stop__c', entityId: 'stop-456' },
      ]);

      expect(scenario).toBeNull();
      expect(consoleErrorSpy).toHaveBeenCalledWith(
        expect.stringContaining(
          'Failed to fetch Route_Summary__c with id route-123',
        ),
        expect.any(Error),
      );

      consoleErrorSpy.mockRestore();
    });
  });

  describe('Data Type Handling', () => {
    it('should handle various data types correctly', async () => {
      const testDate = new Date('2024-03-20T00:00:00.000Z');
      const complexEntity = {
        stringProp: 'test',
        numberProp: 123,
        booleanProp: true,
        nullProp: null,
        dateProp: testDate,
        arrayProp: [1, 2, 3],
        objectProp: { key: 'value' },
      };

      mockRealm.objectForPrimaryKey.mockReturnValue(complexEntity);

      const scenario = await buildScenarioForEntities([
        { entityName: 'Route_Summary__c', entityId: 'test' },
      ]);

      expect(scenario).toEqual({
        'Route_Summary__c.stringProp': 'test',
        'Route_Summary__c.numberProp': 123,
        'Route_Summary__c.booleanProp': true,
        'Route_Summary__c.nullProp': null,
        'Route_Summary__c.dateProp': testDate.toISOString(),
        'Route_Summary__c.arrayProp': [1, 2, 3],
        'Route_Summary__c.objectProp': { key: 'value' },
      });
    });
  });
});
