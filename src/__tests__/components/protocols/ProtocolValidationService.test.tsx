import { evaluateFieldValidations } from '~/services/protocol/ProtocolValidationService.ts';
import { Validation } from '~/types/component.types';

describe('evaluateFieldValidations', () => {
  it('should return no errors for an empty list of validations', () => {
    const value = '';
    const rules: Validation[] = [];
    const result = evaluateFieldValidations(value, rules);
    expect(result).toEqual([]);
  });

  describe('Required', () => {
    it('should return an error if value is empty', () => {
      const rules: Validation[] = [
        { type: 'Required', validationErrorMsg: 'This field is required' },
      ];
      const result = evaluateFieldValidations('', rules);
      expect(result).toEqual(['This field is required']);
    });

    it('should not return an error if value is non-empty', () => {
      const rules: Validation[] = [{ type: 'Required' }];
      const result = evaluateFieldValidations('hello', rules);
      expect(result).toHaveLength(0);
    });
  });

  describe('MinLength', () => {
    it('should return no errors if string length is >= minLength', () => {
      const rules: Validation[] = [
        { type: 'MinLength', value: 3, validationErrorMsg: 'At least 3 chars' },
      ];
      expect(evaluateFieldValidations('abc', rules)).toHaveLength(0);
      expect(evaluateFieldValidations('abcd', rules)).toHaveLength(0);
    });

    it('should return an error if string length is less than minLength', () => {
      const rules: Validation[] = [
        { type: 'MinLength', value: 5, validationErrorMsg: 'Need 5 chars' },
      ];
      const result = evaluateFieldValidations('hi', rules);
      expect(result).toEqual(['Need 5 chars']);
    });
  });

  describe('MaxLength', () => {
    it('should return no errors if string length is <= maxLength', () => {
      const rules: Validation[] = [
        { type: 'MaxLength', value: 5, validationErrorMsg: 'Too long' },
      ];
      expect(evaluateFieldValidations('hello', rules)).toHaveLength(0);
      expect(evaluateFieldValidations('hi', rules)).toHaveLength(0);
    });

    it('should return an error if string length is greater than maxLength', () => {
      const rules: Validation[] = [
        { type: 'MaxLength', value: 3, validationErrorMsg: 'Limit is 3' },
      ];
      const result = evaluateFieldValidations('abcd', rules);
      expect(result).toEqual(['Limit is 3']);
    });
  });

  describe('Regex', () => {
    it('should return no errors if the pattern matches', () => {
      const rules: Validation[] = [
        { type: 'Regex', value: '^[0-9]+$', validationErrorMsg: 'Digits only' },
      ];
      expect(evaluateFieldValidations('12345', rules)).toHaveLength(0);
    });

    it('should return an error if the pattern does not match', () => {
      const rules: Validation[] = [
        { type: 'Regex', value: '^[0-9]+$', validationErrorMsg: 'Digits only' },
      ];
      const result = evaluateFieldValidations('abc123', rules);
      expect(result).toEqual(['Digits only']);
    });
  });

  describe('PhoneNumber', () => {
    it('should return no errors for a numeric string', () => {
      const rules: Validation[] = [{ type: 'PhoneNumber' }];
      const result = evaluateFieldValidations('1234567890', rules);
      expect(result).toHaveLength(0);
    });

    it('should return an error for a non-numeric string', () => {
      const rules: Validation[] = [
        { type: 'PhoneNumber', validationErrorMsg: 'Phone must be numeric' },
      ];
      const result = evaluateFieldValidations('555-12AB', rules);
      expect(result).toEqual(['Phone must be numeric']);
    });
  });

  describe('Email', () => {
    it('should return no errors for a valid email', () => {
      const rules: Validation[] = [{ type: 'Email' }];
      const result = evaluateFieldValidations('<EMAIL>', rules);
      expect(result).toHaveLength(0);
    });

    it('should return an error for an invalid email', () => {
      const rules: Validation[] = [
        { type: 'Email', validationErrorMsg: 'Invalid email' },
      ];
      const result = evaluateFieldValidations('test@', rules);
      expect(result).toEqual(['Invalid email']);
    });
  });

  describe('SSN', () => {
    it('should return no errors for a properly formatted SSN', () => {
      const rules: Validation[] = [{ type: 'SSN' }];
      const result = evaluateFieldValidations('***********', rules);
      expect(result).toHaveLength(0);
    });

    it('should return an error for an improperly formatted SSN', () => {
      const rules: Validation[] = [
        { type: 'SSN', validationErrorMsg: 'Invalid SSN format' },
      ];
      const result = evaluateFieldValidations('123456789', rules);
      expect(result).toEqual(['Invalid SSN format']);
    });
  });

  describe('FileSizeMax', () => {
    it('should return no errors if file size is within limit', () => {
      const rules: Validation[] = [
        { type: 'FileSizeMax', value: 2, validationErrorMsg: 'Too large' },
      ];
      // e.g., a 1MB file
      const result = evaluateFieldValidations(1, rules);
      expect(result).toHaveLength(0);
    });

    it('should return an error if file size exceeds limit', () => {
      const rules: Validation[] = [
        { type: 'FileSizeMax', value: 1, validationErrorMsg: 'Too large' },
      ];
      // e.g., a 2MB file
      const result = evaluateFieldValidations(2, rules);
      expect(result).toEqual(['Too large']);
    });
  });

  describe('FileTypesAllowed', () => {
    it('should return no errors for a supported file extension', () => {
      const rules: Validation[] = [
        { type: 'FileTypesAllowed', value: ['jpg', 'png'] },
      ];
      const result = evaluateFieldValidations('photo.jpg', rules);
      expect(result).toHaveLength(0);
    });

    it('should return an error for an unsupported extension', () => {
      const rules: Validation[] = [
        {
          type: 'FileTypesAllowed',
          value: ['jpg', 'png'],
          validationErrorMsg: 'Only jpg or png allowed',
        },
      ];
      const result = evaluateFieldValidations('document.pdf', rules);
      expect(result).toEqual(['Only jpg or png allowed']);
    });
  });

  describe('Multiple validation rules', () => {
    it('should accumulate multiple errors if more than one rule fails', () => {
      const rules: Validation[] = [
        { type: 'Required', validationErrorMsg: 'Field required' },
        {
          type: 'Regex',
          value: '^[a-z]+$',
          validationErrorMsg: 'Only letters',
        },
      ];
      // Empty string will fail 'Required', and also fail 'Regex' for non-matching pattern
      const result = evaluateFieldValidations('', rules);
      expect(result).toEqual(['Field required', 'Only letters']);
    });
  });

  it('should log a warning (and skip) for an unsupported validation type', () => {
    // Since we can't easily intercept console output in default Jest, we check
    // that no errors are returned for unknown validation type
    const consoleSpy = jest.spyOn(console, 'warn').mockImplementation(() => {});
    const rules: Validation[] = [{ type: 'CustomValidation' as any }];
    const result = evaluateFieldValidations('some-value', rules);

    expect(consoleSpy).toHaveBeenCalledWith(
      'Unsupported validation type: CustomValidation',
    );
    expect(result).toEqual([]);

    consoleSpy.mockRestore();
  });
});
