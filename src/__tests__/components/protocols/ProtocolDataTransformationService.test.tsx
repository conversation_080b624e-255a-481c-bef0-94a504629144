// ProtocolDataTransformationService.test.ts
import { runBeforeTransformations } from '~/services/protocol/ProtocolDataTransformationService';
import { BeforeActionDefinition } from '~/types/protocolActions.types';

describe('ProtocolDataTransformationService', () => {
  it('should combine didKnockOnDoor and askWaitForSamples into stop.SNO_Tags__c using a semicolon', () => {
    // This mimics the relevant "before" instructions from saveSnoStopData in the SNOProtocol
    const before: BeforeActionDefinition[] = [
      {
        type: 'transform',
        operation: 'combine',
        sourceFields: ['didKnockOnDoor', 'askWaitForSamples'],
        destinationField: 'stop.SNO_Tags__c',
        delimiter: ';',
      },
    ];

    // This mimics user-provided state
    const currentState = {
      didKnockOnDoor: 'Yes',
      askWaitForSamples: 'No',
      stop: {
        SNO_Tags__c: null,
      },
    };

    // Run the transformations
    const resultState = runBeforeTransformations(before, currentState);

    // Verify the combined output
    expect(resultState.stop.SNO_Tags__c).toBe('Yes;No');

    // Also verify that other state properties remain unchanged
    expect(resultState.didKnockOnDoor).toBe('Yes');
    expect(resultState.askWaitForSamples).toBe('No');
  });

  it('should skip transformation if no valid instructions are provided', () => {
    const beforeUndefined: BeforeActionDefinition[] | undefined = undefined;
    const currentState = { someField: 'someValue' };

    // Run transformation without valid instructions
    const resultState = runBeforeTransformations(beforeUndefined, currentState);

    // Expect the state to remain exactly as it was
    expect(resultState).toEqual(currentState);
  });

  it('should handle missing or null fields gracefully', () => {
    const before: BeforeActionDefinition[] = [
      {
        type: 'transform',
        operation: 'combine',
        sourceFields: ['didKnockOnDoor', 'askWaitForSamples'],
        destinationField: 'stop.SNO_Tags__c',
        delimiter: ';',
      },
    ];

    // Here, didKnockOnDoor is undefined
    const currentState = {
      askWaitForSamples: 'Yes',
      stop: { SNO_Tags__c: '' },
    };

    const resultState = runBeforeTransformations(before, currentState);
    // "didKnockOnDoor" doesn't exist, so only "askWaitForSamples" is used
    expect(resultState.stop.SNO_Tags__c).toBe('Yes');
  });

  // ----------------- New Tests for Condition-Based Array -----------------

  it('should conditionally append values when sourceFields is an array of condition objects', () => {
    // "before" instructions referencing condition-based sourceFields
    const before: BeforeActionDefinition[] = [
      {
        type: 'transform',
        operation: 'combine',
        // Each object has a condition and a value
        sourceFields: [
          {
            condition: {
              all: [
                {
                  field: 'user.role',
                  operator: '===',
                  value: 'admin',
                },
              ],
            },
            value: 'ADMIN_TAG',
          },
          {
            condition: {
              all: [
                {
                  field: 'user.age',
                  operator: '>=',
                  value: 18,
                },
              ],
            },
            value: 'ADULT',
          },
        ],
        destinationField: 'tags',
        delimiter: ';',
      },
    ];

    const currentState = {
      'user.role': 'admin',
      'user.age': 25,
      tags: '',
    };

    const resultState = runBeforeTransformations(before, currentState);
    // Both conditions should pass => "ADMIN_TAG;ADULT"
    expect(resultState.tags).toBe('ADMIN_TAG;ADULT');
  });

  it('should skip condition objects that fail the condition check', () => {
    const before: BeforeActionDefinition[] = [
      {
        type: 'transform',
        operation: 'combine',
        sourceFields: [
          {
            condition: {
              all: [
                {
                  field: 'user.role',
                  operator: '===',
                  value: 'admin',
                },
              ],
            },
            value: 'ADMIN_TAG',
          },
          {
            condition: {
              all: [
                {
                  field: 'user.age',
                  operator: '>=',
                  value: 18,
                },
              ],
            },
            value: 'ADULT',
          },
        ],
        destinationField: 'tags',
        delimiter: ';',
      },
    ];

    // In this scenario, the role does NOT match 'admin', so only the second object should pass
    const currentState = {
      'user.role': 'guest',
      'user.age': 25,
      tags: '',
    };

    const resultState = runBeforeTransformations(before, currentState);
    // The user is not admin, so first condition fails. Age >= 18 => second passes => "ADULT"
    expect(resultState.tags).toBe('ADULT');
  });

  it('should produce an empty string if none of the conditions pass', () => {
    const before: BeforeActionDefinition[] = [
      {
        type: 'transform',
        operation: 'combine',
        sourceFields: [
          {
            condition: {
              all: [
                {
                  field: 'user.role',
                  operator: '===',
                  value: 'admin',
                },
              ],
            },
            value: 'ADMIN_TAG',
          },
          {
            condition: {
              all: [
                {
                  field: 'user.age',
                  operator: '>=',
                  value: 18,
                },
              ],
            },
            value: 'ADULT',
          },
        ],
        destinationField: 'tags',
        delimiter: ';',
      },
    ];

    // Both conditions fail => user.role not 'admin', age is 17 => <18
    const currentState = {
      'user.role': 'guest',
      'user.age': 17,
      tags: null,
    };

    const resultState = runBeforeTransformations(before, currentState);
    // No condition matched => tags is set to ""
    expect(resultState.tags).toBe('');
  });

  it('should handle multiple condition objects that pass, appending all matching values', () => {
    const before: BeforeActionDefinition[] = [
      {
        type: 'transform',
        operation: 'combine',
        sourceFields: [
          {
            condition: {
              all: [
                {
                  field: 'user.hasCar',
                  operator: '===',
                  value: true,
                },
              ],
            },
            value: 'CAR_OWNER',
          },
          {
            condition: {
              all: [
                {
                  field: 'user.hasBike',
                  operator: '===',
                  value: true,
                },
              ],
            },
            value: 'BIKE_OWNER',
          },
          {
            condition: {
              all: [
                {
                  field: 'user.hasBoat',
                  operator: '===',
                  value: true,
                },
              ],
            },
            value: 'BOAT_OWNER',
          },
        ],
        destinationField: 'ownershipTags',
        delimiter: ';',
      },
    ];

    const currentState = {
      'user.hasCar': true,
      'user.hasBike': false,
      'user.hasBoat': true,
    };

    const resultState = runBeforeTransformations(before, currentState);
    // Car => pass => CAR_OWNER
    // Bike => false => skip
    // Boat => pass => BOAT_OWNER
    expect(resultState.ownershipTags).toBe('CAR_OWNER;BOAT_OWNER');
  });
});
