import { doesConditionHold } from '~/services/protocol/ProtocolConditionEvaluatorService.ts';
import {
  Condition,
  SimpleCondition as BasicCondition,
} from '~/types/condition.types';
import { customOperators } from '~/types/customOperators.types';

jest.mock('~/types/customOperators.types', () => ({
  customOperators: {
    isAdult: jest.fn(), // example custom operator
  },
}));

describe('doesConditionHold', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  test('should return true when all sub-conditions pass', () => {
    const condition: Condition = {
      all: [
        { field: 'age', operator: '>=', value: 18 },
        { field: 'country', operator: '===', value: 'USA' },
      ],
    };

    const scenario = { age: 20, country: 'USA' };
    expect(doesConditionHold(condition, scenario)).toBe(true);
  });

  test('should return false when any sub-condition in "all" fails', () => {
    const condition: Condition = {
      all: [
        { field: 'age', operator: '>=', value: 18 },
        { field: 'country', operator: '===', value: 'USA' },
      ],
    };

    const scenario = { age: 20, country: 'Canada' };
    expect(doesConditionHold(condition, scenario)).toBe(false);
  });

  test('should return true when at least one sub-condition in "any" passes', () => {
    const condition: Condition = {
      any: [
        { field: 'age', operator: '<', value: 18 },
        { field: 'country', operator: '===', value: 'USA' },
      ],
    };

    const scenario = { age: 20, country: 'USA' };
    expect(doesConditionHold(condition, scenario)).toBe(true);
  });

  test('should return false if all sub-conditions in "any" fail', () => {
    const condition: Condition = {
      any: [
        { field: 'age', operator: '>', value: 25 },
        { field: 'country', operator: '===', value: 'Brazil' },
      ],
    };

    const scenario = { age: 20, country: 'USA' };
    expect(doesConditionHold(condition, scenario)).toBe(false);
  });

  test('should handle == operator in a basic condition', () => {
    const basicCondition: BasicCondition = {
      field: 'score',
      operator: '==',
      value: 100,
    };
    const scenario = { score: '100' }; // == does type coerce
    expect(doesConditionHold(basicCondition, scenario)).toBe(true);
  });

  test('should handle === operator in a basic condition (strict equality)', () => {
    const basicCondition: BasicCondition = {
      field: 'score',
      operator: '===',
      value: 100,
    };
    const scenario = { score: '100' };
    expect(doesConditionHold(basicCondition, scenario)).toBe(false);
  });

  test('should handle < operator in a basic condition', () => {
    const basicCondition: BasicCondition = {
      field: 'temperature',
      operator: '<',
      value: 37,
    };
    const scenario = { temperature: 36.5 };
    expect(doesConditionHold(basicCondition, scenario)).toBe(true);
  });

  test('should return false for an unrecognized operator', () => {
    // Spy on console.warn to verify it’s called for an unrecognized operator
    const consoleWarnSpy = jest
      .spyOn(console, 'warn')
      .mockImplementation(() => {});

    const basicCondition: BasicCondition = {
      field: 'unknown',
      // @ts-ignore we are testing the ability for the algorithm to detect an invalid operators, it is expected that this be invalid.
      operator: '??',
      value: 42,
    };
    const scenario = { unknown: 42 };
    expect(doesConditionHold(basicCondition, scenario)).toBe(false);
    expect(consoleWarnSpy).toHaveBeenCalledWith(
      expect.stringContaining('Unrecognized standard operator'),
    );
    consoleWarnSpy.mockRestore();
  });

  describe('custom operator', () => {
    test('should return false and warn if custom operator is missing', () => {
      const consoleWarnSpy = jest
        .spyOn(console, 'warn')
        .mockImplementation(() => {});
      const basicCondition: BasicCondition = {
        field: 'age',
        operator: 'custom',
        // intentionally no customOperator
      } as BasicCondition;
      const scenario = { age: 30 };

      expect(doesConditionHold(basicCondition, scenario)).toBe(false);
      expect(consoleWarnSpy).toHaveBeenCalledWith(
        expect.stringContaining('No custom operator specified'),
      );
      consoleWarnSpy.mockRestore();
    });

    test('should return false if custom operator is unrecognized', () => {
      const consoleWarnSpy = jest
        .spyOn(console, 'warn')
        .mockImplementation(() => {});
      const basicCondition: BasicCondition = {
        field: 'age',
        operator: 'custom',
        customOperator: 'nonExistentOperator',
      };
      const scenario = { age: 30 };

      expect(doesConditionHold(basicCondition, scenario)).toBe(false);
      expect(consoleWarnSpy).toHaveBeenCalledWith(
        expect.stringContaining('Unrecognized custom operator'),
      );
      consoleWarnSpy.mockRestore();
    });

    test('should call the recognized custom operator and return its result', () => {
      const isAdultMock = customOperators.isAdult as jest.MockedFunction<
        (...args: any[]) => boolean
      >;
      isAdultMock.mockReturnValueOnce(true);

      const basicCondition: BasicCondition = {
        field: 'age',
        operator: 'custom',
        customOperator: 'isAdult',
      };
      const scenario = { age: 20 }; // Provide the scenario data
      const context = scenario; // Pass the context directly as the scenario data

      // Expect the condition to hold true as the mock returns true
      expect(doesConditionHold(basicCondition, context)).toBe(true);

      // Check that the custom operator mock was called correctly
      expect(isAdultMock).toHaveBeenCalledWith(
        basicCondition,
        scenario.age,
        context,
      );
    });
  });
});
