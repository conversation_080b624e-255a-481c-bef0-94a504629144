import { handleSaveProtocolAction } from '~/services/protocol/ProtocolDataSyncService';
import { updateEntity } from '~/db/realm/operations';
import { addToRequestQueue } from '~/services/sync/syncUp';
import { ActionHandlerResult } from '~/services/protocol/ProtocolActionService';

// Mock both dependencies
jest.mock('~/db/realm/operations', () => ({
  updateEntity: jest.fn(),
}));

jest.mock('~/services/sync/syncUp', () => ({
  addToRequestQueue: jest.fn(),
}));

// Mock react-native-background-geolocation
jest.mock('react-native-background-geolocation', () => ({
  __esModule: true,
  default: {
    getCurrentPosition: jest.fn(),
    configure: jest.fn(),
    start: jest.fn(),
    stop: jest.fn(),
    onLocation: jest.fn(),
    onMotionChange: jest.fn(),
    onActivityChange: jest.fn(),
    onProviderChange: jest.fn(),
    onHeartbeat: jest.fn(),
    onHttp: jest.fn(),
    onError: jest.fn(),
    onGeofence: jest.fn(),
    onGeofencesChange: jest.fn(),
    onSchedule: jest.fn(),
    onPowerSaveChange: jest.fn(),
    onConnectivityChange: jest.fn(),
    onAuthorization: jest.fn(),
    onEnabledChange: jest.fn(),
  },
}));

// Mock react-native-background-fetch
jest.mock('react-native-background-fetch', () => ({
  __esModule: true,
  default: {
    configure: jest.fn(),
    start: jest.fn(),
    stop: jest.fn(),
    status: jest.fn(),
    finish: jest.fn(),
  },
}));

// Mock react-native-geolocation-service
jest.mock('react-native-geolocation-service', () => ({
  getCurrentPosition: jest.fn(),
  requestAuthorization: jest.fn(),
}));

// Mock react-native-device-info
jest.mock('react-native-device-info', () => ({
  getUniqueId: jest.fn(),
  getSystemVersion: jest.fn(),
  getVersion: jest.fn(),
  getBuildNumber: jest.fn(),
  getBundleId: jest.fn(),
  getModel: jest.fn(),
  getBrand: jest.fn(),
  getDeviceId: jest.fn(),
  getSystemName: jest.fn(),
  getUserAgent: jest.fn(),
  getDeviceName: jest.fn(),
  getDeviceType: jest.fn(),
  isTablet: jest.fn(),
  isEmulator: jest.fn(),
  isLocationEnabled: jest.fn(),
  isCameraAvailable: jest.fn(),
  isBiometricHardwareSupported: jest.fn(),
  isFingerprintAvailable: jest.fn(),
  isFaceIdAvailable: jest.fn(),
  isPinOrFingerprintSet: jest.fn(),
  isAirplaneMode: jest.fn(),
  isBatteryCharging: jest.fn(),
  getBatteryLevel: jest.fn(),
  getPowerState: jest.fn(),
  getAvailableLocationProviders: jest.fn(),
  getCarrier: jest.fn(),
  getTotalMemory: jest.fn(),
  getUsedMemory: jest.fn(),
  getFreeDiskStorage: jest.fn(),
  getTotalDiskCapacity: jest.fn(),
  getFreeDiskStorageOld: jest.fn(),
  getTotalDiskCapacityOld: jest.fn(),
  getApiLevel: jest.fn(),
  getIpAddress: jest.fn(),
  getMacAddress: jest.fn(),
  getPhoneNumber: jest.fn(),
  getFirstInstallTime: jest.fn(),
  getLastUpdateTime: jest.fn(),
  getInstallReferrer: jest.fn(),
  getCameraPresets: jest.fn(),
  getAvailableSensors: jest.fn(),
  isSensorAvailable: jest.fn(),
  getDeviceCountry: jest.fn(),
  getDeviceLocale: jest.fn(),
  getDeviceLocaleCountryCode: jest.fn(),
  getTimezone: jest.fn(),
  isAutoDateAndTime: jest.fn(),
  isAutoTimeZone: jest.fn(),
  getSupportedAbis: jest.fn(),
  getFontScale: jest.fn(),
  isKeyboardConnected: jest.fn(),
  getBootloader: jest.fn(),
  getDevice: jest.fn(),
  getDisplay: jest.fn(),
  getFingerprint: jest.fn(),
  getHardware: jest.fn(),
  getHost: jest.fn(),
  getProduct: jest.fn(),
  getTags: jest.fn(),
  getType: jest.fn(),
  getBaseOs: jest.fn(),
  getPreviewSdkInt: jest.fn(),
  getSecurityPatch: jest.fn(),
  getCodename: jest.fn(),
  getIncremental: jest.fn(),
}));

// Mock services that use the above modules
jest.mock('~/services/location/LocationService', () => ({
  submitCheckInLocation: jest.fn().mockResolvedValue(true),
  initLocationService: jest.fn(),
  setRouteAndStopId: jest.fn(),
  startHeartbeat: jest.fn(),
  stopHeartbeat: jest.fn(),
  postLocation: jest.fn(),
  getCurrentLocationFromGeolocation: jest.fn(),
  isBackgroundLocationEnabled: jest.fn(),
  cleanupLocationService: jest.fn(),
}));

jest.mock('~/services/ImageStoreService', () => ({
  saveImage: jest.fn(),
  getImage: jest.fn(),
  deleteImage: jest.fn(),
}));

const mockedUpdateEntity = updateEntity as jest.MockedFunction<
  typeof updateEntity
>;
const mockedAddToRequestQueue = addToRequestQueue as jest.MockedFunction<
  typeof addToRequestQueue
>;

describe('handleSaveProtocolAction', () => {
  const consoleErrorSpy = jest
    .spyOn(console, 'error')
    .mockImplementation(() => {});
  const consoleWarnSpy = jest
    .spyOn(console, 'warn')
    .mockImplementation(() => {});

  beforeEach(() => {
    jest.clearAllMocks();
    // Set default successful responses
    mockedUpdateEntity.mockResolvedValue(true);
    mockedAddToRequestQueue.mockResolvedValue(true);
  });

  afterAll(() => {
    consoleErrorSpy.mockRestore();
    consoleWarnSpy.mockRestore();
  });

  const baseAction: Extract<ActionHandlerResult, { status: 'save' }> = {
    status: 'save',
    objects: [
      {
        name: 'Stop__c',
        action: 'update',
        fields: ['Is_Lockbox_Out__c', 'SNO_Tags__c'], // Remove Stop__c. prefix from fields
      },
    ],
    transformedState: {
      'Stop__c.Is_Lockbox_Out__c': 'Yes',
      Stop__c: {
        SNO_Tags__c: 'ABC',
      },
    },
  };

  const scenario = {
    'Stop__c.Id': '1234',
  };

  it('should call updateEntity with correct payload', async () => {
    const result = await handleSaveProtocolAction(baseAction, scenario);

    expect(result).toBe(true);
    expect(mockedUpdateEntity).toHaveBeenCalledWith({
      entityId: '1234',
      entityName: 'Stop__c',
      updates: {
        Is_Lockbox_Out__c: 'Yes',
        SNO_Tags__c: 'ABC',
      },
    });
    expect(mockedAddToRequestQueue).toHaveBeenCalledWith({
      entityId: '1234',
      entityName: 'Stop__c',
      updates: {
        Is_Lockbox_Out__c: 'Yes',
        SNO_Tags__c: 'ABC',
      },
    });
  });

  it('should skip update if no entityId found', async () => {
    const invalidScenario = {};
    const result = await handleSaveProtocolAction(baseAction, invalidScenario);

    expect(result).toBe(false);
    expect(mockedUpdateEntity).not.toHaveBeenCalled();
    expect(mockedAddToRequestQueue).not.toHaveBeenCalled();
    expect(consoleErrorSpy).toHaveBeenCalledWith(
      '[handleSaveProtocolAction] No ID found for entity: Stop__c',
    );
  });

  it('should skip unsupported action types', async () => {
    const action = {
      ...baseAction,
      objects: [
        {
          name: 'Stop__c',
          action: 'create',
          fields: ['Is_Lockbox_Out__c'],
        },
      ],
    } as typeof baseAction;

    const result = await handleSaveProtocolAction(action, scenario);

    expect(result).toBe(false); // Should return false for unsupported entity creation
    expect(mockedUpdateEntity).not.toHaveBeenCalled();
    expect(mockedAddToRequestQueue).not.toHaveBeenCalled();
    expect(consoleWarnSpy).toHaveBeenCalledWith(
      '[createVerificationObject] Unsupported entity for creation: Stop__c. Supported entities: Protocol_Verification__c',
    );
  });

  it('should skip update if updates object is empty', async () => {
    const action = {
      ...baseAction,
      transformedState: {}, // no updates
    };

    const result = await handleSaveProtocolAction(action, scenario);

    expect(result).toBe(true); // Should still return true for soft failures
    expect(mockedUpdateEntity).not.toHaveBeenCalled();
    expect(mockedAddToRequestQueue).not.toHaveBeenCalled();
    expect(consoleWarnSpy).toHaveBeenCalledWith(
      '[handleSaveProtocolAction] No updates for entity: Stop__c',
    );
  });

  it('should log error if updateEntity fails', async () => {
    mockedUpdateEntity.mockResolvedValue(false);

    const result = await handleSaveProtocolAction(baseAction, scenario);

    expect(result).toBe(false);
    expect(mockedUpdateEntity).toHaveBeenCalled();
    expect(consoleErrorSpy).toHaveBeenCalledWith(
      '[handleSaveProtocolAction] Failed to update entity: Stop__c (1234)',
    );
  });

  it('should log error if addToRequestQueue fails', async () => {
    mockedAddToRequestQueue.mockResolvedValue(false);

    const result = await handleSaveProtocolAction(baseAction, scenario);

    expect(result).toBe(false);
    expect(mockedUpdateEntity).toHaveBeenCalled();
    expect(mockedAddToRequestQueue).toHaveBeenCalled();
    expect(consoleErrorSpy).toHaveBeenCalledWith(
      '[processEntityUpdate] Failed to queue entity: Stop__c (1234)',
    );
  });
});
