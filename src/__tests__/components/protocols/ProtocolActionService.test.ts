/**
 * @file ProtocolActionService.test.ts
 *
 * Example Jest tests for ProtocolActionService, using the SNOProtocol.json data.
 * Run these tests with: `npm test` or `yarn test`
 */

import {
  processStepAction,
  processGlobalAction,
  type ActionHandlerResult,
} from '~/services/protocol/ProtocolActionService';

import SNOProtocol from '~/services/protocol/configs/SNOProtocol.json';

describe('ProtocolActionService', () => {
  const { steps, actions: globalActions } = SNOProtocol;

  function getStep(stepId: string) {
    return steps.find(s => s.stepId === stepId);
  }

  const initialStep = getStep('snoInitialQuestionsStep');
  const continueAction = initialStep?.actions?.find(
    a => (a.label as any)?.enabled === 'Continue',
  );

  describe('Step Navigation', () => {
    it('navigates to lockboxPhotosStep when lockbox is out and business is closed', () => {
      const currentState = {
        'Stop__c.Is_Lockbox_Out__c': true,
        businessStatus: false,
      };

      if (!continueAction) {
        throw new Error('Continue action not found in SNOProtocol');
      }

      const result: ActionHandlerResult = processStepAction(
        continueAction as any,
        globalActions as any,
        currentState,
      );

      expect(result).toEqual({
        status: 'navigate',
        nextStepId: 'lockboxPhotosStep',
      });
    });

    it('navigates to perimeterCheckStep when lockbox is not out and business is closed', () => {
      const currentState = {
        'Stop__c.Is_Lockbox_Out__c': false,
        businessStatus: false,
      };

      if (!continueAction) {
        throw new Error('Continue action not found in SNOProtocol');
      }

      const result: ActionHandlerResult = processStepAction(
        continueAction as any,
        globalActions as any,
        currentState,
      );

      expect(result).toEqual({
        status: 'navigate',
        nextStepId: 'perimeterCheckStep',
      });
    });

    it('navigates to businessOpenQuestions when lockbox is not out and business is open', () => {
      const currentState = {
        'Stop__c.Is_Lockbox_Out__c': false,
        businessStatus: true,
      };

      if (!continueAction) {
        throw new Error('Continue action not found in SNOProtocol');
      }

      const result: ActionHandlerResult = processStepAction(
        continueAction as any,
        globalActions as any,
        currentState,
      );

      expect(result).toEqual({
        status: 'navigate',
        nextStepId: 'businessOpenQuestions',
      });
    });

    it('returns null nextStepId when lockbox is out and business is open', () => {
      const currentState = {
        'Stop__c.Is_Lockbox_Out__c': true,
        businessStatus: true,
      };

      if (!continueAction) {
        throw new Error('Continue action not found in SNOProtocol');
      }

      const result: ActionHandlerResult = processStepAction(
        continueAction as any,
        globalActions as any,
        currentState,
      );

      expect(result).toEqual({
        status: 'navigate',
        nextStepId: 'businessOpenQuestions',
      });
    });
  });

  describe('Business Open Questions Flow', () => {
    const businessOpenStep = getStep('businessOpenQuestions');
    const nextAction = businessOpenStep?.actions?.find(
      a => a.label === 'Continue',
    );

    it('navigates to waitForSampleTimerStep when knocked and waiting for samples', () => {
      const currentState = {
        didKnockOnDoor: true,
        askWaitForSamples: true,
      };

      if (!nextAction) {
        throw new Error('Next action not found in businessOpenQuestions step');
      }

      const result: ActionHandlerResult = processStepAction(
        nextAction as any,
        globalActions as any,
        currentState,
      );

      expect(result).toEqual({
        status: 'navigate',
        nextStepId: 'waitForSampleTimerStep',
      });
    });

    it('navigates to confirmSampleNotReceivedStep when knocked but not waiting', () => {
      const currentState = {
        didKnockOnDoor: true,
        askWaitForSamples: false,
      };

      if (!nextAction) {
        throw new Error('Next action not found in businessOpenQuestions step');
      }

      const result: ActionHandlerResult = processStepAction(
        nextAction as any,
        globalActions as any,
        currentState,
      );

      expect(result).toEqual({
        status: 'navigate',
        nextStepId: 'confirmSampleNotReceivedStep',
      });
    });
  });

  describe('Save Actions', () => {
    it('processes saveSnoStopData action with transformations', () => {
      const stepAction = {
        type: 'invokeAction',
        actionId: 'saveSnoStopData',
      };

      const currentState = {
        snoWaitingTimeStatus: 'Inactive',
        imagesToUpload: {},
        'Stop__c.Is_Lockbox_Out__c': true,
        businessStatus: true,
        didKnockOnDoor: true,
        askWaitForSamples: true,
        'Stop__c.Proof_of_Service__c': 'Mr. John',
        timerDuration: 0,
        'Stop__c.Driver_Initiated_Early_Departure__c': true,
        'Stop__c.SNO_Bypass_DateTime__c': '2025-04-23T12:58:08.199Z',
        snoInactiveTimeStatus: 'Inactive',
        'Stop__c.Completed_Time__c': '2025-04-23T12:58:11.460Z',
      };

      const result = processStepAction(
        stepAction as any,
        globalActions as any,
        currentState,
      );

      expect(result).toEqual({
        status: 'save',
        after: {
          actionId: 'confirmation',
          options: {
            type: 'stop',
            subType: 'stop_completed',
          },
        },
        objects: [
          {
            name: 'Stop__c',
            fields: [
              'SNO_Tags__c',
              'Proof_of_Service__c',
              'Completed_Time__c',
              'SNO_Driver_Waiting__c',
              'Driver_Initiated_Early_Departure__c',
              'SNO_Bypass_DateTime__c',
            ],
            action: 'update',
          },
        ],
        transformedState: {
          snoWaitingTimeStatus: 'Inactive',
          imagesToUpload: {},
          'Stop__c.Is_Lockbox_Out__c': true,
          businessStatus: true,
          didKnockOnDoor: true,
          askWaitForSamples: true,
          timerDuration: 0,
          'Stop__c.Proof_of_Service__c': 'Mr. John',
          'Stop__c.Driver_Initiated_Early_Departure__c': true,
          'Stop__c.SNO_Bypass_DateTime__c': '2025-04-23T12:58:08.199Z',
          'Stop__c.Completed_Time__c': '2025-04-23T12:58:11.460Z',
          snoInactiveTimeStatus: 'Inactive',
          Stop__c: {
            SNO_Tags__c: 'KOD;TT',
          },
        },
      });
    });
  });

  describe('Error Handling', () => {
    it('returns error for unknown step action type', () => {
      const invalidAction = {
        type: 'invalidType',
        label: 'Invalid Action',
      };

      const result = processStepAction(
        invalidAction as any,
        globalActions as any,
        {},
      );

      expect(result).toEqual({
        status: 'error',
        message: 'Unknown step action type: invalidType',
      });
    });

    it('returns error for unknown global action type', () => {
      const invalidGlobalAction = {
        type: 'invalidType',
      };

      const result = processGlobalAction({}, invalidGlobalAction);

      expect(result).toEqual({
        status: 'error',
        message: 'Unrecognized dictionary action type: invalidType',
      });
    });

    it('returns error for invokeAction without actionId', () => {
      const invalidStepAction = {
        type: 'invokeAction',
      };

      // @ts-expect-error
      const result = processStepAction(invalidStepAction, globalActions, {});

      expect(result).toEqual({
        status: 'error',
        message: 'No actionId provided for invokeAction.',
      });
    });

    it('returns error for non-existent actionId', () => {
      const invalidStepAction = {
        type: 'invokeAction',
        actionId: 'nonExistentAction',
      };

      const result = processStepAction(invalidStepAction, globalActions, {});

      expect(result).toEqual({
        status: 'error',
        message:
          'Action ID "nonExistentAction" not found in protocol dictionary.',
      });
    });
  });

  describe('Close Actions', () => {
    it('handles close action from step', () => {
      const closeAction = {
        type: 'close',
        label: 'Close',
      };

      // @ts-expect-error
      const result = processStepAction(closeAction, globalActions, {});

      expect(result).toEqual({
        status: 'close',
      });
    });

    it('handles close action from global action', () => {
      const globalCloseAction = {
        type: 'close',
      };

      const result = processGlobalAction({}, globalCloseAction);

      expect(result).toEqual({
        status: 'close',
      });
    });
  });
});
