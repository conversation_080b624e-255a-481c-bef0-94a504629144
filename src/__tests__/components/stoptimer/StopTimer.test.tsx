import React from 'react';
import { render, act, waitFor } from '@testing-library/react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import StopTimer from '~/components/stoptimer/StopTimer';

jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
}));

jest.useFakeTimers();

describe('StopTimer Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders initial time correctly', async () => {
    AsyncStorage.getItem.mockResolvedValue(null);
    const { getByText } = render(
      <StopTimer durationInMinutes={5} id="StopTimer" />,
    );
    await waitFor(() => expect(getByText('05:00')).toBeTruthy());
  });

  it('decrements time correctly after 1 second', async () => {
    AsyncStorage.getItem.mockResolvedValue(null);
    const { getByText } = render(
      <StopTimer durationInMinutes={1} id="StopTimer" />,
    );
    await waitFor(() => expect(getByText('01:00')).toBeTruthy());
    act(() => {
      jest.advanceTimersByTime(1000);
    });
    await waitFor(() => expect(getByText('00:59')).toBeTruthy());
  });

  it('stops at 00:00 and does not go negative', async () => {
    AsyncStorage.getItem.mockResolvedValue(null);
    const { getByText } = render(
      <StopTimer durationInMinutes={0} id="StopTimer" />,
    );
    await waitFor(() => expect(getByText('00:00')).toBeTruthy());
    act(() => {
      jest.advanceTimersByTime(5000);
    });
    await waitFor(() => expect(getByText('00:00')).toBeTruthy());
  });

  it('renders remaining text correctly', async () => {
    const { getByTestId } = render(
      <StopTimer durationInMinutes={5} id="StopTimer" />,
    );
    await waitFor(() =>
      expect(getByTestId('StopTimer.Text.remainingText')).toBeTruthy(),
    );
  });

  it('renders circular progress correctly', async () => {
    const { getByTestId } = render(
      <StopTimer durationInMinutes={5} id="StopTimer" />,
    );
    await waitFor(() =>
      expect(getByTestId('StopTimer.View.circle')).toBeTruthy(),
    );
  });

  it('calls onComplete when timer reaches zero', async () => {
    const onComplete = jest.fn();
    AsyncStorage.getItem.mockResolvedValue(null);
    const { getByText } = render(
      <StopTimer
        durationInMinutes={0}
        id="StopTimer"
        onComplete={onComplete}
      />,
    );
    await waitFor(() => expect(getByText('00:00')).toBeTruthy());
    expect(onComplete).toHaveBeenCalled();
  });

  it('resumes from stored start time in AsyncStorage', async () => {
    const now = Date.now();
    const start = now - 30 * 1000; // 30 seconds ago
    AsyncStorage.getItem.mockImplementation(key => {
      if (key === 'timerStart_StopTimer')
        return Promise.resolve(start.toString());
      return Promise.resolve(null);
    });
    const { getByText } = render(
      <StopTimer durationInMinutes={1} id="StopTimer" />,
    );
    await waitFor(() => expect(getByText('00:30')).toBeTruthy());
  });

  it('sets timer as completed in AsyncStorage when finished', async () => {
    AsyncStorage.getItem.mockResolvedValue(null);
    const setItemSpy = jest.spyOn(AsyncStorage, 'setItem');
    render(<StopTimer durationInMinutes={0} id="StopTimer" />);
    await waitFor(() =>
      expect(setItemSpy).toHaveBeenCalledWith(
        'timerCompleted_StopTimer',
        'true',
      ),
    );
  });

  it('renders with custom strokeColor', async () => {
    AsyncStorage.getItem.mockResolvedValue(null);
    const { getByTestId } = render(
      <StopTimer durationInMinutes={5} id="StopTimer" strokeColor="#FF0000" />,
    );
    await waitFor(() =>
      expect(getByTestId('StopTimer.View.circle')).toBeTruthy(),
    );
  });

  it('defaults to 10 minutes if durationInMinutes is not provided', async () => {
    AsyncStorage.getItem.mockResolvedValue(null);
    const { getByText } = render(<StopTimer id="StopTimer" />);
    await waitFor(() => expect(getByText('10:00')).toBeTruthy());
  });

  it('cleans up properly on unmount', async () => {
    AsyncStorage.getItem.mockResolvedValue(null);
    const { unmount, getByText } = render(
      <StopTimer durationInMinutes={1} id="StopTimer" />,
    );
    await waitFor(() => expect(getByText('01:00')).toBeTruthy());
    unmount();
    // No errors should occur
  });
});
