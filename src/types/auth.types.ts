export type AuthResponse = {
  access_token: string;
  refresh_token?: string;
  instance_url: string;
  sfdc_community_url: string;
  sfdc_community_id: string;
  id: string;
  token_type: 'Bearer';
  issued_at: string;
  signature: string;
};

export interface OAuthUser {
  userId: string;
  loginUrl: string;
  communityUrl: string;
  communityId: string;
  clientId: string;
  refreshToken: string;
  orgId: string;
  accessToken: string;
  userAgent: string;
  instanceUrl: string;
}
