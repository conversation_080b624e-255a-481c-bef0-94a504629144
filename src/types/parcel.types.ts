import { BaseResponse, Type } from '~/types/shared.types';

export type Parcel = {
  Id: string;
  Pickup__c: string;
  Delivery__c: string;
  Requires_Signature__c: boolean | null;
  Reference_Required__c: boolean | null;
  Parcel_Type_Definition__c: string | null;
  Parcel_Type_Name__c: string | null;
  Quantity__c: number | null;
  Signature__c?: string;
  Signature_Comments__c?: string;
  Signature_Statuses__c?: string;
  Quantity_Expected__c?: number;
  Dropoff_Quantity__c?: number;
  Comments__c?: string;
  Reference__c?: string;
  Work_Order__c?: string;
  Work_Order__r?: object;
  Pickup_Scan_Time__c?: string;
  Delivery_Scan_Time__c?: string;
  Barcode_Required__c: boolean;
  Barcode_Bypass_Reason__c?: string;
};

export type ParcelResponse = BaseResponse & {
  records: Parcel[] | null;
};

export type ParcelType = Type;

export type ParcelTypeResponse = BaseResponse & {
  records: ParcelType[] | null;
};
