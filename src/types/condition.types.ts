/**
 * Standard comparison operators you might handle directly in code (==, ===, !=, !==, >, <, >=, <=, etc.)
 */
export type StandardOperator =
  | '=='
  | '==='
  | '!='
  | '!=='
  | '>'
  | '>='
  | '<'
  | '<=';

/**
 * We extend the operator union to include "custom" so that we can have conditions like:
 *   { field: 'location', operator: 'custom', customOperator: 'isWithinRadius', ... }
 */
export type Operator = StandardOperator | 'custom';

/**
 * A simple condition describes a basic comparison of a field's value
 * (e.g. `stop.Status__c === 'COMPLETED'`) OR a custom operator (e.g. `isWithinRadius`).
 */
export interface SimpleCondition {
  field: string | null;
  operator: Operator;
  /**
   * The value we compare to (could be boolean, number, string, etc.).
   * Optional because custom operators might rely on specialized fields instead.
   */
  value?: any;
  /**
   * If operator === 'custom', we can put extra props like `customOperator` and
   * any parameters that the custom handler will need (e.g. center lat/long, radius).
   */
  customOperator?: string;
  [key: string]: any; // allow additional fields for custom logic
}

/**
 * Represents a logical AND group: all conditions must evaluate to true.
 * e.g. { all: [cond1, cond2, ...] }
 */
export interface AllCondition {
  all: Condition[];
}

/**
 * Represents a logical OR group: at least one sub-condition must be true.
 * e.g. { any: [cond1, cond2, ...] }
 */
export interface AnyCondition {
  any: Condition[];
}

/**
 * Our top-level union type:
 * - A simple condition (including custom),
 * - A group of conditions with 'all' (AND),
 * - A group of conditions with 'any' (OR).
 */
export type Condition = SimpleCondition | AllCondition | AnyCondition;
