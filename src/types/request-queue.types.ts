import { EntityName } from '~/db/realm/utils/constants';

export const RequestQueueStatus = {
  PENDING: 'PENDING',
  IN_PROGRESS: 'IN_PROGRESS',
  COMPLETED: 'COMPLETED',
  FAILED: 'FAILED',
} as const;

export const RequestType = {
  NEW: 'new',
  UPDATE: 'update',
} as const;

export interface IRequestQueue {
  entityId: string;
  entityName: (typeof EntityName)[keyof typeof EntityName];
  requestType: (typeof RequestType)[keyof typeof RequestType];
  payload: Record<string, any>;
  createdAt: Date;
  lastUpdatedAt: Date;
  retryCount: number;
  status: keyof typeof RequestQueueStatus;
}
