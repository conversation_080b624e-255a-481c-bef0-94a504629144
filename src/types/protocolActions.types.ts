/**
 * Defines core types for protocol actions and how they can be used.
 *
 * <p>StepActionType: The enumerated list of possible user actions at a step level, such as "navigate" or "close".
 * <p>StepAction: Represents a single action in a workflow step, possibly including conditional navigation.
 * <p>StepActionTargetStepCondition: Defines branching logic via "all" or "any" conditions that lead to a particular step.
 * <p>TransformDefinition & BeforeActionDefinition: Helper configurations for data transformations (e.g., combining fields).
 * <p>ProtocolActionType: The enumerated list of global protocol actions that can be invoked, e.g., "save".
 * <p>ProtocolActionDefinition: Details a named global action for advanced functionalities like Salesforce updates.
 * <p>ProtocolActionsMap: A dictionary of all global actions keyed by an ID.
 *
 * These types are used throughout the protocol engine to define step-level and global-level actions, including both navigation
 * and data manipulation.
 */

import { Condition } from '~/types/condition.types';
import {
  ButtonVariant,
  ButtonColor,
  IconPosition,
} from '~/types/component.types';
import {
  ConfirmationSubType,
  ConfirmationType,
} from '~/types/confirmations.types';

export type StepActionType =
  | 'navigate'
  | 'invokeAction'
  | 'close'
  | 'exit'
  | 'capturePhoto';

export type ProtocolContextData = Record<string, any>;

export type StepActionTargetStepCondition = {
  targetStepId: string;
  all?: Condition[];
  any?: Condition[];
};

type KeyValuePair = {
  key: string;
  value: any;
  setCurrentDateTime?: boolean;
};

type SetValueAction = {
  actionType: 'setValue';
  keyValuePairs: KeyValuePair[];
  updateCriteria?: Condition;
};

type ImmediateUpdateAction = {
  actionType: 'immediateUpdate';
  payload: Record<string, any>;
  updateCriteria?: Condition;
};

export type OnTapAction = SetValueAction | ImmediateUpdateAction;

type ActionLabels = {
  enabled: string;
  disabled: string;
};

export type StepAction = {
  label?: string | ActionLabels;
  key?: string;
  type: StepActionType;
  targetStepId?: string | null;
  actionId?: string;
  targetStepConditions?: StepActionTargetStepCondition[];
  targetKey?: string;
  linkedEntities?: string[];
  options?: {
    variant: ButtonVariant;
    priority: ButtonColor;
    requiresValidation?: boolean;
    icon?: string;
    iconPosition?: IconPosition;
    cameraScreenTitle?: string;
    key?: string;
    value?: any;
  };
  displayIf?: Condition;
  onTapAction?: OnTapAction[];
};

export type TransformDefinition = {
  operation: 'combine';
  sourceFields: string[] | StructuredTransformation[];
  delimiter?: string;
  destinationField: string;
  type: 'transform';
};

export type BeforeActionDefinition = TransformDefinition;

type ConfirmationActionType = {
  actionId: 'confirmation';
  options: {
    type: ConfirmationType;
    subType: ConfirmationSubType;
  };
};

export type AfterActionDefinition = ConfirmationActionType;

export type StructuredTransformation = {
  condition: TransformationCondition;
  value: string;
};

export type TransformationCondition = {
  all?: Condition[];
  any?: Condition[];
};

export type TransformationHandler = (params: {
  transformation: BeforeActionDefinition;
  currentState: Record<string, any>;
  transformedState: Record<string, any>;
}) => void;

export type ProtocolActionType = 'save' | 'navigate' | 'close';

export type ProtocolActionDefinition = {
  type: ProtocolActionType;
  before?: BeforeActionDefinition[];
  after?: AfterActionDefinition;
  targetStepId?: string | null;
  targetStepConditions?: StepActionTargetStepCondition[];
  objects?: {
    name: string;
    fields: string[];
    action: 'create' | 'update' | 'delete';
  }[];
};

export type ProtocolActionsMap = {
  [actionId: string]: ProtocolActionDefinition;
};
