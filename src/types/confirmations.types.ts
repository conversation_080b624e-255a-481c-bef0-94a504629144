// Enum for Confirmation Type
export const ConfirmationType = {
  Route: 'route',
  Pickup: 'pickup',
  Delivery: 'delivery',
  Stop: 'stop',
  Protocol: 'protocol',
} as const;

// Enum for Confirmation Sub Type
export const ConfirmationSubType = {
  RouteCompleted: 'route_completed',
  PickupContinueRoute: 'pickup_continue_route',
  PickupContinueDropoff: 'pickup_continue_dropoff',
  PickupSno: 'pickup_sno',
  PickupNoConfirm: 'pickup_no_confirm',
  DropoffContinueRoute: 'dropoff_continue_route',
  DropoffContinuePickup: 'dropoff_continue_pickup',
  SurveyCompleted: 'survey_completed',
  StopCompleted: 'stop_completed',
  ProtocolCompleted: 'protocol_completed',
} as const;

export type ConfirmationType =
  (typeof ConfirmationType)[keyof typeof ConfirmationType];

export type ConfirmationSubType =
  (typeof ConfirmationSubType)[keyof typeof ConfirmationSubType];
