export interface IImage {
  Id: string;
  Title: string;
  VersionData: string;
  PathOnClient: string;
  Coordinates__Latitude__s?: number;
  Coordinates__Longitude__s?: number;

  // linked entities
  StopId: string;
  ParcelId?: string;
  RouteSummaryId?: string;
  WorkOrderId?: string;

  // sync properties
  isSyncReady: boolean;
  isSynced: boolean;
  retryCount: number;
  createdAt: Date;
  updatedAt: Date;
}

export type ImageInfo = {
  id: string;
  uri: string;
};
