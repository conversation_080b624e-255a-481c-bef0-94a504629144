import { Attributes, BaseResponse, Type } from '~/types/shared.types';

export type Service = {
  Id: string;
  Name: string;
  Stop__c: string;
  Daily_Schedule__c: string;
  Service_Type_Name__c: string;
  Completed_Time__c: string | null;
  attributes: Attributes;
};

export type ServiceResponse = BaseResponse & {
  records: Service[] | null;
};

export type ServiceType = Type;

export type ServiceTypeResponse = BaseResponse & {
  records: ServiceType[] | null;
};
