import { SimpleCondition } from '~/types/condition.types.ts';
import {
  calculateDistanceBetweenCoordinatesInMeters,
  isValidCoordinate,
} from '~/utils/location.ts';
import { ProtocolContextData } from '~/types/protocolActions.types';
import { resolveValue } from '~/services/protocol/ProtocolConditionEvaluatorService';

/**
 * A custom operator function takes a condition and contextual data, then decides if the condition holds.
 */
export type CustomOperatorFn = (
  cond: SimpleCondition,
  data: Record<string, any>,
  context?: ProtocolContextData,
) => boolean;

/**
 * Our collection of custom operator functions. Each function inspects the given condition
 * and the relevant data, returning true if the condition is satisfied.
 */
export const customOperators: Record<string, CustomOperatorFn> = {
  /**
   * isWithinGeofence measures the distance between a reference coordinate and the coordinate
   * in data[cond.field], then returns true if that distance is within cond.maxRadius.
   */

  isWithinGeofence: (cond, targetCoord) => {
    const referenceIsValid = isValidCoordinate(cond.reference);
    const targetIsValid = isValidCoordinate(targetCoord);

    const missingOrInvalidProperties = !(referenceIsValid && targetIsValid);
    if (missingOrInvalidProperties) {
      console.warn(
        `[isWithinGeofence] Missing or invalid props: ${JSON.stringify({ cond, targetCoord })}`,
      );
      return false;
    }

    const distanceInMeters = calculateDistanceBetweenCoordinatesInMeters(
      cond.reference.lat,
      cond.reference.lon,
      targetCoord.lat,
      targetCoord.lon,
    );

    const isWithinRadius = distanceInMeters <= cond.maxRadius;
    return isWithinRadius;
  },

  isOutsideGeofence: (cond, targetCoord, context) => {
    if (!context) {
      console.warn('[isOutsideGeofence] Missing context');
      return false;
    }

    const { lat: latKey, lon: lonKey, radius } = cond.reference;
    const referenceLat = resolveValue(context, latKey);
    const referenceLon = resolveValue(context, lonKey);
    const maxRadius = resolveValue(context, radius);

    const reference = {
      lat: referenceLat,
      lon: referenceLon,
    };

    const referenceIsValid = isValidCoordinate(reference);
    const targetIsValid = isValidCoordinate(targetCoord);

    const missingOrInvalidProperties = !(referenceIsValid && targetIsValid);
    if (missingOrInvalidProperties) {
      console.warn(
        `[isOutsideGeofence] Missing or invalid props: ${JSON.stringify({ cond, targetCoord })}`,
      );
      return false;
    }

    const distanceInMeters = calculateDistanceBetweenCoordinatesInMeters(
      reference.lat,
      reference.lon,
      targetCoord.lat,
      targetCoord.lon,
    );
    const isWithinRadius = distanceInMeters <= maxRadius;
    return !isWithinRadius;
  },
  /**
   * isWithinTimeSince checks whether the current time minus the time in `data[cond.field]`
   * is less than or equal to `cond.value` (in seconds). For instance, if `cond.value` is 300,
   * this function checks if the event occurred within the last 5 minutes.
   */
  isWithinTimeSince: (cond, data) => {
    const completedTime = data;
    const timeFieldIsMissing = completedTime == null;
    if (timeFieldIsMissing) {
      console.warn(
        `[isWithinTimeSince] Missing time field "${cond.field}" in data: ${JSON.stringify(data)}`,
      );
      return false;
    }

    const timeFieldIsNotAString = typeof completedTime !== 'string';
    if (timeFieldIsNotAString) {
      console.warn(
        `[isWithinTimeSince] Time field "${cond.field}" is not a string in data: ${JSON.stringify(data)}`,
      );
      return false;
    }

    const thresholdInSeconds = cond.value;
    const thresholdIsNotANumber = typeof thresholdInSeconds !== 'number';
    if (thresholdIsNotANumber) {
      console.warn(
        `[isWithinTimeSince] Missing or invalid threshold in cond.value: ${JSON.stringify(cond)}`,
      );
      return false;
    }

    const parsedDate = new Date(completedTime);
    const dateIsInvalid = isNaN(parsedDate.getTime());
    if (dateIsInvalid) {
      console.warn(
        `[isWithinTimeSince] Invalid date format for "${cond.field}" in data: ${JSON.stringify(data)}`,
      );
      return false;
    }

    const elapsedMillis = Date.now() - parsedDate.getTime();
    const elapsedSeconds = Math.floor(elapsedMillis / 1000);

    const isWithinThreshold = elapsedSeconds <= thresholdInSeconds;
    return isWithinThreshold;
  },
  // Add more custom operators here as needed...
  includes: (cond, data) => {
    if (!data) {
      return false;
    }

    if (Array.isArray(data)) {
      return data.includes(cond.value);
    }
    return false;
  },
};
