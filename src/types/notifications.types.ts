export const NotificationType = {
  autoSyncUp: 'auto_syncup',
  stopAdded: 'stop_added',
  stopRemoved: 'stop_removed',
  shipmentPickupAdded: 'shipment_pickup_added',
  newWorkOrderAssigned: 'new_work_order_assigned',
} as const;

export type NotificationType =
  (typeof NotificationType)[keyof typeof NotificationType];

export type AdditionalData = Record<string, any> | undefined;

export type NewWorkOrderAssignedAdditionalData = {
  workOrderId: string;
  timeAssigned: string;
};

export type StopAdditionalData = {
  type: 'stop_added' | 'stop_removed' | 'shipment_pickup_added';
  data: {
    postDate: string;
    stopName: string;
    stopId: string;
    dailyScheduleId: string;
  };
};

export type StopAddedAdditionalData = StopAdditionalData;

export type StopRemovedAdditionalData = StopAdditionalData;

export type ShipmentPickupAddedAdditionalData = StopAdditionalData;

export const NotificationDisplayType = {
  Foreground: 'foreground',
  Background: 'background',
  Quit: 'quit',
} as const;
