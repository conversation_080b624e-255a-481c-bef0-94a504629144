import { ROUTE_STATUS } from '~/utils/constants';

export type AuthResponse = {
  access_token: string;
  instance_url: string;
  id: string;
  token_type: 'Bearer';
  issued_at: string;
  signature: string;
};

export type RouteStatus = keyof typeof ROUTE_STATUS;

export type RouteSummary = {
  attributes: {
    type: string;
    url: string;
  };
  LastModifiedDate: string;
  Id: string;
  Name: string;
  Planned_Start__c: string | null;
  Planned_End__c: string | null;
  Date__c: string;
  Start_Time__c: string | null;
  End_Time__c: string | null;
  Miles_Driven__c: number;
  Number_of_Open_Stops__c: string | null;
  Number_of_Stops__c: number;
  Number_of_Stops_Completed_Late__c: string | null;
  Number_of_Stops_Completed_Early__c: number;
  Geofencing_Distance__c: number | null;
  Status__c: RouteStatus;
  Service_Status__c: string | null;
  Route__r: {
    attributes: {
      type: string;
      url: string;
    };
    Name: string;
    Timezone__c: 0;
    Geofencing_Distance__c: string | null;
  };
  UTC_Offset__c: number | null;
  Driver__c: string | null;
  SNO_Flow__c: true;
  Lock_Check_In_Order__c: false;
  Rapid_Closure_Warning__c: false;
  Hours_Run__c: number;
  Default_Delivery_Destination__c: boolean | null;
  Survey_Complete__c: boolean | null;
  Require_End_of_Route_Survey__c: boolean | null;
  High_Temperature__c: number | null;
};

export type RouteList = {
  totalSize: number;
  done: true;
  records: RouteSummary[];
};
