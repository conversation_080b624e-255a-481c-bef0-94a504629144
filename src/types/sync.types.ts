import { EntityName } from '~/db/realm/utils/constants';
import { RouteSummary } from '~/types/routes.types';
import { RequestType } from '~/types/request-queue.types';

export type ActionResult = {
  success: boolean;
  message: string;
};

export type SyncResult = {
  entityId: string;
  entityName: (typeof EntityName)[keyof typeof EntityName];
};

export type RouteResponse = {
  records: RouteSummary[];
};

export type EntityType = (typeof EntityName)[keyof typeof EntityName];

export type EntityUpdateMode = (typeof RequestType)[keyof typeof RequestType];

export type UpdateEntityProps = {
  entityName: EntityType;
  entityId: string;
  requestType?: EntityUpdateMode;
  updates: Record<string, any>;
};
