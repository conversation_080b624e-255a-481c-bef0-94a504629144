const es = {
  // Navigation
  routes: '<PERSON>uta<PERSON>',
  route: '<PERSON><PERSON>',
  dispatch: '<PERSON><PERSON><PERSON>',
  profile: '<PERSON>fi<PERSON>',
  cancel: '<PERSON><PERSON><PERSON>',
  close: 'Cerrar',
  refresh: 'Actualizar',
  dropoff: 'Entrega',
  confirm: 'Confirmar',

  // Login screen
  email: 'Correo electrónico',
  password: 'Contraseña',
  login: 'Iniciar sesión',
  forgot_password: '¿Olvidaste tu contraseña?',
  terms: 'Al iniciar sesión aceptas nuestra ',
  privacy_policy: 'Política de Privacidad',

  // Route overview screen
  todays_routes: 'Rutas de hoy',
  current: 'Actual',
  upcoming: 'Próximas',
  see_route_details: 'Ver detalles de la ruta',
  active: 'Activa',
  completed: 'Completada',
  complete: 'Completar',
  no_active_routes: 'No hay rutas disponibles',
  no_completed_routes: 'No completaste ninguna ruta aún',
  stops: 'paradas',
  no_start_time_set: 'Sin hora de inicio',

  // Profile screen
  user_information: 'Información del usuario',
  name: 'Nombre',
  org_id: 'ID de Org.',
  user_id: 'ID de Usuario',
  device_information: 'Información del dispositivo',
  manufacturer: 'Fabricante',
  model: 'Modelo',
  os_version: 'Versión del Sistema Operativo',
  app_version: 'Versión de la applicación',
  free_storage: 'Almacenamiento libre',
  unknown: 'Desconocido',
  logout: 'Cerrar sesión',
  logout_with_unsaved_data: 'Datos no sincronizados',
  logout_info: 'Cerrarás sesión de {{name}}',
  logout_warning:
    'Tienes datos no sincronizados que necesitan ser subidos. Mantente conectado a una red estable para permitir que se complete la sincronización. Cerrar sesión antes de sincronizar puede resultar en la pérdida de datos',

  // Route detail screen
  nextStop: 'Próxima parada',
  lastCompletedStop: 'Última parada completada',
  navigateToStop: 'Navegar a la parada',
  completedRoute: 'Ruta completada',
  scheduledRoute: 'Ruta programada',
  canceledRoute: 'Ruta cancelada',
  arrivedAtStop: 'Has llegado a {{stopName}}',
  waitForMinutes: 'Espera {{minutes}} min',
  closureWarningTitle:
    '{{stopName}} fue completada hace menos de {{minutes}} minutos',
  closureWarningMessage:
    'Debes esperar al menos {{minutes}} minutos entre paradas. Por favor espera hasta que el temporizador haya terminado para marcar la siguiente parada como llegada',
  proof_of_service_missing: 'Completa la prueba de servicio para continuar',

  // Collect Signature screen
  sign_below: 'Firma abajo',
  add_comment: 'Comentario (opcional)',
  additional_information: 'Información adicional',
  complete_dropoff: 'Completar entrega',
  collect_signature: 'Solicitar firma',
  service_comment: 'Service comment (optional)',
  write_more_information: 'Write more information...',
  sign_to_complete: 'Sign to complete dropoff',

  // Schedule screens
  daily_schedule: 'Horario diario',
  stop_details: 'Detalles de la parada',
  select_a_service: 'Seleccionar un servicio',
  weekly_schedule: 'Horario semanal',
  select_date: 'Selecciona una fecha',
  select_date_subtitle: 'Selecciona una fecha para ver el horario diario',
  see_schedule: 'Ver horario',
  no_tasks_assigned: 'No hay tareas asignadas',
  no_tasks_assigned_details:
    'Actualmente no tienes tareas asignadas para la fecha seleccionada. Actualiza o vuelve a revisar más tarde',
  no_completed_tasks: 'No hay tareas completadas para mostrar',
  see_stop_details: 'Ver detalles de la parada',
  select_service_title: 'Selecciona un servicio para completar',
  select_service_subtitle:
    'Todas las acciones deben completarse para marcar esta parada como terminada',
  select_service_continue: 'Selecciona un servicio para continuar',
  select_service_continue_to: 'Continuar con {{task}}',

  // Stop detail screen
  getting_there: 'Cómo llegar',
  things_to_know: 'Detalles a tener en cuenta',
  tasks: 'Tareas',

  // Stop overview screen
  address: 'Dirección',
  notes: 'Notas',
  contact: 'Contact',
  parcels_expected: 'Paquetes esperados',
  expected_parcels: 'Expected parcels',
  pickup_parcels: 'Recoger paquetes',
  delivery_parcels: 'Entregar paquetes',
  add_new: 'Añadir nuevo',
  add_parcels_manually: 'Añadir paquetes manualmente',
  service_photo_proof: 'Foto de prueba de servicio',
  photos_taken: 'Fotos tomadas',
  take_new: 'Tomar una nueva',
  take_photo: 'Tomar una foto',
  photos_taken_appear_here: 'Las fotos tomadas aparecerán aquí',
  comments_optional: 'Comentario (opcional)',
  complete_pickup: 'Completar recogida',
  no_notes_available: 'No hay notas disponibles',
  stop_overview: 'Resumen de la parada',
  delivery: 'Entrega',
  pickup: 'Recogida',
  exchange: 'Intercambio',
  service: 'Servicio',
  address_copied: 'Dirección copiada al portapapeles',
  stop_update_failed: 'Error al actualizar la parada',
  stop_failed_to_queue: 'Error al poner añadir la parada',
  parcels: 'Parcels',
  delivery_destination: 'Delivery destination',

  barcode_not_matched: 'Barcode not matched',
  barcode_not_matched_description:
    'Barcode scanned does not match the parcel selected. Select the correct parcel or scan barcode',
  barcode_not_found: 'Barcode not found',
  barcode_not_found_description:
    'Barcode scanned does not match a parcel on this stop. Contact dispatch to resolve this',

  // Add/Edit Parcel
  parcel_information: 'Información del paquete',
  scan_barcode_info: 'Mantén tu celular sobre el código de barras',
  scan_barcode_title: 'Escanear código de barras',
  barcode: 'Código de barras',
  select_parcel_type: 'Seleccionar tipo',
  search_parcel_type: 'Buscar tipo',
  type: 'Tipo',
  declare_parcel: 'Declarar paquete',
  type_label: 'Tipo',
  quantity_label: 'Cantidad',
  number_of_parcels: 'Número de paquetes',
  sort_by: 'Ordenar por',
  continue: 'Continuar',
  dropoff_destination: 'Destino de entrega',
  select_dropoff_destination: 'Selecciona un destino de entrega',
  signature_required: '¿Se requiere firma?',
  quantity: 'Cantidad',
  parcel_proof_of_service: 'Prueba de servicio del paquete',
  take_parcel_photo: 'Foto del paquete',
  yes: 'Sí',
  no: 'No',
  add_a_parcel: 'Añadir un paquete',
  edit_parcel: 'Editar paquete',
  save: 'Guardar',
  save_parcel: 'Guardar paquete',
  no_result_found: 'No se encontraron resultados',
  pending_scan: 'Pending scan',
  scan_barcode: 'Scan barcode',
  confirm_parcel: 'Confirm parcel',

  // work orders
  complete_pickup_tasks_to_continue: 'Complete pickup tasks to continue',
  complete_delivery_tasks_to_continue: 'Complete delivery tasks to continue',
  complete_photo_to_continue: 'Complete photo to continue',
  no_contact_listed: 'No contact listed',

  // utils
  invalidData: 'Datos inválidos',
  warning: 'Advertencia',
  open_settings: 'Abrir configuración',
  unable_to_openSettings: 'No se puede abrir la configuración',
  unable_to_open_settings: 'Unable to open settings',
  locationPermissions: 'Permisos de ubicación',
  cameraPermissionDesc:
    'Se requiere acceso a la cámara para completar tus tareas. Por favor, actívala en la configuración de la app',
  locationPermissionDescIOS:
    'Se requiere acceso a la ubicación para un seguimiento preciso. Por favor, selecciona "Siempre" y activa "Ubicación precisa" en la configuración de la app',
  locationPermissionDescAndroid:
    'Se requiere acceso a la ubicación para un seguimiento preciso. Por favor, selecciona "Permitir todo el tiempo" y activa "Usar ubicación precisa" en la configuración de la app',
  motionPermissionDescIOS:
    'Se requiere acceso a Movimiento y Fitness para un rendimiento óptimo. Sin él, tu batería puede agotarse más rápido. Por favor, actívalo en la configuración de la app',
  motionPermissionDescAndroid:
    'Se requiere acceso a Actividad Física para un rendimiento óptimo. Sin él, tu batería puede agotarse más rápido. Por favor, selecciona "Permitir" en la configuración de la app',
  unableToFetchLocation: 'Unable to fetch your current location.',

  // Driver actions
  already_marked_arrived: 'Ya marcaste esta parada como llegada',
  must_arrive_first: 'Primero debes llegar a esta parada',
  already_marked_completed: 'Ya marcaste esta parada como completada',
  stop_not_found: 'Parada no encontrada',
  route_not_found: 'Ruta no encontrada',
  invalid_parameters: 'Parámetros inválidos',
  invalid_route_summery_id: 'ID de resumen de ruta inválido',
  stop_updated: 'Parada {{field}} actualizada',
  route_updated: 'Ruta {{field}} actualizada',
  parcel_saved: 'Paquete guardado',
  parcel_not_found: 'Paquete no encontrado',
  parcel_updated: 'Paquete actualizado',
  parcel_deleted: 'Paquete eliminado',
  parcel_create_failed: 'Error al crear el paquete',
  parcel_update_failed: 'Error al actualizar el paquete',
  parcel_delete_failed: 'Error al eliminar el paquete',
  no_parcels_picked_at_stop: 'No se recogieron paquetes en esta parada',
  parcels_created: '{{count}} paquetes creados en la parada {{stopId}}',
  images_request_created: '{{count}} imágenes creadas en la parada {{stopId}}',

  // Bottom sheet
  sort: 'Ordenar',
  invalid_date: 'Fecha inválida',
  title_a_to_z: 'A a Z',
  subtitle_sorted_alphabetical: 'Ordenado alfabéticamente',
  title_z_to_a: 'Z a A',
  subtitle_sorted_reverse_alphabetical: 'Ordenado en orden alfabético inverso',
  title_next_available_stops: 'Próximas paradas disponibles',
  subtitle_sorted_closest_time: 'Ordenado por tiempo más cercano al actual',

  // Confirmation screen text
  confirmation_screen: 'Pantalla de confirmación',
  route_completed: 'Ruta completada',
  pickup_completed: 'Recogida completada',
  dropoff_completed: 'Entrega completada',
  sub_pickup_continue_route: 'Continuar con tu ruta',
  sub_pickup_continue_dropoff: 'Continuar con la entrega',
  sub_pickup_sno: 'Muestras no disponibles (SNO)',
  sub_pickup_no_confirm: 'SNO - Sin confirmación de recogida',
  sub_dropoff_continue_route: 'Continuar con tu ruta',
  sub_dropoff_continue_pickup: 'Continuar con la recogida',
  great_job: '¡Buen trabajo!',
  stop_completed: 'Parada completada',

  // Camera view screen
  retake: 'Volver a tomar',
  re_scan: 'Volver a escanear',
  submit: 'Enviar',
  scanning: 'Escaneando...',
  loading: 'Cargando...',
  error: 'Error',
  failed_to_take_photo: 'Error al tomar la foto: {{error}}',
  unexpected_error_occurred_while_capturing_the_photo:
    'Ocurrió un error inesperado al capturar la foto.',
  photo_view: 'Visor de fotos',
  take_a_proof_of_service_photo: 'Take a proof of service photo',
  take_a_parcel_photo: 'Take a parcel photo',

  // Image manager
  image_invalid_payload: 'Carga de imagen inválida',
  image_upload_failed: 'Error al subir la imagen',
  image_delete_failed: 'Error al eliminar la imagen',
  image_not_found: 'Imagen no encontrada',
  image_add_update_success: 'Imagen añadida/actualizada exitosamente',
  image_add_update_failed: 'Error al añadir/actualizar la imagen',

  // Errors
  error_generic:
    'Ocurrió un error inesperado. Por favor, inténtalo de nuevo más tarde',
  error_auth_failure:
    'Credenciales incorrectas. Por favor, asegúrate de que tu correo electrónico y contraseña sean correctos',
  error_no_network:
    'Error al conectarse a una red. Por favor, verifica tu conexión e inténtalo de nuevo',

  // Database
  database_connection_failed: 'Error de conexión a la base de datos',

  // Protocol
  update_failed: 'Error al actualizar el resumen de la ruta',
  delete_parcel: 'Eliminar [{quantity} {type}]',
  delete_parcel_description:
    'Esta acción no se puede recuperar, si se elimina se eliminará permanentemente',
  delete: 'Eliminar',
  protocol_completed: 'Protocol verification completed',

  // Notifications - TODO: Translate
  notification_permission: 'Notification permission',
  push_notifications_disabled:
    'Push notifications are disabled. Please allow push notifications in your phone settings for Rapid Medical app',
  push_notifications_limited: 'Push notifications are limited on this device.',
  push_notifications_unavailable:
    'Push notifications are unavailable on this device.',

  // Bypass barcode
  bypass_barcode: 'Bypass barcode',
  enter_manually: 'Enter manually',

  // Barcode entry screen
  barcode_required: 'Barcode is required',
  barcode_too_short: 'Barcode must be at least 3 characters',
  quantity_required: 'Quantity is required',
  quantity_invalid: 'Quantity must be a positive number',
  barcode_photo: 'Barcode photo',
  bypass_reason_label: "Why wasn't the barcode scanned?",
  bypass_reason_placeholder: 'Write more information...',
  damaged_barcode: 'Damaged barcode',
  barcode_mismatch: 'Barcode mismatch',

  // Barcode input modal
  enter_barcode_manually: 'Enter barcode',
  please_enter_the_barcode_number: 'Please enter the barcode number',
  parcels_confirmed_or_added_will_appear_here:
    'Parcels confirmed or added will appear here',
  select_reason_to_bypass: 'Select reason to bypass',
  enter_barcode_to_continue: 'Enter barcode to continue',
};

export default es;
