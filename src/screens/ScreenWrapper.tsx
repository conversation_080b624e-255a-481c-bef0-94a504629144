import React from 'react';
import { View, StyleSheet, StyleProp, ViewStyle, Platform } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import useKeyboardListener from '~/hooks/useKeyboardListener';
import colors from '~/styles/colors';

type ScreenWrapperProps = {
  color?: string;
  children: React.ReactNode;
  style?: StyleProp<ViewStyle>;
  isKeyboardSensitive?: boolean;
};

const ScreenWrapper = ({
  children,
  style,
  color = colors.backgroundLight,
  isKeyboardSensitive = false,
}: ScreenWrapperProps) => {
  const { bottom } = useSafeAreaInsets();
  const { keyboardHeight, isKeyboardVisible } = useKeyboardListener();

  const extraSpacing = bottom === 0 ? 0 : styles.bottom.padding;

  const paddingBottom = isKeyboardSensitive
    ? Platform.select({
        ios: isKeyboardVisible ? keyboardHeight - extraSpacing : 0,
        android: 0,
      })
    : 0;

  return (
    <View
      style={[
        styles.container,
        { backgroundColor: color, paddingBottom },
        style,
      ]}>
      {children}
    </View>
  );
};

const ScreenBody = ({
  children,
  withoutPadding = false,
}: {
  children: React.ReactNode;
  withoutPadding?: boolean;
}) => {
  const bodyStyle = {
    ...styles.body,
    padding: withoutPadding ? 0 : styles.body.padding,
  };

  return <View style={bodyStyle}>{children}</View>;
};

const ScreenBottom = ({ children }: { children: React.ReactNode }) => {
  const { bottom } = useSafeAreaInsets();

  const updatedBottom = Platform.select({
    ios: bottom === 0 ? styles.bottom.padding : bottom,
    android:
      bottom === 0 ? styles.bottom.padding : bottom + styles.bottom.padding,
  });

  const bottomContainerStyle = {
    ...styles.bottom,
    paddingBottom: updatedBottom,
  };

  return <View style={bottomContainerStyle}>{children}</View>;
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  body: {
    flex: 1,
    padding: 16,
  },
  bottom: {
    gap: 16,
    padding: 16,
    backgroundColor: colors.white,
    shadowOffset: { width: 0, height: 4 },
    shadowColor: colors.grayishBlue,
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
});

ScreenWrapper.Body = ScreenBody;
ScreenWrapper.Bottom = ScreenBottom;

export default ScreenWrapper;
