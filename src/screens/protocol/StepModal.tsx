import React, { useEffect } from 'react';
import { StyleSheet, View } from 'react-native';
import { RouteProp } from '@react-navigation/native';

import colors from '~/styles/colors';
import { container } from '~/styles/views';
import { ProtocolEngineStackParams } from '~/navigation/ProtocolEngineStack';
import { padding20 } from '~/styles/spacing';
import {
  ProtocolEngineStackNavigationProp,
  useProtocolContext,
} from '~/services/protocol/context/ProtocolContext';
import StepComponents from '~/components/protocol/StepComponents';

export default function StepModal({
  route,
  navigation,
}: {
  route: RouteProp<ProtocolEngineStackParams, 'StepModal'>;
  navigation: ProtocolEngineStackNavigationProp;
}) {
  const { step } = route.params;
  const { setNestedNavigationRef } = useProtocolContext();

  useEffect(() => {
    setNestedNavigationRef(navigation);
  }, [navigation, setNestedNavigationRef]);

  return (
    <View style={styles.container}>
      <StepComponents step={step} />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    ...container,
    ...padding20,
    justifyContent: 'center',
    backgroundColor: colors.transBlack40,
  },
});
