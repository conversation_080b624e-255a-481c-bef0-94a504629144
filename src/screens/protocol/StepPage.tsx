import React, { useEffect } from 'react';
import { View } from 'react-native';
import { RouteProp } from '@react-navigation/native';
import { container } from '~/styles/views';
import { ProtocolEngineStackParams } from '~/navigation/ProtocolEngineStack';
import {
  ProtocolEngineStackNavigationProp,
  useProtocolContext,
} from '~/services/protocol/context/ProtocolContext';
import StepComponents from '~/components/protocol/StepComponents';

export default function StepPage({
  route,
  navigation,
}: {
  route: RouteProp<ProtocolEngineStackParams, 'StepPage'>;
  navigation: ProtocolEngineStackNavigationProp;
}) {
  const { step } = route.params;
  const { setNestedNavigationRef } = useProtocolContext();

  useEffect(() => {
    setNestedNavigationRef(navigation);
  }, [navigation, setNestedNavigationRef]);

  return (
    <View style={container}>
      <StepComponents step={step} />
    </View>
  );
}
