import React, { useState } from 'react';
import { Text } from '@rneui/themed';
import {
  StyleSheet,
  View,
  KeyboardAvoidingView,
  TouchableWithoutFeedback,
  Keyboard,
  ViewStyle,
  TextStyle,
} from 'react-native';
import FilledButton from '~/components/buttons/FilledButton';
import TextInput from '~/components/inputs/TextInput';
import en from '~/localization/en';
import TextURLButton from '~/components/buttons/TextURLButton';
import { URLs } from '~/utils/constants';
import { primaryText, p } from '~/styles/text';
import { screenBaseWhite, container, row, deviceHeight } from '~/styles/views';
import { buttonFullWidth, primarySolidButton } from '~/styles/buttons';
import Logo from '~/assets/logo.svg';
import colors from '~/styles/colors';
import {
  checkIfValidEmail,
  getEmailValidationMessage,
  getPasswordValidationMessage,
} from '~/utils/validation/input';
import { Email, EyeClosed, EyeOpen } from '~/components/icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { AuthService } from '~/services/AuthService';
import ErrorText from '~/components/text/ErrorText';
import { getHumanReadableErrorMessage } from '~/utils/errors';

const LoginScreen = ({ navigation }: any) => {
  const { bottom } = useSafeAreaInsets();

  const [email, setEmail] = useState<string | null>(null);
  const [password, setPassword] = useState<string | null>(null);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isShowingPassword, setIsShowingPassword] = useState<boolean>(false);

  const handleLogin = async () => {
    setErrorMessage(null);
    setIsLoading(true);

    try {
      if (email && password) {
        await AuthService.login(email, password);
        navigation.replace('Main', { screen: 'Routes' });
      }
    } catch (error) {
      const readableErrorMessage: string = getHumanReadableErrorMessage(error);
      setErrorMessage(readableErrorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const isLoginButtonDisabled = (): boolean => {
    return (
      (email && !checkIfValidEmail(email)) || !password?.trim() || isLoading
    );
  };

  const bottomContainerStyle = {
    ...styles.bottomView,
    paddingBottom: bottom === 0 ? styles.bottomView.paddingBottom : bottom,
  };

  return (
    <KeyboardAvoidingView style={container}>
      <View style={screenBaseWhite as ViewStyle}>
        <TouchableWithoutFeedback
          onPress={Keyboard.dismiss}
          id="LoginScreen.TouchableWithoutFeedback"
          testID="LoginScreen.TouchableWithoutFeedback">
          <View style={styles.div}>
            <Logo />

            <View style={styles.divInputsWrapper}>
              <TextInput
                id={'LoginScreen.InputField.email'}
                onChangeText={(value: string) => setEmail(value)}
                label="Email"
                placeholder="Email address"
                icon={<Email />}
                errorMessage={email && getEmailValidationMessage(email)}
                keyboardType="email-address"
              />

              <TextInput
                id={'LoginScreen.InputField.password'}
                onChangeText={(value: string) => setPassword(value)}
                label="Password"
                placeholder="Password"
                secureTextEntry={!isShowingPassword}
                icon={
                  <TouchableWithoutFeedback
                    id={'LoginScreen.TouchableWithoutFeedback.eye'}
                    testID={'LoginScreen.TouchableWithoutFeedback.eye'}
                    accessibilityRole={'button'}
                    onPress={() =>
                      setIsShowingPassword(prevViewState => !prevViewState)
                    }>
                    <View>
                      {isShowingPassword ? <EyeClosed /> : <EyeOpen />}
                    </View>
                  </TouchableWithoutFeedback>
                }
                errorMessage={
                  password && getPasswordValidationMessage(password)
                }
              />
            </View>
          </View>
        </TouchableWithoutFeedback>

        {errorMessage && (
          <ErrorText id="LoginScreen.ErrorText.msg" text={errorMessage} />
        )}

        <View style={bottomContainerStyle}>
          <FilledButton
            id={'LoginScreen.FilledButton.login'}
            title={en.login}
            onClick={handleLogin}
            color="primary"
            isDisabled={isLoginButtonDisabled()}
            isLoading={isLoading}
            style={[buttonFullWidth, primarySolidButton]}
          />

          <View style={row as ViewStyle}>
            <Text
              id="LoginScreen.text.terms"
              testID="LoginScreen.text.terms"
              style={[p, styles.bySigningInTypo] as TextStyle[]}>
              {en.terms}
            </Text>
            <TextURLButton
              id="LoginScreen.TextURLButton.privacy"
              style={[p, primaryText] as TextStyle}
              title={en.privacy_policy}
              url={URLs.PRIVACY_POLICY}
            />
          </View>
        </View>
      </View>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  bySigningInTypo: {
    ...p,
    color: colors.darkGray,
    fontSize: 14,
    alignSelf: 'stretch',
  },
  content: {
    letterSpacing: -0.1,
    textAlign: 'left',
    color: colors.grey900,
    flex: 1,
  },
  divInputsWrapper: {
    width: '100%',
    gap: 24,
  },
  div: {
    flex: 1,
    gap: deviceHeight * 0.05,
    alignItems: 'center',
    justifyContent: 'center',
  },
  bottomView: {
    gap: 16,
    paddingBottom: 20,
    alignItems: 'center',
    marginTop: 20,
  },
});

export default LoginScreen;
