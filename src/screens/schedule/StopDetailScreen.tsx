import { useQuery, useRealm } from '@realm/react';
import { Divider } from '@rneui/base';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Stop } from 'react-native-svg';
import OutlinedButton from '~/components/buttons/OutlinedButton';
import StopActionButton from '~/components/buttons/StopActionButton';
import {
  Notes,
  LocationPinOutlined,
  RouteOutlined,
  Nut,
} from '~/components/icons';
import InfoItem from '~/components/info/InfoItem';
import MapView from '~/components/maps/MapView';
import InfoPanel from '~/components/placeholders/InfoPanel';
import { RouteSummary } from '~/types/routes.types';
import { StopSchema } from '~/db/realm/schemas/stop.schema';
import en from '~/localization/en';
import ScreenWrapper from '~/screens/ScreenWrapper';
import { gap24 } from '~/styles/spacing';
import {
  StopWithRelations,
  StopStatusType,
  StopType,
} from '~/types/stops.types';
import { openMap } from '~/utils/maps';
import { useNavigation } from '@react-navigation/native';
import ToastMessage from '~/components/toast/ToastMessage';
import { ToastType } from '~/types/toast.types';
import {
  InteractionManager,
  RefreshControl,
  ScrollView,
  View,
} from 'react-native';
import colors from '~/styles/colors';
import { getFormattedTime } from '~/utils/dateAndTime';
import { RouteWithBackground } from '~/assets/icons';
import {
  getDeliveryParcelsByStopId,
  getPickupParcelsByStopId,
} from '~/db/realm/operations/parcel.operations';
import { Parcel } from '~/types/parcel.types';
import { useSyncService } from '~/services/sync';
import { getRouteById } from '~/db/realm/operations/route.operations';
import ErrorComponent from '~/components/error/ErrorComponent';
import BottomSheetModal, {
  ActionItem,
} from '~/components/modals/BottomSheetModal';
import { getAddressString, getCoordinates } from '~/utils/stops';

interface StopDetailScreenProps {
  route: {
    params: {
      date: string;
      stopId: string;
      isCurrent: boolean;
      showPlaceholder?: boolean;
      placeholderTime?: string;
    };
  };
}

const queryFilterForUpcomingStops = `Post_Date__c == $0 AND NOT Status__c == $1 AND NOT Status__c == $2`;
const queryFilterForUpcomingStopsInRoute = `Post_Date__c == $0 AND Summary__c == $1 AND NOT Status__c == $2 AND NOT Status__c == $3`;

const NavigationOptions = {
  IN_APP: 'in_app_navigation',
  EXTERNAL: 'external_maps',
  CANCEL: 'cancel',
} as const;

const StopDetailScreen = ({ route }: StopDetailScreenProps) => {
  const { stopId, isCurrent, showPlaceholder, placeholderTime, date } =
    route.params;
  const realm = useRealm();
  const navigation = useNavigation();
  const [isDelivery, setIsDelivery] = useState(false);
  const [isPickup, setIsPickup] = useState(false);
  const [isService, setIsService] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  const [pickupParcels, setPickupParcels] = useState<Parcel[]>([]);
  const [deliveryParcels, setDeliveryParcels] = useState<Parcel[]>([]);
  const [currentRoute, setCurrentRoute] = useState<RouteSummary | null>(null);
  const [coordinates, setCoordinates] = useState<{
    latitude: number;
    longitude: number;
  } | null>(null);

  const [showNavigationSheet, setShowNavigationSheet] =
    useState<boolean>(false);

  const stopResults = useQuery<StopWithRelations>(StopSchema.name).filtered(
    'Id == $0',
    stopId,
  );
  const stop = stopResults[0];

  const { syncDown } = useSyncService();

  const stopAccessSettings = useMemo(() => {
    if (!currentRoute) {
      return {
        hasOrderLock: false,
        allowsFlexibleSequence: false,
        hasRapidClosureWarning: true,
      };
    }

    const hasOrderLock = currentRoute.Lock_Check_In_Order__c;

    return {
      hasOrderLock,
      allowsFlexibleSequence: !hasOrderLock,
      hasRapidClosureWarning: Boolean(currentRoute.Rapid_Closure_Warning__c),
    };
  }, [currentRoute]);

  const stopAccessibility = useMemo(() => {
    const isCompleted = stop?.Completed_Time__c !== null;
    const isCurrentStop = isCurrent;
    const allowsFlexibleAccess = stopAccessSettings.allowsFlexibleSequence;

    return {
      canAccess: isCompleted || isCurrentStop || allowsFlexibleAccess,
      canPerformActions:
        !isCompleted && (isCurrentStop || allowsFlexibleAccess),
    };
  }, [
    stop?.Completed_Time__c,
    isCurrent,
    stopAccessSettings.allowsFlexibleSequence,
  ]);

  const { canAccess, canPerformActions } = stopAccessibility;

  const getNavigationOptions = (): ActionItem[] =>
    [
      {
        id: NavigationOptions.IN_APP,
        title: 'In-App Navigation',
        onPress: () => navigateToMapScreen(),
      },
      {
        id: NavigationOptions.EXTERNAL,
        title: 'External Maps App',
        onPress: () => openExternalMap(),
      },
    ].filter(Boolean) as ActionItem[];

  const getParcels = useCallback(async () => {
    const fetchedPickupParcels = await getPickupParcelsByStopId(stop?.Id);
    setPickupParcels(fetchedPickupParcels);

    const fetchedDeliveryParcels = await getDeliveryParcelsByStopId(stop?.Id);
    setDeliveryParcels(fetchedDeliveryParcels);
  }, [stop?.Id]);

  const getRoute = useCallback(async () => {
    const fetchedRoute = await getRouteById(stop?.Summary__c);
    setCurrentRoute(fetchedRoute ?? null);
  }, [stop?.Summary__c]);

  useEffect(() => {
    getParcels();
    getRoute();
  }, [getParcels, getRoute, stop?.Id]);

  useEffect(() => {
    const coords = getCoordinates(stop);
    setCoordinates(coords);
  }, [stop?.Id, stop]);

  const isPickupStop =
    stop?.Pickup_Parcels__r !== null ||
    pickupParcels.length > 0 ||
    stop.Type__c === StopType.Pickup ||
    stop.Type__c === StopType.Start ||
    stop.Type__c === StopType.Exchange;

  const isDeliveryStop =
    stop?.Delivery_Parcels__r !== null ||
    deliveryParcels.length > 0 ||
    stop.Type__c === StopType.Delivery ||
    stop.Type__c === StopType.End ||
    stop.Type__c === StopType.Exchange;

  const isServiceStop = stop?.Services__r !== null;

  useEffect(() => {
    setIsPickup(isPickupStop);
    setIsDelivery(isDeliveryStop);
    setIsService(isServiceStop);
  }, [
    isPickupStop,
    isDeliveryStop,
    isServiceStop,
    pickupParcels,
    deliveryParcels,
  ]);

  const getTasksText = () => {
    let taskList = [];

    if (isPickupStop) {
      taskList.push(StopType.Pickup);
    } else if (isDeliveryStop === false && isServiceStop === false) {
      return StopType.Pickup;
    }

    if (isDeliveryStop) {
      taskList.push(StopType.Delivery);
    }

    if (isServiceStop) {
      taskList.push(StopType.Service);
    }

    return taskList.join(', ');
  };

  const stopHasRouteSummary = Boolean(stop?.Summary__c);

  const stopListFilters = useMemo(() => {
    return stopHasRouteSummary
      ? [
          date,
          stop?.Summary__c,
          StopStatusType.COMPLETE,
          StopStatusType.INACTIVE,
        ]
      : [date, StopStatusType.COMPLETE, StopStatusType.INACTIVE];
  }, [date, stop?.Summary__c, stopHasRouteSummary]);

  const lastCompletedStop = useMemo<StopWithRelations | undefined>(() => {
    const completedStops = realm
      .objects<StopWithRelations>(StopSchema.name)
      .filtered('Completed_Time__c != null')
      .sorted('Completed_Time__c', true);

    return Array.from(completedStops)?.[0];
  }, [realm]);

  const upcomingStopList = useMemo<Stop[]>(() => {
    const upcomingStops = realm
      .objects<Stop>(StopSchema.name)
      .filtered(
        stopHasRouteSummary
          ? queryFilterForUpcomingStopsInRoute
          : queryFilterForUpcomingStops,
        ...stopListFilters,
      )
      .sorted('Stop_Time_Preferred__c', true);

    return Array.from(upcomingStops);
  }, [realm, stopHasRouteSummary, stopListFilters]);

  const renderToastView = () => {
    if (stop?.Completed_Time__c !== null) {
      return (
        <ToastMessage
          variant="banner"
          type={ToastType.SUCCESS}
          message={`Stop completed at ${getFormattedTime(stop?.Completed_Time__c)}`}
        />
      );
    }

    if (!canAccess) {
      const message = stopAccessSettings.hasOrderLock
        ? 'Route requires stops to be completed in order. Complete the current stop first.'
        : 'Current stop needs to be finished before this one.';

      return (
        <ToastMessage
          variant="banner"
          type={ToastType.WARNING}
          message={message}
          linkText="Go back to view current stop"
          onPress={() => navigation.goBack()}
          testID="ToastMessage.Banner"
        />
      );
    }

    return null;
  };

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);

    try {
      syncDown();
    } finally {
      setRefreshing(false);
    }
  }, [syncDown]);

  if (!stop && !showPlaceholder) {
    return <ErrorComponent errorText={en.invalidData} />;
  }

  const navigateToMapScreen = () => {
    setShowNavigationSheet(false);
    navigation.navigate('MapNavigationScreen', {
      stop,
      enableRapidClosureWarning:
        currentRoute?.Rapid_Closure_Warning__c ?? false,
      lastCompletedStop,
      currentRoute,
      stopList: Array.from(upcomingStopList) as unknown as StopWithRelations[],
      isPickup,
      isDelivery,
      isService,
      coordinates,
    });
  };

  const showNavigationOptions = () => {
    setShowNavigationSheet(true);
  };

  const closeNavigationModel = () => {
    setShowNavigationSheet(false);
  };

  const openExternalMap = () => {
    setShowNavigationSheet(false);

    InteractionManager.runAfterInteractions(() => {
      openMap({
        lat: stop?.Stop_Coordinates__Latitude__s ?? coordinates?.latitude,
        lng: stop?.Stop_Coordinates__Longitude__s ?? coordinates?.longitude,
        label: stop?.Name,
      });
    });
  };

  return (
    <ScreenWrapper color={colors.white}>
      <ScreenWrapper.Body>
        <ScrollView
          contentContainerStyle={gap24}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
          }>
          {showPlaceholder && Boolean(placeholderTime) ? (
            <InfoPanel
              title={'Route details'}
              infoText={'Route details will be available at ' + placeholderTime}
              icon={<RouteWithBackground />}
            />
          ) : (
            <View style={gap24}>
              {renderToastView()}

              {coordinates && (
                <MapView
                  coordinate={[coordinates.longitude, coordinates.latitude]}
                />
              )}

              <Divider />

              {Boolean(currentRoute) && (
                <InfoItem
                  title={en.route}
                  infoText={currentRoute?.Name ?? ''}
                  icon={<RouteOutlined />}
                />
              )}

              <InfoItem
                title={en.getting_there}
                infoText={getAddressString(stop)}
                icon={<LocationPinOutlined />}
                enableCopy={true}
              />

              <InfoItem
                title={en.tasks}
                infoText={getTasksText()}
                icon={<Nut />}
              />

              {Boolean(stop?.Notes__c) && (
                <InfoItem
                  title={en.things_to_know}
                  infoText={stop?.Notes__c ?? ''}
                  icon={<Notes />}
                />
              )}
            </View>
          )}
        </ScrollView>
      </ScreenWrapper.Body>

      {canPerformActions && (
        <ScreenWrapper.Bottom>
          <StopActionButton
            lastCompletedStop={lastCompletedStop}
            nextStop={stop}
            currentRoute={currentRoute}
            stopList={
              Array.from(upcomingStopList) as unknown as StopWithRelations[]
            }
            isPickup={isPickup}
            isDelivery={isDelivery}
            isService={isService}
          />
          <OutlinedButton
            title={en.navigateToStop}
            onClick={showNavigationOptions}
            color={'primary'}
            isDisabled={false}
            isLoading={false}
          />
        </ScreenWrapper.Bottom>
      )}

      <BottomSheetModal
        isVisible={showNavigationSheet}
        onClose={closeNavigationModel}
        actions={getNavigationOptions()}
      />
    </ScreenWrapper>
  );
};

export default StopDetailScreen;
