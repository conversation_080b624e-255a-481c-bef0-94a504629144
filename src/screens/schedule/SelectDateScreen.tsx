import React, { useEffect, useState, useCallback } from 'react';
import { View, ScrollView, RefreshControl } from 'react-native';
import Title from '~/components/text/Title';
import ScreenWrapper from '~/screens/ScreenWrapper';
import en from '~/localization/en';
import { container } from '~/styles/views';
import DateCard from '~/components/cards/DateCard';
import { gap16, padding16, paddingTop6 } from '~/styles/spacing';
import { useBottomTabBarHeight } from '@react-navigation/bottom-tabs';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { DateTime } from 'luxon';
import { ScheduleStackParamList } from '~/navigation/ScheduleStack';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { getTimeOfDay } from '~/utils/dateAndTime';
import { usePermissionHandler } from '~/hooks/usePermissionHandler';
import { PermissionType } from '~/types/permission.types';
import { initLocationService } from '~/services/location/LocationService';
import { setOneSignalUser } from '~/services/notifications/NotificationsService';
import { useAppState } from '~/hooks/useAppState';

const SelectDateScreen = ({
  navigation,
}: {
  navigation: NativeStackNavigationProp<ScheduleStackParamList>;
}) => {
  const [today, setToday] = useState<string>();
  const [yesterday, setYesterday] = useState<string>();
  const [isRefreshing, setIsRefreshing] = useState(false);

  usePermissionHandler({
    permissionTypes: [
      PermissionType.LOCATION,
      PermissionType.MOTION,
      PermissionType.NOTIFICATION,
    ],
    successCallback: initLocationService,
  });

  // Function to update dates
  const updateDates = useCallback(() => {
    const todaysDate = DateTime.now().toFormat('MMMM dd, yyyy');
    const yesterdaysDate = DateTime.now()
      .minus({ days: 1 })
      .toFormat('MMMM dd, yyyy');

    setToday(todaysDate);
    setYesterday(yesterdaysDate);
  }, []);

  useEffect(() => {
    if (global.userId) {
      setOneSignalUser();
    }
  }, []);

  // Use app state to refresh dates when app comes to foreground
  useAppState({
    onForeground: () => {
      updateDates();
    },
  });

  useEffect(() => {
    updateDates();
  }, [updateDates]);

  // Handle refresh
  useEffect(() => {
    if (isRefreshing) {
      updateDates();
      setIsRefreshing(false);
    }
  }, [isRefreshing, updateDates]);

  const { bottom } = useSafeAreaInsets();
  const bottomTabBarHeight = useBottomTabBarHeight();

  const scrollContainerStyle = {
    ...padding16,
    paddingBottom:
      bottomTabBarHeight + (bottom ?? padding16.padding) + padding16.padding,
  };

  const handleDateCardPress = (date: string) => {
    const dateTime = DateTime.fromFormat(date, 'MMMM dd, yyyy').toFormat(
      'yyyy-MM-dd',
    );

    navigation.navigate('DailyScheduleScreen', {
      date: dateTime,
      title: date,
    });
  };

  const getTitle = () => {
    const timeOfDay = getTimeOfDay();
    return `Good ${timeOfDay ?? 'day'}!`;
  };

  const handleRefresh = useCallback(() => {
    setIsRefreshing(true);
    // Force update dates on manual refresh
    updateDates();
  }, [updateDates]);

  return (
    <ScreenWrapper>
      <ScreenWrapper.Body withoutPadding>
        <ScrollView
          contentContainerStyle={scrollContainerStyle}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={isRefreshing}
              onRefresh={handleRefresh}
            />
          }>
          <Title title={getTitle()} subtitle={en.select_date_subtitle} />

          <View style={[container, gap16, paddingTop6]}>
            <DateCard date={yesterday} onItemPress={handleDateCardPress} />
            <DateCard date={today} onItemPress={handleDateCardPress} />
          </View>
        </ScrollView>
      </ScreenWrapper.Body>
    </ScreenWrapper>
  );
};

export default SelectDateScreen;
