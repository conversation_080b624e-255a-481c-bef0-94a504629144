import { useQuery, useObject } from '@realm/react';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { StyleSheet, ScrollView, TextStyle, ViewStyle } from 'react-native';
import FilledButton from '~/components/buttons/FilledButton';
import { ParcelSchema } from '~/db/realm/schemas/parcel.schema';
import en from '~/localization/en';
import { DriverActions } from '~/services/sync/driverActions';
import { buttonFullWidth, primarySolidButton } from '~/styles/buttons';
import colors from '~/styles/colors';
import { buttonText, h3 } from '~/styles/text';
import { center, row } from '~/styles/views';
import {
  Stop,
  StopType,
  StopWithRelations,
  TaskListItem,
} from '~/types/stops.types';
import { Parcel, ParcelType } from '~/types/parcel.types';
import ScreenWrapper from '~/screens/ScreenWrapper';
import { paddingTop16 } from '~/styles/spacing';
import {
  ConfirmationSubType,
  ConfirmationType,
} from '~/types/confirmations.types';
import { BarCodeFilled, LocationPinOutlined, Notes } from '~/components/icons';
import { ImageStore, ImageTitleType } from '~/services/ImageStoreService';
import { filterValidImageUris, ImageTitle } from '~/utils/images';
import { useImages } from '~/hooks/useImages';
import { ProtocolResult, ProtocolService } from '~/services/protocol';
import { ProtocolType } from '~/types/protocol.types';
import { RootStackParamList } from '~/navigation/ParcelStack';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import PhotoCaptureWrapper from '~/components/camera/PhotoCaptureWrapper';
import CardWrapper from '~/components/cards/CardWrapper';
import ParcelsExpected from '~/components/parcels/ParcelsExpected';
import Title from '~/components/text/Title';
import { getAddressString, StopTypeInfo } from '~/utils/stops';
import ProofOfService from '~/components/ProofOfService';
import { useFocusEffect } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useTaskStore } from '~/store/useCompletedTasksStore';
import { updateEntity } from '~/db/realm/operations';
import { StopSchema } from '~/db/realm/schemas/stop.schema';
import { addToRequestQueue } from '~/services/sync/syncUp';
import { getParcelTypes } from '~/db/realm/operations/parcel.operations';
import { checkProtocolVerificationExistsForStop } from '~/db/realm/operations/protocol-verification.operations';
import StopInfoItemCard from '~/components/cards/StopInfoItemCard';
import { checkCameraPermissionsAndOpenIt } from '~/components/camera/CheckPermissionAndOpenCamera';
import { useDestinationsData } from '~/utils/parcel';
import { DailyScheduleSchema } from '~/db/realm/schemas/daily-schedule.schema';
import { DailySchedule } from '~/types/daily-schedule.types';
import CustomModal from '~/components/modals/CustomModal';

export interface StopOverviewScreenProps {
  route: {
    params: {
      stop: StopWithRelations;
      stopList: StopWithRelations[];
      task: TaskListItem;
      taskList: TaskListItem[];
      numberOfServices: number;
    };
  };
  navigation: NativeStackNavigationProp<RootStackParamList>;
}

type BarcodeErrorType = null | 'not_matched' | 'not_found';

interface BarcodeScanResult {
  matchedParcel: Parcel | undefined;
  errorType: BarcodeErrorType;
}

interface ParcelTypeData {
  Id: string;
  Name: string;
}

const StopOverviewScreen: React.FC<StopOverviewScreenProps> = ({
  route,
  navigation,
}) => {
  const locallyCompletedTasksRef = useRef<Set<string>>(new Set());

  const { completedTasks, addCompletedTask } = useTaskStore();

  const { stop, stopList, task, numberOfServices, taskList } = route.params;
  const [imageRefreshKey, setImageRefreshKey] = useState(Date.now());
  const stopDataFromDB = useObject<Stop>(StopSchema.name, stop?.Id);

  const dailySchedule = useObject<DailySchedule>(
    DailyScheduleSchema.name,
    stop?.Daily_Schedule__c,
  );

  const barcodeScanOverride = dailySchedule?.Barcode_Scan_Override__c === true;
  const [stopTypeInfo, setStopTypeInfo] = useState<StopTypeInfo | null>(null);
  const [proofOfServiceComment, setProofOfServiceComment] = useState<string>(
    stopDataFromDB?.Proof_of_Service__c ?? '',
  );
  const [serviceComment, setServiceComment] = useState<string>(
    stopDataFromDB?.Service_Comments__c ?? '',
  );
  const [parcelTypes, setParcelTypes] = useState<ParcelType[]>([]);

  const [isBarcodeModalVisible, setIsBarcodeModalVisible] = useState(false);

  const destinationsData = useDestinationsData(stopList, stop?.Id);

  const [barcodeErrorType, setBarcodeErrorType] = useState<
    null | 'not_matched' | 'not_found'
  >(null);

  const signatureURIRef = useRef<string>('');
  const signatureCommentsRef = useRef<string>('');

  const pickupParcels = useQuery<Parcel>({
    type: ParcelSchema.name,
    query: parcels => parcels.filtered('Pickup__c == $0', stop?.Id),
  });

  const deliveryParcels = useQuery<Parcel>({
    type: ParcelSchema.name,
    query: parcels => parcels.filtered('Delivery__c == $0', stop?.Id),
  });

  const { data: stopImages } = useImages({
    titleTypes: Object.values(ImageTitle).filter(
      title => title !== ImageTitle.dropOffSignature,
    ),
    stopId: stop?.Id,
    routeSummaryId: stop?.Summary__c,
    refreshKey: imageRefreshKey,
  });

  const allStopImageUris = useMemo(() => {
    if (!stopImages) return [];

    const allUris: string[] = [];

    Object.entries(stopImages).forEach(([key, value]) => {
      if (key === ImageTitle.dropOffSignature) return;

      const imagesArray = Array.isArray(value) ? value : value ? [value] : [];
      allUris.push(...filterValidImageUris(imagesArray));
    });

    return allUris;
  }, [stopImages]);

  useFocusEffect(
    React.useCallback(() => {
      const TIMER_START_KEY = `timerStart_${stop?.Id}`;
      const TIMER_COMPLETED_KEY = `timerCompleted_${stop?.Id}`;
      AsyncStorage.removeItem(TIMER_START_KEY);
      AsyncStorage.removeItem(TIMER_COMPLETED_KEY);
    }, [stop?.Id]),
  );

  useEffect(() => {
    locallyCompletedTasksRef.current = new Set(completedTasks);

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    const stopTypeInfoObj = StopTypeInfo[task.type as StopType];

    setStopTypeInfo(stopTypeInfoObj);
  }, [task.type]);

  useEffect(() => {
    const fetchParcelTypes = async () => {
      try {
        const types = await getParcelTypes();
        setParcelTypes(types);
      } catch (error) {
        console.error('StopOverviewScreen: fetchParcelTypes():', error);
      }
    };

    fetchParcelTypes();
  }, []);

  const checkAndTriggerPrePickupProtocol = async (): Promise<boolean> => {
    try {
      if (task.type !== StopType.Pickup) {
        return false;
      }

      const protocolVerificationExists =
        await checkProtocolVerificationExistsForStop(stop.Id, stop.Summary__c);
      // If protocol verification already exists, don't trigger
      if (protocolVerificationExists) {
        console.info('Protocol verification already exists for this stop');
        return false;
      }

      const result = await ProtocolService.checkProtocols(
        stop,
        ProtocolType.PRE_PICKUP,
      );

      if (result?.protocolData) {
        // Check if pre-pickup protocol is applicable
        launchProtocolFlow(result?.protocolData);
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error checking pre-pickup protocol:', error);
      return false;
    }
  };

  const launchProtocolFlow = (protocolData: ProtocolResult) => {
    const { protocols, scenario } = protocolData;
    navigation.navigate('ProtocolEngineStack', {
      protocols,
      scenario,
      onSuccessCallback: () => {
        navigateToAddParcelFlow();
      },
    });
  };

  const countPickupParcelsQuantity = () => {
    return pickupParcels.reduce(
      (acc, parcel) => acc + (parcel.Quantity__c ?? 0),
      0,
    );
  };

  const markStopCompleted = async ({
    confirmationType,
    confirmationSubType,
  }: {
    confirmationType: ConfirmationType;
    confirmationSubType: ConfirmationSubType;
  }) => {
    let stopData: Record<string, any> = {
      Service_Comments__c: serviceComment ?? '',
      Proof_of_Service__c: proofOfServiceComment ?? '',
    };

    const pickupParcelsCount = countPickupParcelsQuantity();

    if (pickupParcelsCount > 0) {
      stopData.Pieces__c = pickupParcelsCount;
    }

    await DriverActions.markStopCompletion(stop?.Id, stopData);

    navigation.navigate('ConfirmationScreen', {
      type: confirmationType,
      subType: confirmationSubType,
      stop: stop,
    });
  };

  const handleAddToQueue = async () => {
    if (!proofOfServiceComment && !serviceComment) return;

    try {
      await addToRequestQueue({
        entityId: stop?.Id,
        entityName: StopSchema.name,
        updates: {
          Service_Comments__c: serviceComment ?? '',
          Proof_of_Service__c: proofOfServiceComment ?? '',
        },
      });
    } catch (error) {
      console.error('StopOverviewScreen: handleAddToQueue()', error);
    }
  };

  const checkRequiresSignature = (): boolean =>
    task.type === StopType.Delivery &&
    deliveryParcels.some(parcel => parcel.Requires_Signature__c);

  const isPickupOnlyService = () => {
    return taskList.every(taskItem => taskItem.type === StopType.Pickup);
  };

  const isDeliveryOnlyService = () => {
    return taskList.every(taskItem => taskItem.type === StopType.Delivery);
  };

  const isAllTaskCompleted = () => {
    // Filter completed tasks to only include tasks from the current stop's taskList
    const currentStopCompletedTasks = Array.from(
      locallyCompletedTasksRef.current,
    ).filter(taskId => taskList.some(_task => _task.id === taskId));

    return currentStopCompletedTasks.length === numberOfServices;
  };

  const launchSignatureFlow = () => {
    navigation.navigate('CollectSignature', {
      title: stop?.Name ?? '',
      signatureURI: signatureURIRef.current,
      comments: signatureCommentsRef.current,
      onValueChange: (signature: string, comments: string) => {
        signatureURIRef.current = signature;
        signatureCommentsRef.current = comments;
      },
      onCompleteDropOff: (signature: string, comments: string) => {
        signatureURIRef.current = signature;
        signatureCommentsRef.current = comments;
        if (signature) {
          const cleanedSignature = signature.replace(
            /^data:image\/\w+;base64,/,
            '',
          );
          addImage(ImageTitle.dropOffSignature, cleanedSignature);
        }
        markTaskCompleted();
      },
    });
  };

  const addTaskToCompletedOnce = (taskId: string) => {
    // Only add if not already completed
    if (!locallyCompletedTasksRef.current.has(taskId)) {
      locallyCompletedTasksRef.current.add(taskId);
      addCompletedTask(taskId);
    }
  };

  const completeStop = async () => {
    await markStopCompleted({
      confirmationType: ConfirmationType.Stop,
      confirmationSubType: ConfirmationSubType.StopCompleted,
    });
  };

  const markTaskCompleted = async () => {
    addTaskToCompletedOnce(task.id);

    if (!isAllTaskCompleted()) {
      const state = navigation.getState();
      const paramsOfSelectServiceScreen = state?.routes.find(
        ({ name }) => name === 'SelectServiceScreen',
      )?.params;

      navigation.popTo('SelectServiceScreen', {
        ...paramsOfSelectServiceScreen,
      });
      return;
    }
    // All tasks completed
    const hasPickupTask = taskList.some(
      taskItem => taskItem.type === StopType.Pickup,
    );

    const isEmptyPickupWithPickupTask =
      pickupParcels.length === 0 && hasPickupTask;

    if (isEmptyPickupWithPickupTask) {
      try {
        handleAddToQueue();

        const result = await ProtocolService.checkProtocols(
          stop,
          ProtocolType.PRE_COMPLETE_STOP,
        );

        if (result.protocolData) {
          launchProtocolFlow(result?.protocolData);
        } else {
          await completeStop();
        }
      } catch (error) {
        console.error('StopOverviewScreen: markTaskCompleted()', error);
      }
      return;
    }

    await completeStop();
  };

  const handleCompleteButtonPress = async () => {
    const isPickupOnly = isPickupOnlyService();

    if (isPickupOnly) {
      await markTaskCompleted();
      return;
    }

    const isDeliveryOnly = isDeliveryOnlyService();
    const isDelivery = task.type === StopType.Delivery;

    if (isDeliveryOnly || isDelivery) {
      const isSignatureRequired = checkRequiresSignature();

      if (isSignatureRequired) {
        launchSignatureFlow();
      } else {
        await markTaskCompleted();
      }
      return;
    }

    await markTaskCompleted();
  };

  const addImage = (titleType: ImageTitleType, base64String: string) => {
    ImageStore.addImage({
      titleType,
      base64String,
      stopId: stop?.Id ?? '',
      routeSummaryId: stop?.Summary__c ?? '',
    });
  };

  const getImagePathOrBarcodeResult = (imageBase64: string) => {
    addImage(ImageTitle.photosTaken, imageBase64);
  };

  const handleImagePOS = (imageBase64: string) => {
    addImage(ImageTitle.proofOfService, imageBase64);
  };

  const handleEditParcel = (parcel: Parcel) => {
    navigation.navigate('ParcelStack', {
      screen: 'EditParcelScreen',
      params: {
        stop: stop,
        stopId: stop?.Id,
        stopList: stopList,
        parcel: parcel,
        parcelTypes: parcelTypes,
        stopType: task.type,
      },
    });
  };

  const navigateToAddParcelFlow = async () => {
    const protocolTriggered = await checkAndTriggerPrePickupProtocol();

    if (protocolTriggered) return;

    navigation.navigate('ParcelStack', {
      stop: stop,
      stopList: stopList,
      parcelTypes: parcelTypes,
      stopType: task.type,
    });
  };

  const saveStopData = async () => {
    const hasServiceCommentChanged =
      serviceComment !== stop?.Service_Comments__c;
    const hasProofOfServiceChanged =
      proofOfServiceComment !== stop?.Proof_of_Service__c;

    if (!hasServiceCommentChanged && !hasProofOfServiceChanged) return null;

    const updates: Record<string, any> = {};
    if (hasServiceCommentChanged) {
      updates.Service_Comments__c = serviceComment;
    }
    if (hasProofOfServiceChanged) {
      updates.Proof_of_Service__c = proofOfServiceComment;
    }

    try {
      await updateEntity({
        entityId: stop?.Id,
        entityName: StopSchema.name,
        updates,
      });
    } catch (error) {
      console.error('StopOverviewScreen: saveStopData()', error);
      return null;
    }
  };

  const isProofOfServiceMissing = () => {
    return !stopImages.proofOfService || proofOfServiceComment?.trim() === '';
  };

  const isPickup = task.type === StopType.Pickup;
  const isDelivery = task.type === StopType.Delivery;

  const relevantParcels = isPickup ? pickupParcels : deliveryParcels;

  const parcelsWithUnscannedReference = relevantParcels.filter(
    parcel =>
      parcel.Work_Order__c &&
      parcel.Reference__c !== null &&
      ((isPickup && !parcel.Pickup_Scan_Time__c) ||
        (isDelivery && !parcel.Delivery_Scan_Time__c)),
  );

  const isAllParcelsScanned = parcelsWithUnscannedReference.length === 0;
  const isProofOfServiceAvailable = !isProofOfServiceMissing();
  const isCompleteEnabled = isAllParcelsScanned && isProofOfServiceAvailable;

  const getButtonTitle = () => {
    if (isCompleteEnabled) {
      return en.confirm;
    }
    const hasWorkOrderParcel = relevantParcels.some(
      parcel => parcel.Work_Order__c,
    );

    if (hasWorkOrderParcel) {
      if (isPickup) {
        return en.complete_pickup_tasks_to_continue;
      } else if (isDelivery) {
        return en.complete_delivery_tasks_to_continue;
      }
    }
    if (isProofOfServiceMissing()) {
      return en.proof_of_service_missing;
    }
    return `Complete ${task.type.toLowerCase()}`;
  };

  const onInputBlur = () => {
    saveStopData();
  };

  const getAllParcels = useMemo(() => {
    return [...pickupParcels, ...deliveryParcels];
  }, [pickupParcels, deliveryParcels]);

  const getParcelTypeData = (parcel: Parcel): ParcelTypeData => ({
    Id: parcel.Parcel_Type_Definition__c ?? '',
    Name: parcel.Parcel_Type_Name__c ?? '',
  });

  const getInitialParcelData = (parcel: Parcel) => ({
    type: getParcelTypeData(parcel),
    quantity: parcel.Quantity__c,
    quantityExpected: parcel.Quantity_Expected__c ?? 1,
    dropoffDestination: parcel.Delivery__c,
    barcodeScannerResult: parcel.Reference__c,
  });

  const getWorkOrderParcelScreenParams = (parcel: Parcel) => ({
    stop,
    stopList,
    type: task.type,
    parcel,
    destinationsData,
  });

  const navigateToWorkOrderParcelScreen = (
    parcel: Parcel,
    additionalParams: Record<string, any> = {},
  ) => {
    setTimeout(() => {
      navigation.navigate('ParcelStack', {
        stop,
        stopList,
        parcelTypes,
        stopType: task.type,
        initialParcelData: getInitialParcelData(parcel),
        screen: 'WorkOrderParcelScreen',
        params: {
          ...getWorkOrderParcelScreenParams(parcel),
          ...additionalParams,
        },
      });
    }, 100);
  };

  const validateBarcodeScan = (
    scannedCode: string,
    targetedParcelId?: string,
  ): BarcodeScanResult => {
    if (!scannedCode?.trim()) {
      return { matchedParcel: undefined, errorType: null };
    }

    const expectedRefMap = new Map(
      getAllParcels.map(p => [p.Id, p.Reference__c]),
    );

    if (targetedParcelId) {
      return validateTargetedBarcodeScan(
        scannedCode,
        targetedParcelId,
        expectedRefMap,
      );
    } else {
      return validateUntargetedBarcodeScan(scannedCode);
    }
  };

  const validateTargetedBarcodeScan = (
    scannedCode: string,
    targetedParcelId: string,
    expectedRefMap: Map<string, string | undefined>,
  ): BarcodeScanResult => {
    const targetRef = expectedRefMap.get(targetedParcelId);
    const matchedParcel = getAllParcels.find(
      parcel => parcel.Id === targetedParcelId,
    );

    if (scannedCode === targetRef) {
      return { matchedParcel, errorType: null };
    }

    // Check if scannedCode matches any other parcel
    const otherMatch = getAllParcels.find(
      parcel => parcel.Reference__c === scannedCode,
    );

    if (otherMatch) {
      return { matchedParcel: undefined, errorType: 'not_matched' };
    } else {
      return { matchedParcel: undefined, errorType: 'not_found' };
    }
  };

  const validateUntargetedBarcodeScan = (
    scannedCode: string,
  ): BarcodeScanResult => {
    const matchedParcel = getAllParcels.find(
      parcel => parcel.Reference__c === scannedCode,
    );

    if (!matchedParcel) {
      return { matchedParcel: undefined, errorType: 'not_found' };
    }

    return { matchedParcel, errorType: null };
  };

  const handleBarcodeScanned = (
    scannedCode: string,
    targetedParcelId?: string,
  ) => {
    if (!scannedCode?.trim()) return;

    const { matchedParcel, errorType } = validateBarcodeScan(
      scannedCode,
      targetedParcelId,
    );

    setBarcodeErrorType(errorType);

    if (matchedParcel) {
      navigateToWorkOrderParcelScreen(matchedParcel);
    }
  };

  const handleBarcodeConfirm = (barcode: string) => {
    setIsBarcodeModalVisible(false);

    if (!barcode) {
      return;
    }
    const targetedParcel = getAllParcels.find(
      parcel => parcel.Reference__c === barcode,
    );

    if (targetedParcel) {
      handleBarcodeScanned(barcode, targetedParcel.Id);
      return;
    }
    setBarcodeErrorType('not_found');
  };

  const handleManualEntry = (targetedParcelId?: string) => {
    if (!targetedParcelId) {
      setIsBarcodeModalVisible(true);
      return;
    }

    const matchedParcel = findParcelForManualEntry(targetedParcelId);

    if (matchedParcel) {
      navigateToWorkOrderParcelScreen(matchedParcel, {
        isBarcodeBypass: false,
        isManualEntry: true,
      });
    } else {
      console.warn('No matching parcel found for manual entry');
    }
  };

  const handleBarcodeBypass = (
    targetedParcelId?: string,
    barcodeBypassReason?: string,
  ) => {
    if (!barcodeScanOverride) {
      console.warn('Barcode bypass not allowed - barcodeScanOverride is false');
      return;
    }

    const matchedParcel = findParcelForManualEntry(targetedParcelId);

    if (matchedParcel) {
      navigateToWorkOrderParcelScreen(matchedParcel, {
        isBarcodeBypass: true,
        barcodeBypassReason: barcodeBypassReason,
      });
    } else {
      console.warn('No matching parcel found for bypass');
    }
  };

  const findParcelForManualEntry = (
    targetedParcelId?: string,
  ): Parcel | undefined => {
    if (targetedParcelId) {
      return getAllParcels.find(parcel => parcel.Id === targetedParcelId);
    }

    return getAllParcels.find(
      parcel =>
        parcel.Reference__c != null &&
        ((task.type === StopType.Pickup &&
          parcel.Pickup_Scan_Time__c === null) ||
          (task.type === StopType.Delivery &&
            parcel.Delivery_Scan_Time__c === null)),
    );
  };
  const isScanRequired = (targetedParcelId?: string) => {
    const targetParcel = getAllParcels.find(p => p.Id === targetedParcelId);

    if (!targetParcel) {
      return { isBarcodeRequired: true, matchedParcel: targetParcel };
    }

    const isBarcodeRequired =
      targetParcel.Reference__c != null &&
      targetParcel.Barcode_Required__c === true;

    return { isBarcodeRequired, matchedParcel: targetParcel };
  };

  const openBarcodeScanner = (targetedParcelId?: string) => {
    const { isBarcodeRequired, matchedParcel } =
      isScanRequired(targetedParcelId);

    if (!isBarcodeRequired && matchedParcel) {
      navigateToWorkOrderParcelScreen(matchedParcel, {
        isManualEntry: false,
      });
      return;
    }

    checkCameraPermissionsAndOpenIt(
      navigation,
      scannedValue => handleBarcodeScanned(scannedValue, targetedParcelId),
      {
        title: en.scan_barcode_title,
        infoMessage: en.scan_barcode_info,
        isBarCodeScanner: true,
        MessageIconComponent: BarCodeFilled,
        barcodeScanOverride,
        onEnterManually: () => handleManualEntry(targetedParcelId),
        onBypassBarcode: () => {
          navigation.navigate('ParcelStack', {
            screen: 'BypassBarcode',
            params: {
              stop,
              onBypassSubmitted: (
                parcelId?: string,
                barcodeBypassReason?: string,
              ) => {
                handleBarcodeBypass(parcelId, barcodeBypassReason);
              },
              targetedParcelId,
            },
          });
        },
      },
    );
  };

  return (
    <ScreenWrapper isKeyboardSensitive>
      <ScreenWrapper.Body withoutPadding>
        <ScrollView contentContainerStyle={styles.scrollViewContainer}>
          <Title title={stopTypeInfo?.title ?? ''} icon={stopTypeInfo?.icon} />
          <StopInfoItemCard
            icon={<LocationPinOutlined color={colors.grey900} />}
            title={stop?.Name}
            value={getAddressString(stop)}
          />

          <StopInfoItemCard
            icon={<Notes color={colors.grey900} />}
            title={en.notes}
            value={stop?.Notes__c ?? en.no_notes_available}
          />

          <PhotoCaptureWrapper
            multiple={true}
            imageData={allStopImageUris}
            onCapture={getImagePathOrBarcodeResult}
          />

          {task.type !== StopType.Service && (
            <CardWrapper>
              <ParcelsExpected
                stopType={task.type}
                pickupParcels={Array.from(pickupParcels)}
                deliveryParcels={Array.from(deliveryParcels)}
                navigateToAddParcelFlow={navigateToAddParcelFlow}
                handleEditParcel={handleEditParcel}
                isLastStop={false}
                onParcelDeleted={() => setImageRefreshKey(Date.now())}
                openBarcodeScanner={openBarcodeScanner}
                barcodeErrorType={barcodeErrorType}
                onDismissBarcodeModal={() => setBarcodeErrorType(null)}
              />
            </CardWrapper>
          )}

          <ProofOfService
            imageData={stopImages.proofOfService?.uri}
            onCaptureClick={handleImagePOS}
            onCommentChange={value => {
              setServiceComment(value);
            }}
            commentValue={serviceComment}
            onProofOfServiceChange={value => {
              setProofOfServiceComment(value);
            }}
            proofOfServiceValue={proofOfServiceComment}
            onBlur={onInputBlur}
          />
        </ScrollView>
        <CustomModal
          isVisible={isBarcodeModalVisible}
          onClose={() => setIsBarcodeModalVisible(false)}
          headerText={en.enter_barcode_manually}
          descriptionText={en.please_enter_the_barcode_number}
          okButtonText={en.confirm}
          cancelButtonText={en.cancel}
          onOkPress={handleBarcodeConfirm}
          onCancelPress={() => setIsBarcodeModalVisible(false)}
          showTextInput={true}
          testID="barcode-input-modal"
        />
      </ScreenWrapper.Body>
      <ScreenWrapper.Bottom>
        <FilledButton
          id={'StopOverviewScreen.FilledButton.complete'}
          title={getButtonTitle()}
          style={[buttonFullWidth, primarySolidButton]}
          isDisabled={!isCompleteEnabled}
          color="primary"
          onClick={handleCompleteButtonPress}
          containerStyle={null}
        />
      </ScreenWrapper.Bottom>
    </ScreenWrapper>
  );
};

const styles = StyleSheet.create({
  scrollViewContainer: {
    padding: paddingTop16.paddingTop,
  },
  sectionTitle: {
    ...h3,
    color: colors.grey900,
    fontSize: 20,
    marginTop: 8,
  } as TextStyle,
  row: {
    ...(row as ViewStyle),
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  redTextButton: {
    ...(buttonText as TextStyle),
    color: colors.red400,
  } as TextStyle,
  parcelPlaceholder: {
    ...(center as ViewStyle),
    height: 120,
    backgroundColor: colors.backgroundLight,
    borderRadius: 8,
  },
  infoView: {
    flexDirection: 'row',
    flex: 1,
    gap: 4,
  },
});

export default StopOverviewScreen;
