import React, { useState } from 'react';
import {
  View,
  Text,
  FlatList,
  RefreshControl,
  StyleSheet,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { useIsFocused, useNavigation } from '@react-navigation/native';
import { center, container } from '~/styles/views';
import { pendingInfoText } from '~/styles/text';
import { paddingTop32 } from '~/styles/spacing';
import en from '~/localization/en';
import { useBottomTabBarHeight } from '@react-navigation/bottom-tabs';
import ScreenWrapper from '~/screens/ScreenWrapper';
import { getStopStepperPillColors } from '~/utils/stops';
import StopCard from '~/components/cards/StopCard';
import { StopWithRelations } from '~/types/stops.types';

const CompletedScheduleList = ({
  date,
  tasks,
  onRefresh,
  headerComponent,
}: {
  date: string;
  tasks: any[];
  onRefresh: () => void;
  headerComponent?: React.ReactNode;
}) => {
  const navigation = useNavigation();
  const isFocused = useIsFocused();
  const bottomBarHeight = useBottomTabBarHeight();

  const [refreshing, setRefreshing] = useState(false);

  const handleRefresh = async () => {
    try {
      setRefreshing(true);
      onRefresh();
    } finally {
      setRefreshing(false);
    }
  };

  const renderEmptyList = () => {
    return (
      <View style={[container, center, paddingTop32] as ViewStyle}>
        <Text style={pendingInfoText as TextStyle}>
          {en.no_completed_tasks}
        </Text>
      </View>
    );
  };

  const renderItemSeparator = () => {
    return <View style={styles.itemSeparator} />;
  };

  const routeContainerStyle = {
    ...styles.routeContainer,
    paddingBottom: bottomBarHeight + styles.routeContainer.padding,
    paddingTop: headerComponent && 0,
  };

  const renderRefreshControl = () => {
    if (isFocused) {
      return (
        <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
      );
    }
  };

  const handleStopSelection = (
    stop: StopWithRelations,
    isCurrent: boolean = false,
  ) => {
    navigation.navigate('StopDetailScreen', {
      date,
      stopId: stop.Id,
      isCurrent,
      title: stop.Name,
    });
  };

  return (
    <ScreenWrapper>
      <FlatList
        data={tasks ?? []}
        keyExtractor={item => item.Id}
        contentContainerStyle={routeContainerStyle}
        renderItem={({ item }) => (
          <StopCard
            stop={item}
            stepperChipColor={getStopStepperPillColors('COMPLETE')}
            current={false}
            onItemPress={() => handleStopSelection(item, false)}
          />
        )}
        ItemSeparatorComponent={renderItemSeparator}
        ListEmptyComponent={renderEmptyList}
        refreshControl={renderRefreshControl()}
        ListHeaderComponent={<>{headerComponent}</>}
      />
    </ScreenWrapper>
  );
};

const styles = StyleSheet.create({
  routeContainer: {
    padding: 16,
  },
  itemSeparator: {
    height: 16,
  },
});

export default CompletedScheduleList;
