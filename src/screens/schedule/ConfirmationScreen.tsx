import React from 'react';
import en from '~/localization/en';
import ScreenWrapper from '~/screens/ScreenWrapper';
import { buttonFullWidth, primarySolidButton } from '~/styles/buttons';
import { useNavigation } from '@react-navigation/native';

import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import FilledButton from '~/components/buttons/FilledButton';
import {
  ConfirmationType,
  ConfirmationSubType,
} from '~/types/confirmations.types';
import { ConfirmationTypeInfo, subLabelMapping } from '~/utils/confirmations';
import colors from '~/styles/colors';
import InfoPanel from '~/components/placeholders/InfoPanel';
import useBackHandler from '~/hooks/useBackHandler';
import { ScheduleStackParamList } from '~/navigation/ScheduleStack';
import { useTaskStore } from '~/store/useCompletedTasksStore';

export type ConfirmationScreenProps = {
  route: {
    params: {
      type: ConfirmationType;
      subType: ConfirmationSubType;
    };
  };
};

const ConfirmationScreen: React.FC<ConfirmationScreenProps> = ({ route }) => {
  const { resetCompletedTasks } = useTaskStore();

  const navigation =
    useNavigation<NativeStackNavigationProp<ScheduleStackParamList>>();

  const { type, subType } = route.params;

  const handleConfirmationNavigation = () => {
    resetCompletedTasks();
    const state = navigation.getState();

    const paramsOfDailyScheduleScreen = state?.routes.find(
      ({ name }) => name === 'DailyScheduleScreen',
    )?.params;

    if (paramsOfDailyScheduleScreen) {
      navigation.popToTop();
      navigation.navigate('DailyScheduleScreen', paramsOfDailyScheduleScreen);
    }
  };

  useBackHandler(() => {
    try {
      handleConfirmationNavigation();
      return true;
    } catch (error) {
      console.error(
        'ConfirmationScreen: useBackHandler(): Error handling back button:',
        error,
      );
      return false;
    }
  });

  return (
    <ScreenWrapper color={colors.white}>
      <ScreenWrapper.Body>
        <InfoPanel
          icon={ConfirmationTypeInfo[type].icon}
          title={ConfirmationTypeInfo[type].title}
          infoText={subLabelMapping[subType]}
        />
      </ScreenWrapper.Body>
      <ScreenWrapper.Bottom>
        <FilledButton
          id={'ConfirmationScreen.FilledButton.continue'}
          title={en.continue}
          style={[buttonFullWidth, primarySolidButton]}
          color="primary"
          onClick={handleConfirmationNavigation}
        />
      </ScreenWrapper.Bottom>
    </ScreenWrapper>
  );
};

export default ConfirmationScreen;
