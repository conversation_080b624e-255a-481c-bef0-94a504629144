import React, { useCallback, useEffect, useMemo, useState } from 'react';
import {
  View,
  FlatList,
  StyleSheet,
  Text,
  TextStyle,
  RefreshControl,
} from 'react-native';
import StopCard from '~/components/timeline/StopTimelineItem';
import en from '~/localization/en';
import colors from '~/styles/colors';
import {
  centerText,
  disabledText,
  fontFamilyBasedOnRunningOS,
  h5,
} from '~/styles/text';
import { RouteSummary, RouteStatus } from '~/types/routes.types';
import { Stop } from '~/types/stops.types';
import ErrorComponent from '~/components/error/ErrorComponent';
import { deviceHeight } from '~/styles/views';
import OutlinedButton from '~/components/buttons/OutlinedButton';
import RouteDetailSkeletonView from '~/components/loaders/RouteDetailSkeletonView';
import { useRouteHeartbeat } from '~/hooks/useRouteHeartbeat';
import StopActionButton from '~/components/buttons/StopActionButton';
import { useQuery } from '@realm/react';
import { StopSchema } from '~/db/realm/schemas/stop.schema';
import ScreenWrapper from '~/screens/ScreenWrapper';
import { ROUTE_STATUS, STOP_STATUS } from '~/utils/constants';
import { useSyncService } from '~/services/sync/syncServiceContext';
import { SYNC_STATUS } from '~/hooks/useSync';

interface RouteDetailScreenProps {
  route: {
    params: {
      route: RouteSummary;
      isCurrent: boolean;
    };
  };
}

const infoTextMapping: Partial<Record<keyof typeof ROUTE_STATUS, string>> = {
  [ROUTE_STATUS.Complete]: en.completedRoute,
  [ROUTE_STATUS.Scheduled]: en.scheduledRoute,
  [ROUTE_STATUS.Cancelled]: en.canceledRoute,
} as const;

const queryFilterForCompletedStops = `Summary__c == $0 AND Status__c == $1`;
const queryFilterForPendingStops = `Summary__c == $0 AND NOT Status__c == $1 AND NOT Status__c == $2`;

const RouteDetailScreen: React.FC<RouteDetailScreenProps> = ({ route }) => {
  const [refreshing, setRefreshing] = useState(false);

  const currentRoute = route.params.route;

  const { status: syncStatus, syncDown } = useSyncService();

  const { handleRouteSelection } = useRouteHeartbeat({ route: currentRoute });

  const completedStops = useQuery<Stop>({
    type: StopSchema.name,
    query: stops =>
      stops
        .filtered(
          queryFilterForCompletedStops,
          currentRoute.Id,
          STOP_STATUS.COMPLETE,
        )
        .sorted('Completed_Time__c'),
  });

  const pendingStops = useQuery<Stop>({
    type: StopSchema.name,
    query: stops =>
      stops
        .filtered(
          queryFilterForPendingStops,
          currentRoute.Id,
          STOP_STATUS.COMPLETE,
          STOP_STATUS.INACTIVE,
        )
        .sorted('Stop_Time_Preferred__c'),
  });

  const stops: Stop[] = useMemo(
    () => Array.from([...completedStops, ...pendingStops]),
    [completedStops, pendingStops],
  );

  const handleRefresh = useCallback(async () => {
    try {
      setRefreshing(true);
      await syncDown();
    } finally {
      setRefreshing(false);
    }
  }, [syncDown]);

  const lastCompletedStopIndex = useMemo(() => {
    let lastIndex = -1;

    for (let i = stops.length - 1; i >= 0; i--) {
      if (stops[i].Status__c === STOP_STATUS.COMPLETE) {
        lastIndex = i;
        break;
      }
    }

    return lastIndex;
  }, [stops]);

  const lastCompletedStop = stops[lastCompletedStopIndex];
  const nextStop = stops[lastCompletedStopIndex + 1];

  useEffect(() => {
    if (stops.length > 0) {
      handleRouteSelection();
    }
  }, [stops, handleRouteSelection]);

  const error = syncStatus === SYNC_STATUS.ERROR ? 'Sync error' : '';

  if (error && stops.length === 0) {
    return <ErrorComponent errorText={error} />;
  }

  if (stops.length === 0) {
    return <ErrorComponent errorText="No stops available" />;
  }

  const ListHeaderComponent = ({
    routeStatus,
  }: {
    routeStatus: RouteStatus;
  }) => {
    const isRouteCompleteOrScheduled =
      routeStatus === ROUTE_STATUS.Complete ||
      routeStatus === ROUTE_STATUS.Scheduled;

    if (!isRouteCompleteOrScheduled) {
      return null;
    }

    return (
      <View>
        <Text style={styles.infoText}>{infoTextMapping[routeStatus]}</Text>
      </View>
    );
  };

  if (refreshing) {
    return <RouteDetailSkeletonView />;
  }

  return (
    <ScreenWrapper color={colors.white}>
      <ScreenWrapper.Body withoutPadding>
        <FlatList
          contentContainerStyle={styles.listContainer}
          data={stops}
          keyExtractor={item => item.Id}
          nestedScrollEnabled
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
          }
          renderItem={({ item, index }) => (
            <StopCard
              stop={item}
              lastCompletedStopIndex={lastCompletedStopIndex}
              itemIndex={index}
              routeStatus={currentRoute?.Status__c}
            />
          )}
          ListHeaderComponent={
            <ListHeaderComponent routeStatus={currentRoute?.Status__c} />
          }
          ListEmptyComponent={() => (
            <View style={styles.emptyViewContainer}>
              <Text style={[h5, disabledText, centerText] as TextStyle}>
                {'No stops available\nPlease try again later'}
              </Text>
            </View>
          )}
        />
      </ScreenWrapper.Body>

      {stops.length > 0 &&
        route.params.isCurrent &&
        currentRoute.Status__c !== 'Complete' &&
        currentRoute.Status__c !== 'Scheduled' &&
        nextStop && (
          <ScreenWrapper.Bottom>
            <StopActionButton
              enableRapidClosureWarning={currentRoute.Rapid_Closure_Warning__c}
              lastCompletedStop={lastCompletedStop}
              nextStop={nextStop}
              stopList={stops}
              currentRoute={currentRoute}
            />
            <OutlinedButton
              title={en.navigateToStop}
              onClick={() => null}
              color={'primary'}
              isDisabled={false}
              isLoading={false}
            />
          </ScreenWrapper.Bottom>
        )}
    </ScreenWrapper>
  );
};

const styles = StyleSheet.create({
  listContainer: {
    paddingHorizontal: 16,
  },
  infoText: {
    fontSize: 24,
    letterSpacing: -0.3,
    marginBottom: 8,
    marginTop: 16,
    fontWeight: '500',
    fontFamily: fontFamilyBasedOnRunningOS,
    color: colors.grey800,
    textAlign: 'left',
    paddingHorizontal: 16,
  },
  emptyViewContainer: {
    height: deviceHeight * 0.8,
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default RouteDetailScreen;
