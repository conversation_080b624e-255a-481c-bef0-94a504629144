import React, { useMemo, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextStyle,
  RefreshControl,
  ViewStyle,
  SectionList,
  SectionListData,
} from 'react-native';
import colors from '~/styles/colors';
import { h3, pendingInfoText } from '~/styles/text';
import { RouteSummary } from '~/types/routes.types';
import en from '~/localization/en';
import { useIsFocused, useNavigation } from '@react-navigation/native';
import { getStepperPillColors } from '~/utils/route';
import { center, container } from '~/styles/views';
import RouteCard from '~/components/cards/RouteCard';
import { useRouteHeartbeat } from '~/hooks/useRouteHeartbeat';
import { paddingTop32 } from '~/styles/spacing';
import ScreenWrapper from '~/screens/ScreenWrapper';
import { useBottomTabBarHeight } from '@react-navigation/bottom-tabs';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

const ActiveRoute = ({
  routes,
  nextRoute,
  onRefresh,
}: {
  routes: RouteSummary[];
  nextRoute?: RouteSummary;
  onRefresh: () => void;
}) => {
  const navigation = useNavigation();
  const isFocused = useIsFocused();
  const bottomBarHeight = useBottomTabBarHeight();
  const { bottom } = useSafeAreaInsets();

  const [refreshing, setRefreshing] = useState(false);

  // send heartbeat location to to backend, if eligible
  useRouteHeartbeat({ route: nextRoute });

  const handleRefresh = async () => {
    try {
      setRefreshing(true);
      onRefresh();
    } finally {
      setRefreshing(false);
    }
  };

  const renderSectionHeader = ({
    section,
  }: {
    section: SectionListData<RouteSummary>;
  }) => {
    return <Text style={styles.sectionHeaderText}>{section.title}</Text>;
  };

  const renderEmptyList = () => {
    if (nextRoute) return null;

    return (
      <View style={[container, center as ViewStyle, paddingTop32]}>
        <Text style={pendingInfoText as TextStyle}>{en.no_active_routes}</Text>
      </View>
    );
  };

  const handleRouteSelection = (
    route: RouteSummary,
    isCurrent: boolean = false,
  ) => {
    navigation.navigate('RouteDetailScreen', { route, isCurrent });
  };

  const renderRouteCard = ({ item }: { item: RouteSummary }) => {
    return (
      <RouteCard
        current={item.Id === nextRoute?.Id}
        route={item}
        stepperChipColor={getStepperPillColors(item.Status__c)}
        onItemPress={() =>
          handleRouteSelection(item, item.Id === nextRoute?.Id)
        }
      />
    );
  };

  const renderItemSeparator = () => {
    return <View style={styles.itemSeparator} />;
  };

  const routeContainerStyle = {
    ...styles.routeContainer,
    paddingBottom:
      bottomBarHeight +
      (bottom || styles.routeContainer.paddingBottom) +
      styles.routeContainer.paddingBottom,
  };

  const sections = useMemo(() => {
    return [
      ...(nextRoute ? [{ title: en.current, data: [nextRoute] }] : []),
      ...(routes.length ? [{ title: en.upcoming, data: routes }] : []),
    ];
  }, [nextRoute, routes]);

  const renderRefreshControl = () => {
    if (isFocused) {
      return (
        <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
      );
    }
  };

  return (
    <ScreenWrapper>
      <SectionList
        stickySectionHeadersEnabled={false}
        sections={sections}
        contentContainerStyle={routeContainerStyle}
        keyExtractor={item => item.Id}
        nestedScrollEnabled
        renderItem={renderRouteCard}
        renderSectionHeader={renderSectionHeader}
        ListEmptyComponent={renderEmptyList}
        ItemSeparatorComponent={renderItemSeparator}
        refreshControl={renderRefreshControl()}
      />
    </ScreenWrapper>
  );
};

const styles = StyleSheet.create({
  routeContainer: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  sectionHeaderText: {
    ...h3,
    fontSize: 20,
    color: colors.textBlack,
    marginTop: 24,
    marginBottom: 16,
  } as TextStyle,
  itemSeparator: {
    height: 16,
  } as ViewStyle,
});

export default ActiveRoute;
