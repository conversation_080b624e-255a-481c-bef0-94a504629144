import React, { useState } from 'react';
import {
  View,
  Text,
  FlatList,
  RefreshControl,
  StyleSheet,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { RouteSummary } from '~/types/routes.types';
import RouteCard from '~/components/cards/RouteCard';
import { useIsFocused, useNavigation } from '@react-navigation/native';
import { center, container } from '~/styles/views';
import { pendingInfoText } from '~/styles/text';
import { paddingTop32 } from '~/styles/spacing';
import en from '~/localization/en';
import { getStepperPillColors } from '~/utils/route';
import { useBottomTabBarHeight } from '@react-navigation/bottom-tabs';
import ScreenWrapper from '~/screens/ScreenWrapper';

const CompletedRoute = ({
  routes,
  onRefresh,
}: {
  routes: RouteSummary[];
  onRefresh: () => void;
}) => {
  const navigation = useNavigation();
  const isFocused = useIsFocused();
  const bottomBarHeight = useBottomTabBarHeight();

  const [refreshing, setRefreshing] = useState(false);

  const handleRefresh = async () => {
    try {
      setRefreshing(true);
      onRefresh();
    } finally {
      setRefreshing(false);
    }
  };

  const renderEmptyList = () => {
    return (
      <View style={[container, center, paddingTop32] as ViewStyle}>
        <Text style={pendingInfoText as TextStyle}>
          {en.no_completed_routes}
        </Text>
      </View>
    );
  };

  const handleRouteSelection = (route: RouteSummary) => {
    navigation.navigate('RouteDetailScreen', { route });
  };

  const renderItemSeparator = () => {
    return <View style={styles.itemSeparator} />;
  };

  const routeContainerStyle = {
    ...styles.routeContainer,
    paddingBottom: bottomBarHeight + styles.routeContainer.padding,
  };

  const renderRefreshControl = () => {
    if (isFocused) {
      return (
        <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
      );
    }
  };

  return (
    <ScreenWrapper>
      <FlatList
        data={routes ?? []}
        keyExtractor={item => item.Id}
        contentContainerStyle={routeContainerStyle}
        renderItem={({ item }) => (
          <RouteCard
            route={item}
            stepperChipColor={getStepperPillColors(item.Status__c)}
            onItemPress={handleRouteSelection}
          />
        )}
        ItemSeparatorComponent={renderItemSeparator}
        ListEmptyComponent={renderEmptyList}
        refreshControl={renderRefreshControl()}
      />
    </ScreenWrapper>
  );
};

const styles = StyleSheet.create({
  routeContainer: {
    padding: 16,
  },
  itemSeparator: {
    height: 16,
  },
});

export default CompletedRoute;
