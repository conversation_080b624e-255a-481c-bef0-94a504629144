import React, { useMemo } from 'react';
import { createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs';
import ActiveRoute from '~/screens/route/ActiveRouteScreen';
import CompletedRoute from '~/screens/route/CompletedRouteScreen';
import colors from '~/styles/colors';
import { RouteSummary } from '~/types/routes.types';
import { StyleSheet, TextStyle } from 'react-native';
import en from '~/localization/en';
import ErrorComponent from '~/components/error/ErrorComponent';
import { buttonText } from '~/styles/text';
import { initLocationService } from '~/services/location/LocationService';
import { useSyncLocation } from '~/hooks/dataSync/useSyncLocation';
import { useQuery } from '@realm/react';
import { RouteSummarySchema } from '~/db/realm/schemas/route-summary.schema';
import { ROUTE_STATUS } from '~/utils/constants';
import { useSyncService } from '~/services/sync/syncServiceContext';
import { SYNC_STATUS } from '~/hooks/useSync';
import ScreenWrapper from '~/screens/ScreenWrapper';
import { usePermissionHandler } from '~/hooks/usePermissionHandler';
import { PermissionType } from '~/types/permission.types';

const Tab = createMaterialTopTabNavigator();

interface RouteList {
  activeRouteList: RouteSummary[];
  completedRouteList: RouteSummary[];
}

interface Routes {
  activeRoutes: RouteSummary[];
  completedRoutes: RouteSummary[];
  nextRoute: RouteSummary | undefined;
}

const RouteScreen = () => {
  useSyncLocation();

  usePermissionHandler({
    permissionTypes: [PermissionType.LOCATION, PermissionType.MOTION],
    successCallback: initLocationService,
  });

  const { status, syncDown } = useSyncService();

  const error = status === SYNC_STATUS.ERROR ? 'Sync error' : '';

  const routesWithPlannedStartTime = useQuery<RouteSummary>({
    type: RouteSummarySchema.name,
    query: routeRecords =>
      routeRecords.filtered('Planned_Start__c != null').sorted([
        ['Planned_Start__c', false],
        ['Name', true],
      ]),
  });

  const routesWithoutPlannedStartTime = useQuery<RouteSummary>({
    type: RouteSummarySchema.name,
    query: routeRecords => routeRecords.filtered('Planned_Start__c == null'),
  });

  const sortedRoutes = useMemo(
    () => [...routesWithPlannedStartTime, ...routesWithoutPlannedStartTime],
    [routesWithPlannedStartTime, routesWithoutPlannedStartTime],
  );

  const { activeRoutes, completedRoutes, nextRoute } = useMemo<Routes>(() => {
    if (!sortedRoutes || sortedRoutes.length === 0) {
      return { activeRoutes: [], completedRoutes: [], nextRoute: undefined };
    }

    const { activeRouteList, completedRouteList } =
      sortedRoutes.reduce<RouteList>(
        (result: RouteList, route: RouteSummary) => {
          const routeCopy = { ...route };

          if (
            route?.Status__c?.includes(ROUTE_STATUS.Complete) ||
            route?.Status__c.includes(ROUTE_STATUS.Cancelled)
          ) {
            result.completedRouteList.push(routeCopy);
          } else {
            result.activeRouteList.push(routeCopy);
          }

          return result;
        },
        { activeRouteList: [], completedRouteList: [] },
      );

    const currentRouteData = activeRouteList.shift();

    return {
      activeRoutes: activeRouteList,
      completedRoutes: completedRouteList,
      nextRoute: currentRouteData,
    };
  }, [sortedRoutes]);

  return (
    <ScreenWrapper>
      {error && sortedRoutes === undefined ? (
        <ErrorComponent errorText={error} />
      ) : (
        <Tab.Navigator
          screenOptions={{
            tabBarAllowFontScaling: true,
            tabBarActiveTintColor: colors.darkBlue500,
            tabBarInactiveTintColor: colors.grey900,
            swipeEnabled: false,
            tabBarLabelStyle: styles.tabBarLabelStyle,
            tabBarIndicatorStyle: styles.tabBarIndicatorStyle,
            tabBarStyle: styles.tabBarStyle,
          }}>
          <Tab.Screen name={en.active}>
            {() => (
              <ActiveRoute
                routes={activeRoutes}
                nextRoute={nextRoute}
                onRefresh={syncDown}
              />
            )}
          </Tab.Screen>
          <Tab.Screen name={en.completed}>
            {() => (
              <CompletedRoute routes={completedRoutes} onRefresh={syncDown} />
            )}
          </Tab.Screen>
        </Tab.Navigator>
      )}
    </ScreenWrapper>
  );
};

const styles = StyleSheet.create({
  errorText: {
    color: 'red',
    fontSize: 16,
    marginBottom: 10,
  },
  tabBarLabelStyle: {
    ...buttonText,
    textTransform: 'none',
  } as TextStyle,
  tabBarIndicatorStyle: {
    backgroundColor: colors.darkBlue500,
    height: 3,
  },
  tabBarStyle: {
    borderBottomWidth: 1,
    borderBottomColor: colors.lightGray, // Inactive underline color
    elevation: 0,
    backgroundColor: colors.backgroundLight,
  },
});

export default RouteScreen;
