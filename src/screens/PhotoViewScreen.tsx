import React from 'react';
import { Image, ImageSourcePropType, ImageStyle } from 'react-native';
import colors from '~/styles/colors';
import ScreenWrapper from '~/screens/ScreenWrapper';
import { image } from '~/styles/views';

/**
 * A Photo View Screen is a component that shows up a Photo.
 * 
 * @component
 * @param {string} props.source - This is the header title text of the camera screen.
 * @param {string} props.title - Title that will be displayed on the app bar.
 * @param {function} props.headerInfoPress - Info button on the right side of the app bar.

 * @example
 * // Example usage of PhotoViewScreen
 *  navigation.navigate('PhotoViewScreen', {
 *    source: capturedImagePath,
 *    title: 'Title of the app bar'
 *    headerInfoPress: 'The callback when user presses on the header info icon on the right side of the app bar.'
 *  });
 */

export interface PhotoViewScreenProps {
  route: {
    params: {
      source: ImageSourcePropType;
      title: string;
      headerInfoPress?: () => void;
    };
  };
}

const PhotoViewScreen: React.FC<PhotoViewScreenProps> = ({ route }) => {
  return (
    <ScreenWrapper color={colors.black}>
      <ScreenWrapper.Body withoutPadding>
        <Image
          style={image as ImageStyle}
          source={route.params?.source}
          resizeMode="contain"
        />
      </ScreenWrapper.Body>
    </ScreenWrapper>
  );
};

export default PhotoViewScreen;
