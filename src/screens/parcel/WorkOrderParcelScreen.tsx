import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { ScrollView } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '~/navigation/ParcelStack';
import ScreenWrapper from '~/screens/ScreenWrapper';
import { useAddParcelContext } from '~/screens/parcel/AddParcelProvider';
import FilledButton from '~/components/buttons/FilledButton';
import CardWrapper from '~/components/cards/CardWrapper';
import en from '~/localization/en';
import { buttonFullWidth, primarySolidButton } from '~/styles/buttons';
import TextInput from '~/components/inputs/TextInput';
import PhotoCaptureWrapper from '~/components/camera/PhotoCaptureWrapper';
import StepperInput from '~/components/inputs/StepperInput';
import { ParcelManager } from '~/services/sync/parcelManager';
import { Parcel } from '~/types/parcel.types';
import { Stop, StopType } from '~/types/stops.types';
import { ImageTitle } from '~/utils/images';
import { ImageStore } from '~/services/ImageStoreService';
import { getStopNameById } from '~/db/realm/operations/stop.operations';
import OutlinedButton from '~/components/buttons/OutlinedButton';
import StopInfoItemCard from '~/components/cards/StopInfoItemCard';
import { Notes, AddressBook } from '~/components/icons';
import colors from '~/styles/colors';
import { useDebounce } from '~/hooks/useDebounce';
import { padding16 } from '~/styles/spacing';

export interface WorkOrderParcelScreenProps {
  route: {
    params: {
      stop: Stop;
      parcel: Parcel;
      type: StopType;
      isBarcodeBypass?: boolean;
      isManualEntry?: boolean;
      barcodeBypassReason?: string;
    };
  };
  navigation: NativeStackNavigationProp<RootStackParamList>;
}

interface WorkOrder {
  Pickup_Contact_Name__c?: string;
  Delivery_Contact_Name__c?: string;
  [key: string]: any;
}

function getContactNameForStop(
  workOrder: WorkOrder | undefined,
  stopType: StopType,
  noContactFallback: string,
): string {
  if (!workOrder) return noContactFallback;
  if (stopType === StopType.Pickup)
    return workOrder.Pickup_Contact_Name__c ?? noContactFallback;
  if (stopType === StopType.Delivery)
    return workOrder.Delivery_Contact_Name__c ?? noContactFallback;
  return noContactFallback;
}

const WorkOrderParcelScreen: React.FC<WorkOrderParcelScreenProps> = ({
  route,
}) => {
  const {
    parcel,
    stop,
    type,
    isBarcodeBypass = false,
    isManualEntry = false,
    barcodeBypassReason,
  } = route.params;

  const navigation =
    useNavigation<NativeStackNavigationProp<RootStackParamList>>();

  const { parcelData, setParcelQuantity, setPosPhoto } = useAddParcelContext();

  const [destinationAddress, setDestinationAddress] = useState('');
  const [barcodeInput, setBarcodeInput] = useState('');
  const [barcodeError, setBarcodeError] = useState('');
  const [isBarcodeValid, setIsBarcodeValid] = useState(false);

  useEffect(() => {
    if (isManualEntry) return;
    setBarcodeInput(parcel?.Reference__c ?? '');
  }, [isManualEntry, parcel.Reference__c]);

  useEffect(() => {
    const getDestinationAddress = async () => {
      if (parcelData.dropoffDestination) {
        const destination = await getStopNameById(
          parcelData.dropoffDestination,
        );
        setDestinationAddress(destination);
      } else {
        setDestinationAddress('');
      }
    };

    getDestinationAddress();
  }, [parcelData?.dropoffDestination]);

  const confirmParcel = async () => {
    try {
      await ParcelManager.updateParcel(parcel.Id, {
        Quantity__c: type === StopType.Pickup ? parcelData.quantity : undefined,
        Dropoff_Quantity__c:
          type === StopType.Delivery ? parcelData.quantity : undefined,
        Pickup_Scan_Time__c:
          type === StopType.Pickup ? new Date().toISOString() : undefined,
        Delivery_Scan_Time__c:
          type === StopType.Delivery ? new Date().toISOString() : undefined,
        Barcode_Bypass_Reason__c: isBarcodeBypass
          ? barcodeBypassReason
          : undefined,
      });

      const base64String = parcelData.posPhoto?.includes('base64,')
        ? parcelData.posPhoto.split('base64,')[1]
        : parcelData.posPhoto;

      if (base64String) {
        await ImageStore.addImage({
          titleType: ImageTitle.parcel,
          stopId: stop.Id,
          parcelId: parcel.Id,
          routeSummaryId: stop.Summary__c,
          workOrderId: parcel.Work_Order__c,
          base64String,
        });
      }

      navigation.goBack();
    } catch (error) {
      console.error('WorkOrderParcelScreen: confirmParcel(): ', error);
    }
  };

  const parcelPhotoUri = !parcelData.posPhoto
    ? ''
    : `data:image/jpeg;base64,${parcelData.posPhoto}`;

  const workOrder = parcel?.Work_Order__r as WorkOrder | undefined;
  const contactName = getContactNameForStop(
    workOrder,
    type,
    en.no_contact_listed,
  );

  const validateBarcode = useCallback(
    async (scannedBarcode: string): Promise<boolean> => {
      if (!scannedBarcode.trim()) {
        return false;
      }

      return parcel.Reference__c === scannedBarcode.trim();
    },
    [parcel.Reference__c],
  );

  const debouncedValidateBarcode = useDebounce(
    async (scannedBarcode: string) => {
      const isValid = await validateBarcode(scannedBarcode);
      setIsBarcodeValid(isValid);
      setBarcodeError(isValid ? '' : en.barcode_mismatch);
    },
    500,
  );

  const handleBarcodeChange = useCallback(
    (barcodeText: string) => {
      setBarcodeInput(barcodeText);
      setBarcodeError('');
      debouncedValidateBarcode(barcodeText);
    },
    [debouncedValidateBarcode],
  );

  const buttonTitle = useMemo(() => {
    if (isManualEntry && !isBarcodeValid) {
      return en.enter_barcode_to_continue;
    }

    if (type === StopType.Pickup && !parcelData.posPhoto) {
      return en.complete_photo_to_continue;
    }

    return en.confirm;
  }, [type, parcelData.posPhoto, isBarcodeValid, isManualEntry]);

  const shouldShowRestOfUI = !isManualEntry || isBarcodeValid;
  const shouldShowBarcodeInput = parcel.Barcode_Required__c === true;

  const canSubmit = useMemo(() => {
    if (type === StopType.Pickup && !parcelData.posPhoto) {
      return false;
    }

    if (isManualEntry && type === StopType.Delivery) {
      return isBarcodeValid;
    }

    return true;
  }, [isManualEntry, isBarcodeValid, type, parcelData.posPhoto]);

  return (
    <ScreenWrapper>
      <ScreenWrapper.Body withoutPadding>
        <ScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={padding16}>
          {shouldShowBarcodeInput && (
            <CardWrapper>
              <TextInput
                label={en.barcode}
                defaultValue={barcodeInput}
                disabled={isManualEntry === false || isBarcodeBypass}
                onChangeText={handleBarcodeChange}
                errorMessage={barcodeError}
              />
            </CardWrapper>
          )}
          {shouldShowRestOfUI && (
            <>
              <StopInfoItemCard
                icon={<AddressBook color={colors.grey600} />}
                title={en.contact}
                value={contactName}
              />

              <StopInfoItemCard
                icon={<Notes color={colors.grey600} />}
                title={en.notes}
                value={parcel?.Comments__c || en.no_notes_available}
              />
              <CardWrapper>
                <TextInput
                  label={en.type}
                  defaultValue={parcelData.type?.Name}
                  disabled
                />
              </CardWrapper>

              {type === StopType.Pickup && (
                <CardWrapper>
                  <TextInput
                    label={en.delivery_destination}
                    defaultValue={
                      destinationAddress ?? parcelData.dropoffDestination
                    }
                    disabled
                  />
                </CardWrapper>
              )}

              {type === StopType.Pickup ? (
                <CardWrapper title={en.quantity}>
                  <StepperInput
                    value={parcelData?.quantity ?? 1}
                    setValue={setParcelQuantity}
                    infoLabel={en.number_of_parcels}
                  />
                </CardWrapper>
              ) : (
                <CardWrapper>
                  <TextInput
                    label={en.quantity}
                    defaultValue={String(parcelData?.quantity ?? '')}
                    disabled
                  />
                </CardWrapper>
              )}

              {type === StopType.Pickup && (
                <CardWrapper>
                  <PhotoCaptureWrapper
                    imageTitle={en.take_parcel_photo}
                    cameraTitle={en.take_a_parcel_photo}
                    imageData={parcelPhotoUri}
                    onCapture={setPosPhoto}
                  />
                </CardWrapper>
              )}
            </>
          )}
        </ScrollView>
      </ScreenWrapper.Body>

      <ScreenWrapper.Bottom>
        <FilledButton
          id={'WorkOrderParcelScreen.FilledButton.continue'}
          title={buttonTitle}
          style={[buttonFullWidth, primarySolidButton]}
          isDisabled={!canSubmit}
          color="primary"
          onClick={confirmParcel}
          containerStyle={null}
        />
        <OutlinedButton
          id="WorkOrderParcelScreen.OutlinedButton.cancel"
          title={en.cancel}
          color="primary"
          onClick={navigation.goBack}
        />
      </ScreenWrapper.Bottom>
    </ScreenWrapper>
  );
};

export default WorkOrderParcelScreen;
