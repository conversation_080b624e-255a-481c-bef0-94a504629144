import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  TextStyle,
  ViewStyle,
} from 'react-native';
import colors from '~/styles/colors';
import {
  blackText,
  buttonText,
  centerText,
  h3,
  heading,
  letterSpacing,
} from '~/styles/text';
import { center } from '~/styles/views';
import { useAddParcelContext } from '~/screens/parcel/AddParcelProvider';
import FilledButton from '~/components/buttons/FilledButton';
import { buttonFullWidth, primarySolidButton } from '~/styles/buttons';
import { useNavigation } from '@react-navigation/native';
import { StopOverviewScreenProps } from '~/screens/schedule/StopOverviewScreen';
import en from '~/localization/en';
import ScreenWrapper from '~/screens/ScreenWrapper';
import useLongPress from '~/hooks/useLongPress';
import { marginBottom10 } from '~/styles/spacing';
import CardWrapper from '~/components/cards/CardWrapper';
import SearchableDropdownSelect from '~/components/select/dropdown/SearchableDropdownSelect';

const SelectParcelTypeScreen: React.FC<StopOverviewScreenProps> = () => {
  const navigation = useNavigation();

  const {
    parcelData,
    parcelTypes,
    setParcelQuantity,
    setParcelType,
    isContinueButtonEnabled,
  } = useAddParcelContext();

  const increment = () => setParcelQuantity(parcelData.quantity + 1);
  const decrement = () => setParcelQuantity(parcelData.quantity - 1);

  const {
    handlePressIn: handlePressInIncrement,
    handlePressOut: handlePressOutIncrement,
  } = useLongPress(() => {
    const newQuantity = parcelData.quantity + 2;
    setParcelQuantity(newQuantity);
  });

  const {
    handlePressIn: handlePressInDecrement,
    handlePressOut: handlePressOutDecrement,
  } = useLongPress(() => {
    const newQuantity = parcelData.quantity > 2 ? parcelData.quantity - 2 : 1;
    setParcelQuantity(newQuantity);
  });

  const continueButtonPressed = () => {
    navigation.navigate('ParcelInformationScreen');
  };

  return (
    <ScreenWrapper>
      <ScreenWrapper.Body>
        <View style={styles.header}>
          <Text style={[heading, blackText] as TextStyle}>
            {en.declare_parcel}
          </Text>
        </View>

        <SearchableDropdownSelect
          label={en.type}
          selectedValue={parcelData.type}
          placeholderText={en.select_parcel_type}
          items={parcelTypes}
          onValueSelection={setParcelType}
          id={'SelectParcelTypeScreen.SearchableDropdownSelect.type'}
          searchPlaceHolderText={en.select_parcel_type}
        />

        {/* Quantity Section */}
        <CardWrapper title={en.quantity}>
          <View style={styles.quantitySelectorCounterContainer}>
            <TouchableOpacity
              style={[
                styles.quantitySelectorButton,
                parcelData.quantity === 1 &&
                  styles.quantitySelectorDisabledButton,
              ]}
              onPress={decrement}
              onLongPress={handlePressInDecrement}
              onPressOut={handlePressOutDecrement}
              disabled={parcelData.quantity === 1}>
              <Text
                style={[
                  styles.quantitySelectorButtonText,
                  parcelData.quantity === 1 &&
                    styles.quantitySelectorDisabledText,
                ]}>
                -
              </Text>
            </TouchableOpacity>

            <Text style={styles.quantitySelectorQuantity}>
              {parcelData.quantity}
            </Text>

            <TouchableOpacity
              style={styles.quantitySelectorButton}
              onPress={increment}
              onLongPress={handlePressInIncrement}
              onPressOut={handlePressOutIncrement}>
              <Text style={styles.quantitySelectorButtonText}>+</Text>
            </TouchableOpacity>
          </View>

          <Text
            style={
              (h3 as TextStyle,
              letterSpacing as TextStyle,
              centerText as TextStyle)
            }>
            {en.number_of_parcels}
          </Text>
        </CardWrapper>
      </ScreenWrapper.Body>
      <ScreenWrapper.Bottom>
        <FilledButton
          id={'SelectParcelTypeScreen.FilledButton.continue'}
          title={en.continue}
          style={[buttonFullWidth, primarySolidButton]}
          isDisabled={!isContinueButtonEnabled('first')}
          color="primary"
          onClick={continueButtonPressed}
          containerStyle={null}
        />
      </ScreenWrapper.Bottom>
    </ScreenWrapper>
  );
};

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    ...marginBottom10,
  },
  scanBarcodeText: {
    ...(buttonText as TextStyle),
    color: colors.red400,
  },
  quantitySelectorCounterContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginVertical: 10,
  },
  quantitySelectorButton: {
    ...(center as ViewStyle),
    width: 45,
    height: 45,
    borderRadius: 12,
    backgroundColor: colors.lightGrayishBlue,
    marginVertical: 10,
  },
  quantitySelectorDisabledButton: {
    backgroundColor: colors.backgroundLight,
  },
  quantitySelectorButtonText: {
    fontSize: 24,
    color: colors.textBlack,
  },
  quantitySelectorDisabledText: {
    color: colors.grey400,
  },
  quantitySelectorQuantity: {
    fontSize: 45,
    fontWeight: '700',
    marginHorizontal: 20,
    color: colors.textBlack,
  },
});

export default SelectParcelTypeScreen;
