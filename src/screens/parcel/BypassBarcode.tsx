import React, { useMemo, useState, useCallback } from 'react';
import { StyleSheet, ScrollView } from 'react-native';
import ScreenWrapper from '~/screens/ScreenWrapper';
import CardWrapper from '~/components/cards/CardWrapper';
import TextInput from '~/components/inputs/TextInput';
import PhotoCaptureWrapper from '~/components/camera/PhotoCaptureWrapper';
import FilledButton from '~/components/buttons/FilledButton';
import { paddingTop16 } from '~/styles/spacing';
import { buttonFullWidth, primarySolidButton } from '~/styles/buttons';
import { filterValidImageUris, ImageTitle } from '~/utils/images';
import { useImages } from '~/hooks/useImages';
import { useNavigation, useRoute } from '@react-navigation/native';
import { Stop } from '~/types/stops.types';
import { ImageStore } from '~/services/ImageStoreService';
import en from '~/localization/en';

type RouteParams = {
  stop: Stop;
  onBypassSubmitted?: (
    targetedParcelId?: string,
    barcodeBypassReason?: string,
  ) => void;
  targetedParcelId?: string;
};

const BypassBarcode = () => {
  const [imageRefreshKey, setImageRefreshKey] = useState(Date.now());
  const [bypassReason, setBypassReason] = useState('');

  const navigation = useNavigation();
  const route = useRoute();
  const { stop, onBypassSubmitted, targetedParcelId } = (route.params ??
    {}) as RouteParams;

  const { data: stopImages } = useImages({
    titleTypes: [ImageTitle.parcelBarcodeBypassPhoto],
    stopId: stop?.Id,
    routeSummaryId: stop?.Summary__c,
    parcelId: targetedParcelId ?? '',
    refreshKey: imageRefreshKey,
  });

  const validImageUris = useMemo(() => {
    if (!stopImages) return [];

    const barcodeImages = stopImages[ImageTitle.parcelBarcodeBypassPhoto];
    if (!barcodeImages) return [];

    const imagesArray = Array.isArray(barcodeImages)
      ? barcodeImages
      : [barcodeImages];

    return filterValidImageUris(imagesArray);
  }, [stopImages]);

  const handleImageCapture = useCallback(
    (imageBase64: string) => {
      try {
        ImageStore.addImage({
          titleType: ImageTitle.parcelBarcodeBypassPhoto,
          base64String: imageBase64,
          stopId: stop?.Id ?? '',
          parcelId: targetedParcelId ?? '',
          routeSummaryId: stop?.Summary__c ?? '',
        });

        setImageRefreshKey(Date.now());
      } catch (error) {
        console.error('BypassBarcode: handleImageCapture():', error);
      }
    },
    [stop?.Id, stop?.Summary__c, targetedParcelId],
  );

  const handleSubmit = useCallback(async () => {
    onBypassSubmitted?.(targetedParcelId, bypassReason);

    if (navigation.canGoBack()) {
      navigation.goBack();
    }
  }, [onBypassSubmitted, navigation, targetedParcelId, bypassReason]);

  const canSubmit = useMemo(() => {
    return bypassReason.trim().length > 0 && validImageUris.length > 0;
  }, [bypassReason, validImageUris]);

  const submitButtonTitle = useMemo(() => {
    return canSubmit ? en.confirm : en.select_reason_to_bypass;
  }, [canSubmit]);

  return (
    <ScreenWrapper>
      <ScreenWrapper.Body withoutPadding>
        <ScrollView
          contentContainerStyle={styles.scrollViewContainer}
          keyboardShouldPersistTaps="handled">
          <CardWrapper>
            <TextInput
              id="BypassBarcode.TextInput.reason"
              label={en.bypass_reason_label}
              value={bypassReason}
              placeholder={en.bypass_reason_placeholder}
              onChangeText={setBypassReason}
              multiline
              numberOfLines={4}
              maxLength={500}
              testID="bypass-reason-input"
            />
          </CardWrapper>

          <PhotoCaptureWrapper
            multiple={true}
            imageData={validImageUris}
            onCapture={handleImageCapture}
            imageTitle={en.barcode_photo}
            cameraTitle={en.barcode_photo}
          />
        </ScrollView>
      </ScreenWrapper.Body>

      <ScreenWrapper.Bottom>
        <FilledButton
          id="BypassBarcode.FilledButton.submit"
          title={submitButtonTitle}
          style={[buttonFullWidth, primarySolidButton]}
          color="primary"
          containerStyle={null}
          onClick={handleSubmit}
          isDisabled={!canSubmit}
        />
      </ScreenWrapper.Bottom>
    </ScreenWrapper>
  );
};

const styles = StyleSheet.create({
  scrollViewContainer: {
    padding: paddingTop16.paddingTop,
    flexGrow: 1,
  },
});

export default BypassBarcode;
