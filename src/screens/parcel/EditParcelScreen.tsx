import React, { useEffect, useState } from 'react';
import { StyleSheet, ScrollView } from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { Mobile } from '~/components/icons';
import FilledButton from '~/components/buttons/FilledButton';
import { checkCameraPermissionsAndOpenIt } from '~/components/camera/CheckPermissionAndOpenCamera';
import en from '~/localization/en';
import { primarySolidButton, buttonFullWidth } from '~/styles/buttons';
import RadioSelectionInput from '~/components/select/radio/RadioSelectionCard';
import { ParcelManager } from '~/services/sync/parcelManager';
import ScreenWrapper from '~/screens/ScreenWrapper';
import ScanBarcodeCard from '~/components/barcode/ScanBarcodeCard';
import { Parcel, ParcelType } from '~/types/parcel.types';
import BottomSheetModalWithRadioButton from '~/components/modals/BottomSheetWithRadioButtonsList';
import OutlinedButton from '~/components/buttons/OutlinedButton';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { RootStackParamList } from '~/navigation/ParcelStack';
import StepperInput from '~/components/inputs/StepperInput';
import CardWrapper from '~/components/cards/CardWrapper';
import { useImages } from '~/hooks/useImages';
import { ImageTitle } from '~/utils/images';
import { ImageStore } from '~/services/ImageStoreService';
import { useDestinationsData } from '~/utils/parcel';
import DropoffDestinationSelector from '~/components/select/dropdown/DropoffDestinationSelector';
import TextInput from '~/components/inputs/TextInput';
import PhotoCaptureWrapper from '~/components/camera/PhotoCaptureWrapper';
import SearchableDropdownSelect from '~/components/select/dropdown/SearchableDropdownSelect';
import { StopType } from '~/types/stops.types';
import CustomModal from '~/components/modals/CustomModal';

type EditParcelScreenProps = NativeStackScreenProps<
  RootStackParamList,
  'EditParcelScreen'
>;

const EditParcelScreen: React.FC = () => {
  const route = useRoute<EditParcelScreenProps['route']>();
  const navigation = useNavigation();

  const { stopList, stop, parcelTypes, stopType, parcel, isBarCodeScanner } =
    route.params as any;

  const {
    data: { parcel: parcelImages },
  } = useImages({
    titleTypes: [ImageTitle.parcel],
    stopId: stop?.Id,
    routeSummaryId: stop?.Summary__c,
    parcelId: parcel?.Id,
  });

  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isBarcodeModalVisible, setIsBarcodeModalVisible] = useState(false);

  const [parcelData, setParcelData] = useState<Parcel & { posPhoto: string }>({
    ...parcel,
    posPhoto: parcelImages?.uri,
  });

  const showBarcodeInputModal = () => {
    setIsBarcodeModalVisible(true);
  };

  const handleBarcodeConfirm = (barcode: string) => {
    setIsBarcodeModalVisible(false);
    setBarcodeResult(barcode);
  };

  useEffect(() => {
    if (
      Array.isArray(parcelImages) &&
      parcelImages.length > 0 &&
      !parcelData.posPhoto
    ) {
      setParcelData(prev => ({ ...prev, posPhoto: parcelImages[0].uri }));
    }
  }, [parcelImages, parcelData.posPhoto]);

  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      const params = route.params as any;

      if (params?.result) {
        if (isBarCodeScanner) {
          setParcelData(prev => ({ ...prev, Reference__c: params.result }));
        } else {
          setParcelData(prev => ({ ...prev, posPhoto: params.result }));
        }
      }
    });

    return unsubscribe;
  }, [navigation, route.params, isBarCodeScanner]);

  const destinationsData = useDestinationsData(stopList, stop?.Id);

  const saveParcel = async () => {
    try {
      await ParcelManager.updateParcel(parcel.Id, {
        Parcel_Type_Definition__c: parcelData.Parcel_Type_Definition__c,
        Parcel_Type_Name__c: parcelData.Parcel_Type_Name__c,
        Quantity__c: parcelData.Quantity__c,
        Delivery__c: parcelData.Delivery__c,
        Requires_Signature__c: parcelData.Requires_Signature__c,
        Comments__c: parcelData.Comments__c,
        Reference__c: parcelData.Reference__c,
      });

      const splitBase64String = parcelData.posPhoto?.split('base64,') || [];
      const base64String =
        splitBase64String.length > 0 ? splitBase64String[1] : null;

      if (base64String) {
        await ImageStore.deleteImages({
          titleType: ImageTitle.parcel,
          stopId: stop.Id,
          parcelId: parcel.Id,
          routeSummaryId: stop.Summary__c,
        });

        await ImageStore.addImage({
          titleType: ImageTitle.parcel,
          stopId: stop.Id,
          parcelId: parcel.Id,
          routeSummaryId: stop.Summary__c,
          base64String,
        });
      }

      navigation.goBack();
    } catch (error) {
      console.error('EditParcelScreen: saveParcel(): ', error);
    }
  };

  const setParcelQuantity = (value: number) => {
    setParcelData({ ...parcelData, Quantity__c: value });
  };

  const setParcelType = (type: ParcelType) => {
    setParcelData({
      ...parcelData,
      Parcel_Type_Definition__c: type.Id,
      Parcel_Type_Name__c: type.Name,
    });
  };

  const setIsSignatureRequired = (value: boolean) => {
    setParcelData({ ...parcelData, Requires_Signature__c: value });
  };

  const onChangeComment = (value: string) => {
    setParcelData({ ...parcelData, Comments__c: value });
  };

  const setPosPhoto = (photo: string) => {
    const uri = `data:image/png;base64,${photo}`;
    setParcelData({ ...parcelData, posPhoto: uri });
  };

  const setBarcodeResult = (barcode: string) => {
    setParcelData({ ...parcelData, Reference__c: barcode });
  };

  return (
    <ScreenWrapper isKeyboardSensitive>
      <ScreenWrapper.Body withoutPadding>
        <ScrollView contentContainerStyle={styles.scrollContainer}>
          <SearchableDropdownSelect
            label={en.type}
            selectedValue={
              parcelData?.Parcel_Type_Definition__c &&
              parcelData?.Parcel_Type_Name__c
                ? {
                    Id: parcelData?.Parcel_Type_Definition__c,
                    Name: parcelData?.Parcel_Type_Name__c,
                  }
                : null
            }
            placeholderText={en.select_parcel_type}
            items={parcelTypes}
            onValueSelection={setParcelType}
            id={'EditParcelScreen.SearchableDropdownSelect.type'}
            searchPlaceHolderText={en.select_parcel_type}
          />

          <CardWrapper title={en.quantity}>
            <StepperInput
              value={parcelData?.Quantity__c || 0}
              setValue={setParcelQuantity}
              infoLabel={en.number_of_parcels}
            />
          </CardWrapper>

          {destinationsData.length > 0 && stopType !== StopType.Delivery && (
            <DropoffDestinationSelector
              destinations={destinationsData}
              selectedDestination={parcelData?.Delivery__c}
              onOpenModal={() => setIsModalVisible(true)}
            />
          )}

          <RadioSelectionInput<boolean>
            label={en.signature_required}
            value={parcelData.Requires_Signature__c || false}
            onChange={setIsSignatureRequired}
            options={[
              { label: en.yes, value: true },
              { label: en.no, value: false },
            ]}
          />

          <ScanBarcodeCard
            barcode={parcelData.Reference__c || ''}
            onScan={() =>
              checkCameraPermissionsAndOpenIt(navigation, setBarcodeResult, {
                title: en.scan_barcode_title,
                infoMessage: en.scan_barcode_info,
                isBarCodeScanner: true,
                MessageIconComponent: Mobile,
                onEnterManually: showBarcodeInputModal,
              })
            }
            setBarcode={setBarcodeResult}
          />

          <CardWrapper>
            <PhotoCaptureWrapper
              imageTitle={en.take_parcel_photo}
              imageData={parcelData.posPhoto}
              onCapture={setPosPhoto}
            />
          </CardWrapper>

          <CardWrapper>
            <TextInput
              label={en.add_comment}
              value={parcelData.Comments__c || ''}
              placeholder={en.additional_information}
              onChangeText={onChangeComment}
            />
          </CardWrapper>
        </ScrollView>

        <CustomModal
          isVisible={isBarcodeModalVisible}
          onClose={() => setIsBarcodeModalVisible(false)}
          headerText={en.enter_barcode_manually}
          descriptionText={en.please_enter_the_barcode_number}
          okButtonText={en.confirm}
          cancelButtonText={en.cancel}
          onOkPress={handleBarcodeConfirm}
          onCancelPress={() => setIsBarcodeModalVisible(false)}
          showTextInput={true}
          testID="barcode-input-modal"
        />

        <BottomSheetModalWithRadioButton
          id="EditParcelScreen.BottomSheetModalWithRadioButton.delivery"
          selectedId={parcelData.Delivery__c}
          items={destinationsData}
          isVisible={isModalVisible}
          onClose={() => setIsModalVisible(false)}
          onItemSelected={(id: string) => {
            setIsModalVisible(false);
            setParcelData({ ...parcelData, Delivery__c: id });
          }}
          title={en.dropoff_destination}
        />
      </ScreenWrapper.Body>
      <ScreenWrapper.Bottom>
        <FilledButton
          id="EditParcelScreen.FilledButton.save"
          color="primary"
          title={en.save}
          isDisabled={!parcelData.posPhoto && !parcelData.Reference__c}
          style={[buttonFullWidth, primarySolidButton]}
          onClick={saveParcel}
        />
        <OutlinedButton
          id="EditParcelScreen.OutlinedButton.cancel"
          title={en.cancel}
          color="primary"
          onClick={navigation.goBack}
        />
      </ScreenWrapper.Bottom>
    </ScreenWrapper>
  );
};

const styles = StyleSheet.create({
  scrollContainer: { padding: 16 },
  image: { height: 100, borderRadius: 8 },
});

export default EditParcelScreen;
