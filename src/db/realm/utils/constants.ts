import { ParcelSchema } from '~/db/realm/schemas/parcel.schema';
import { StopSchema } from '~/db/realm/schemas/stop.schema';
import { RouteSummarySchema } from '~/db/realm/schemas/route-summary.schema';
import {
  ServiceSchema,
  ServiceTypeSchema,
} from '~/db/realm/schemas/service.schema';
import { DataSchema } from '~/db/realm/schemas/data.schema';
import { LocationSchema } from '~/db/realm/schemas/location.schema';
import { ImageSchema } from '~/db/realm/schemas/image.schema';
import { RequestQueueSchema } from '~/db/realm/schemas/request-queue.schema';
import { DailyScheduleSchema } from '~/db/realm/schemas/daily-schedule.schema';

export const EntityName = {
  STOP: StopSchema.name,
  PARCEL: ParcelSchema.name,
  ROUTE_SUMMARY: RouteSummarySchema.name,
  SERVICE: ServiceSchema.name,
  SERVICE_TYPE: ServiceTypeSchema.name,
  DAILY_SCHEDULE: DailyScheduleSchema.name,
  LOCATION: LocationSchema.name,
  REQUEST_QUEUE: RequestQueueSchema.name,
  IMAGE: ImageSchema.name,
  DATA: DataSchema.name,
} as const;
