import Realm from 'realm';

function migrateRouteOffsets(oldRealm: Realm, newRealm: Realm) {
  const oldRoutes = oldRealm.objects('Route_Summary__c');
  const newRoutes = newRealm.objects('Route_Summary__c');

  for (let i = 0; i < oldRoutes.length; i++) {
    const oldOffset = oldRoutes[i].UTC_Offset__c;
    let newOffset: number | null = null;

    if (typeof oldOffset === 'string' && oldOffset.trim() !== '') {
      const parsed = parseFloat(oldOffset);
      newOffset = isNaN(parsed) ? null : parsed;
    }

    newRoutes[i].UTC_Offset__c = newOffset;
  }
}

function migrateImageAndProtocolVerificationFields(
  oldRealm: Realm,
  newRealm: Realm,
) {
  const oldImages = oldRealm.objects('Image');
  const newImages = newRealm.objects('Image');

  for (let i = 0; i < oldImages.length; i++) {
    newImages[i].DailyScheduleId = null;
    const existingRouteSummaryId = oldImages[i].RouteSummaryId;
    newImages[i].RouteSummaryId = existingRouteSummaryId || null;
  }

  const oldProtocolVerifications = oldRealm.objects('Protocol_Verification__c');
  const newProtocolVerifications = newRealm.objects('Protocol_Verification__c');

  for (let i = 0; i < oldProtocolVerifications.length; i++) {
    newProtocolVerifications[i].Route_Summary__c =
      newProtocolVerifications[i].Route_Summary__c || null;
    newProtocolVerifications[i].Stop__c =
      newProtocolVerifications[i].Stop__c || null;
    newProtocolVerifications[i].Field_Comments__c =
      newProtocolVerifications[i].Field_Comments__c || null;
  }
}

function migrateTemperatureFields(oldRealm: Realm, newRealm: Realm) {
  const oldDailySchedules = oldRealm.objects('Daily_Schedule__c');
  const newDailySchedules = newRealm.objects('Daily_Schedule__c');
  for (let i = 0; i < oldDailySchedules.length; i++) {
    const oldTemp = oldDailySchedules[i].High_Temperature__c;
    let newTemp: number | null = null;
    if (oldTemp !== null && oldTemp !== undefined) {
      if (typeof oldTemp === 'string') {
        const parsed = parseFloat(oldTemp);
        newTemp = isNaN(parsed) ? null : parsed;
      } else if (typeof oldTemp === 'number') {
        newTemp = oldTemp;
      } else {
        newTemp = null;
      }
    }
    newDailySchedules[i].High_Temperature__c = newTemp;
  }

  const oldRoutes = oldRealm.objects('Route_Summary__c');
  const newRoutes = newRealm.objects('Route_Summary__c');
  for (let i = 0; i < oldRoutes.length; i++) {
    const oldTemp = oldRoutes[i].High_Temperature__c;
    let newTemp: number | null = null;
    if (oldTemp !== null && oldTemp !== undefined) {
      if (typeof oldTemp === 'string') {
        const parsed = parseFloat(oldTemp);
        newTemp = isNaN(parsed) ? null : parsed;
      } else if (typeof oldTemp === 'number') {
        newTemp = oldTemp;
      } else {
        newTemp = null;
      }
    }
    newRoutes[i].High_Temperature__c = newTemp;
  }
}

function migrateWorkOrderImages(oldRealm: Realm, newRealm: Realm) {
  const oldImages = oldRealm.objects('Image');
  const newImages = newRealm.objects('Image');

  for (let i = 0; i < oldImages.length; i++) {
    const oldImage = oldImages[i] as any;
    const newImage = newImages[i] as any;

    if (oldImage.RouteSummaryId === '') {
      newImage.RouteSummaryId = null;
    }
  }
}

function migrateBarcodeFields(oldRealm: Realm, newRealm: Realm) {
  const oldDailySchedules = oldRealm.objects('Daily_Schedule__c');
  const newDailySchedules = newRealm.objects('Daily_Schedule__c');

  for (let i = 0; i < oldDailySchedules.length; i++) {
    const oldSchedule = oldDailySchedules[i] as any;
    const newSchedule = newDailySchedules[i] as any;

    if (oldSchedule.Barcode_Scan_Override__c === undefined) {
      newSchedule.Barcode_Scan_Override__c = false;
    }
  }

  const oldParcels = oldRealm.objects('Parcel__c');
  const newParcels = newRealm.objects('Parcel__c');

  for (let i = 0; i < oldParcels.length; i++) {
    const oldParcel = oldParcels[i] as any;
    const newParcel = newParcels[i] as any;

    if (oldParcel.Barcode_Required__c === undefined) {
      newParcel.Barcode_Required__c = false;
    }

    if (oldParcel.Barcode_Bypass_Reason__c === undefined) {
      newParcel.Barcode_Bypass_Reason__c = null;
    }
  }
}

export function migrateRealm(oldRealm: Realm, newRealm: Realm) {
  if (oldRealm.schemaVersion < 2) {
    migrateRouteOffsets(oldRealm, newRealm);
  }

  if (oldRealm.schemaVersion < 4) {
    migrateTemperatureFields(oldRealm, newRealm);
  }

  if (oldRealm.schemaVersion < 5) {
    migrateImageAndProtocolVerificationFields(oldRealm, newRealm);
    migrateTemperatureFields(oldRealm, newRealm);
    migrateWorkOrderImages(oldRealm, newRealm);
  }

  if (oldRealm.schemaVersion < 15) {
    migrateBarcodeFields(oldRealm, newRealm);
  }
}
