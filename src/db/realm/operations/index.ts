import { getRealmInstance } from '~/db/realm/utils/realm.config';
import en from '~/localization/en';
import { UpdateEntityProps } from '~/types/sync.types';
import { safeWrite } from '~/db/realm/utils/safeRealm';

export const updateEntity = async ({
  entityId,
  entityName,
  updates,
}: UpdateEntityProps): Promise<boolean> => {
  try {
    const realm = await getRealmInstance();

    if (!realm || realm.isClosed) {
      throw new Error(en.database_connection_failed);
    }

    safeWrite(realm, () => {
      realm.create(
        entityName,
        {
          ...updates,
          Id: entityId,
        },
        true,
      );
    });

    return true;
  } catch (error) {
    console.error('index.ts: updateEntity(): Error updating entity:', error);
    return false;
  }
};
