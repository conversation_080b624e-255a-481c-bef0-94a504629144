import { getRealmInstance } from '~/db/realm/utils/realm.config';
import { DailySchedule } from '~/types/daily-schedule.types';
import { saveStops } from '~/db/realm/operations/stop.operations';
import { DailyScheduleSchema } from '~/db/realm/schemas/daily-schedule.schema';
import { safeQueryFiltered, safeWrite } from '~/db/realm/utils/safeRealm';

const saveDailySchedules = async (dailySchedules: DailySchedule[]) => {
  try {
    const realm = await getRealmInstance();
    if (!realm || realm.isClosed) return;
    const incomingScheduleIds = dailySchedules.map(s => s.Id);

    safeWrite(realm, () => {
      const existingSchedules = realm.objects<DailySchedule>(
        DailyScheduleSchema.name,
      );

      const schedulesToDelete = existingSchedules.filtered(
        `NOT (Id IN $0)`,
        incomingScheduleIds,
      );

      schedulesToDelete.forEach(schedule => {
        const stopsToDelete = realm
          .objects('Stop__c')
          .filtered('Daily_Schedule__c == $0', schedule.Id);

        const stopIdsToDelete = stopsToDelete.map(stop => stop.Id);

        const parcelsToDelete = realm
          .objects('Parcel__c')
          .filtered('Pickup__c IN $0', stopIdsToDelete);

        const servicesToDelete = realm
          .objects('Service__c')
          .filtered('Stop__c IN $0', stopIdsToDelete);

        const locationsToDelete = realm
          .objects('Location')
          .filtered('Stop__c IN $0', stopIdsToDelete);

        realm.delete(parcelsToDelete);
        realm.delete(servicesToDelete);
        realm.delete(locationsToDelete);
        realm.delete(stopsToDelete);
      });

      dailySchedules.forEach(dailySchedule => {
        const safeAttributes =
          typeof dailySchedule.attributes === 'string'
            ? dailySchedule.attributes
            : JSON.stringify(dailySchedule.attributes ?? {});
        realm.create(
          DailyScheduleSchema.name,
          {
            ...dailySchedule,
            attributes: safeAttributes,
          },
          true,
        );
      });
    });

    // Bulk stops insert for all schedules
    const allStops = dailySchedules.flatMap(
      schedule => schedule.Stops__r?.records ?? [],
    );
    if (allStops.length > 0) {
      await saveStops(allStops);
    }
  } catch (error) {
    console.error('daily-schedule.operations.ts: saveDailySchedules():', error);
    throw error;
  }
};

const getDailyScheduleByDate = async (
  date: string,
): Promise<DailySchedule[]> => {
  try {
    return await safeQueryFiltered<DailySchedule>(
      DailyScheduleSchema.name,
      'Date__c = $0',
      [date],
    );
  } catch (error) {
    console.error(
      'daily-schedule.operations.ts: getDailyScheduleByDate():',
      error,
    );
    throw error;
  }
};

const getDailyScheduleIdByDate = async (
  date: string,
): Promise<string | undefined> => {
  try {
    const dailySchedule = await safeQueryFiltered<DailySchedule>(
      DailyScheduleSchema.name,
      'Date__c = $0',
      [date],
    );

    return dailySchedule[0]?.Id;
  } catch (error) {
    console.error(
      'daily-schedule.operations.ts: getDailyScheduleIdByDate():',
      error,
    );

    return undefined;
  }
};

const getDisableLocationHistoryByDailyScheduleId = async (
  id: string,
): Promise<boolean | undefined> => {
  if (!id) {
    return false;
  }

  try {
    const dailySchedule = await safeQueryFiltered<DailySchedule>(
      DailyScheduleSchema.name,
      'Id = $0',
      [id],
    );

    return dailySchedule[0]?.Disable_Location_History__c;
  } catch (error) {
    console.error(
      'daily-schedule.operations.ts: getDisableLocationHistoryByDailyScheduleId():',
      error,
    );

    return false;
  }
};

export {
  saveDailySchedules,
  getDailyScheduleByDate,
  getDailyScheduleIdByDate,
  getDisableLocationHistoryByDailyScheduleId,
};
