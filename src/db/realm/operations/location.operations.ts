import { getRealmInstance } from '~/db/realm/utils/realm.config';
import { Location } from '~/types/location.types';
import uuid from 'react-native-uuid';
import { safeQueryAll, safeWrite } from '~/db/realm/utils/safeRealm';

const saveLocation = async (location: Location) => {
  try {
    const realm = await getRealmInstance();
    if (!realm || realm.isClosed) return;
    safeWrite(realm, () => {
      realm.create(
        'Location',
        {
          Id: uuid.v4(),
          Speed__c: location.Speed__c ?? undefined,
          Heading__c: location.Heading__c ?? undefined,
          ...location,
        },
        true,
      );
    });
  } catch (error) {
    console.error('Error saving location:', error);
    throw error;
  }
};

const getLocations = async (count: number): Promise<Location[]> => {
  try {
    const allLocations = await safeQueryAll<Location>('Location');
    const result = allLocations.slice(0, count);
    console.info('get Locations from db: ', JSON.stringify(result));
    return result;
  } catch (error) {
    console.error('Error getting location:', error);
    throw error;
  }
};

const deleteLocations = async (locationIds?: string[]) => {
  try {
    if (!locationIds) return;
    const realm = await getRealmInstance();
    if (!realm || realm.isClosed) return;
    safeWrite(realm, () => {
      locationIds.forEach(id => {
        const location = realm.objectForPrimaryKey('Location', id);
        if (location) {
          realm.delete(location);
        }
      });
    });
  } catch (error) {
    console.error('Error deleting location:', error);
    throw error;
  }
};

export { saveLocation, getLocations, deleteLocations };
