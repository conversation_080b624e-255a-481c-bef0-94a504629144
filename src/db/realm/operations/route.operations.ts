import { getRealmInstance } from '~/db/realm/utils/realm.config';
import { RouteSummary } from '~/types/routes.types';
import { RouteSummarySchema } from '~/db/realm/schemas/route-summary.schema';
import {
  safeQueryAll,
  safeQueryFiltered,
  safeWrite,
} from '~/db/realm/utils/safeRealm';

const saveRoutes = async (routes: RouteSummary[]) => {
  try {
    const realm = await getRealmInstance();
    if (!realm || realm.isClosed) return;
    const incomingRouteIds = routes.map(route => route.Id);

    safeWrite(realm, () => {
      const existingRoutes = realm.objects(RouteSummarySchema.name);
      const routesToDelete = existingRoutes.filtered(
        `NOT (Id IN $0)`,
        incomingRouteIds,
      );
      realm.delete(routesToDelete);

      routes.forEach(route => {
        realm.create(
          RouteSummarySchema.name,
          {
            ...route,
            attributes:
              typeof route.attributes === 'string'
                ? route.attributes
                : JSON.stringify(route.attributes ?? {}),
            Route__r:
              typeof route.Route__r === 'string'
                ? route.Route__r
                : JSON.stringify(route.Route__r ?? {}),
          },
          true,
        );
      });
    });
  } catch (error) {
    console.error('Error saving routes:', error);
    throw error;
  }
};

const getRoutes = async (): Promise<RouteSummary[]> => {
  try {
    const realmRoutes = await safeQueryAll<RouteSummary>(
      RouteSummarySchema.name,
    );
    return realmRoutes.map(route => ({
      ...route,
      attributes: route.attributes
        ? JSON.parse(route.attributes as string)
        : null,
      Route__r: route.Route__r ? JSON.parse(route.Route__r as string) : null,
    })) as RouteSummary[];
  } catch (error) {
    console.error('Error getting routes:', error);
    throw error;
  }
};

const getRouteById = async (
  routeId: string,
): Promise<RouteSummary | undefined> => {
  try {
    const routes = await safeQueryFiltered<RouteSummary>(
      RouteSummarySchema.name,
      'Id == $0',
      [routeId],
    );
    if (!routes.length) return undefined;
    const route = routes[0];
    return {
      ...route,
      attributes: route.attributes
        ? JSON.parse(route.attributes as string)
        : null,
      Route__r: route.Route__r ? JSON.parse(route.Route__r as string) : null,
    } as RouteSummary;
  } catch (error) {
    console.error('route.operations.ts: getRouteById():', error);
    throw error;
  }
};

const checkEndOfRouteSurveyCompleted = async (
  routeId: string,
): Promise<boolean> => {
  try {
    const realm = await getRealmInstance();

    if (!realm || realm.isClosed) {
      throw new Error('Realm is not available or is closed.');
    }

    const existingRoute = realm
      .objects<RouteSummary>(RouteSummarySchema.name)
      .filtered('Id == $0 AND Survey_Complete__c == $1', routeId, true);

    return existingRoute.length > 0;
  } catch (error) {
    console.error('checkEndOfRouteSurveyCompleted error:', error);
    throw error;
  }
};

export { saveRoutes, getRoutes, getRouteById, checkEndOfRouteSurveyCompleted };
