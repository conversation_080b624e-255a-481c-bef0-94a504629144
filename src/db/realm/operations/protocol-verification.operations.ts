import { ProtocolVerificationRecord } from '~/types/protocol.types';
import { getRealmInstance } from '~/db/realm/utils/realm.config';
import { ProtocolVerificationSchema } from '~/db/realm/schemas/protocol-verification.schema';
import { safeWrite } from '~/db/realm/utils/safeRealm';
import { checkEndOfRouteSurveyCompleted } from '~/db/realm/operations/route.operations';

const saveProtocolVerifications = async (
  protocolVerifications: ProtocolVerificationRecord[],
) => {
  if (protocolVerifications && protocolVerifications.length > 0) {
    try {
      const realm = await getRealmInstance();

      safeWrite(realm, () => {
        protocolVerifications.forEach(protocolVerification => {
          realm.create(
            ProtocolVerificationSchema.name,
            {
              ...protocolVerification,
              attributes:
                typeof protocolVerification.attributes === 'string'
                  ? protocolVerification.attributes
                  : JSON.stringify(protocolVerification.attributes ?? {}),
            },
            true,
          );
        });
      });
    } catch (error) {
      console.error(
        'protocol-verification.operations.ts: saveProtocolVerifications():',
        error,
      );
      throw error;
    }
  }
};

const createProtocolVerification = async (
  protocolVerification: ProtocolVerificationRecord,
) => {
  try {
    const realm = await getRealmInstance();

    safeWrite(realm, () => {
      realm.create(
        ProtocolVerificationSchema.name,
        {
          ...protocolVerification,
          isSyncReady: true,
          isSynced: false,
          retryCount: 0,
          createdAt: protocolVerification.createdAt || new Date(),
          updatedAt: new Date(),
          attributes:
            typeof protocolVerification.attributes === 'string'
              ? protocolVerification.attributes
              : JSON.stringify(protocolVerification.attributes ?? {}),
        },
        true,
      );
    });
  } catch (error) {
    console.error(
      'protocol-verification.operations.ts: createProtocolVerification():',
      error,
    );
    throw error;
  }
};

const checkProtocolVerificationExists = async (
  dailyScheduleId: string,
): Promise<boolean> => {
  try {
    const realm = await getRealmInstance();
    const existingVerifications = realm
      .objects<ProtocolVerificationRecord>(ProtocolVerificationSchema.name)
      .filtered('Daily_Schedule__c == $0', dailyScheduleId);
    return existingVerifications.length > 0;
  } catch (error) {
    console.error(
      'protocol-verification.operations.ts: checkProtocolVerificationExists():',
      error,
    );
    throw error;
  }
};

const checkProtocolVerificationExistsForStop = async (
  stopId: string,
  routeId: string,
): Promise<boolean> => {
  try {
    const realm = await getRealmInstance();

    if (!realm || realm.isClosed) {
      throw new Error('Realm is not available or is closed.');
    }

    const existingVerifications = realm
      .objects<ProtocolVerificationRecord>(ProtocolVerificationSchema.name)
      .filtered('Stop__c == $0 AND Route_Summary__c == $1', stopId, routeId);

    return existingVerifications.length > 0;
  } catch (error) {
    console.error('checkProtocolVerificationExistsForStop error:', error);
    throw error;
  }
};

const checkPostRouteSurveyExists = async (
  routeId: string,
): Promise<boolean> => {
  try {
    const realm = await getRealmInstance();

    if (!realm || realm.isClosed) {
      throw new Error('Realm is not available or is closed.');
    }

    const existingVerifications = realm
      .objects<ProtocolVerificationRecord>(ProtocolVerificationSchema.name)
      .filtered(
        'Route_Summary__c == $0 AND Protocol_Step_Id__c == $1',
        routeId,
        'PostRouteSurvey',
      );

    return existingVerifications.length > 0;
  } catch (error) {
    console.error('checkPostRouteSurveyExists error:', error);
    throw error;
  }
};

const getIncompleteEndOfRouteProtocols = async (
  routeId: string,
): Promise<{ endOfRouteSurvey: boolean; postRouteSurvey: boolean }> => {
  try {
    const endOfRouteSurveyExists =
      await checkEndOfRouteSurveyCompleted(routeId);

    const postRouteSurveyExists = await checkPostRouteSurveyExists(routeId);

    return {
      endOfRouteSurvey: endOfRouteSurveyExists,
      postRouteSurvey: postRouteSurveyExists,
    };
  } catch (error) {
    console.error('Error getting incomplete end-of-route protocols:', error);
    throw error;
  }
};

export {
  saveProtocolVerifications,
  createProtocolVerification,
  checkProtocolVerificationExists,
  checkProtocolVerificationExistsForStop,
  getIncompleteEndOfRouteProtocols,
};
