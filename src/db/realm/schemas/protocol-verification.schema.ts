import Realm from 'realm';

export const ProtocolVerificationSchema: Realm.ObjectSchema = {
  name: 'Protocol_Verification__c',
  primaryKey: 'Id',
  properties: {
    Id: 'string',
    Protocol_Step_Id__c: 'string?',
    Protocol__c: 'string?',
    Field_Value__c: 'string?',
    Field_Comments__c: 'string?',
    Daily_Schedule__c: 'string?',
    Stop__c: 'string?',
    Route_Summary__c: 'string?',
    attributes: 'string?',
    isSyncReady: {
      type: 'bool',
      default: false,
    },
    isSynced: {
      type: 'bool',
      default: false,
    },
    retryCount: {
      type: 'int',
      default: 0,
    },
    createdAt: {
      type: 'date',
      default: new Date(),
    },
    updatedAt: {
      type: 'date',
      default: new Date(),
    },
  },
};
