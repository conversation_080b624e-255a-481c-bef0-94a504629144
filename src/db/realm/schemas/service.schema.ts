import Realm from 'realm';

export const ServiceSchema: Realm.ObjectSchema = {
  name: 'Service__c',
  primaryKey: 'Id',
  properties: {
    Id: 'string',
    Name: 'string',
    Stop__c: 'string',
    Daily_Schedule__c: 'string',
    Service_Type_Name__c: 'string?',
    Completed_Time__c: 'string?',
  },
};

export const ServiceTypeSchema: Realm.ObjectSchema = {
  name: 'Service_Type__c',
  primaryKey: 'Id',
  properties: {
    Id: 'string',
    Name: 'string',
  },
};
