export const StopSchema = {
  name: 'Stop__c',
  primaryKey: 'Id',
  properties: {
    Id: 'string',
    attributes: 'string?',
    Name: 'string?',
    Stop_Time_Min__c: 'string?',
    Stop_Time_Preferred__c: 'string?',
    Stop_Time_Max__c: 'string?',
    ETA__c: 'string?',
    Type__c: 'string?',
    Address__c: 'string?',
    Address_1__c: 'string?',
    Address_2__c: 'string?',
    City__c: 'string?',
    Postal_Code__c: 'string?',
    State__c: 'string?',
    Status__c: 'string?',
    Stop_Coordinates__Latitude__s: 'double?',
    Stop_Coordinates__Longitude__s: 'double?',
    Summary__c: 'string?', // Route summary id
    Notes__c: 'string?',
    Coordinates__c: 'string?',
    Arrival_Time__c: 'string?',
    Completed_Time__c: 'string?',
    Geofencing_Distance__c: 'double?',
    Service_Comments__c: 'string?',
    Proof_of_Service__c: 'string?',
    Post_Date__c: 'string?',
    Schedule__c: 'string?',
    Pieces__c: 'int?',
    Pieces_Delivered__c: 'int?',
    Expected_Dropoff_Quantity__c: 'int?',
    SNO_Bypass_DateTime__c: 'string?',
    SNO_Driver_Waiting__c: 'bool?',
    Location__r: 'mixed?',
    Pickup_Parcels__r: 'mixed?',
    Delivery_Parcels__r: 'mixed?',
    Services__r: 'mixed?',
    Daily_Schedule__c: 'string?',
  },
};
