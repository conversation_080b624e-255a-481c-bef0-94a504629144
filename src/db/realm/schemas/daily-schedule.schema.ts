import Realm from 'realm';

export const DailyScheduleSchema: Realm.ObjectSchema = {
  name: 'Daily_Schedule__c',
  primaryKey: 'Id',
  properties: {
    Id: 'string',
    Name: 'string',
    Date__c: 'string',
    Driver__c: 'string',
    Driver_User_Id__c: 'string',
    High_Temperature__c: 'double?',
    Stops__r: 'mixed',
    Disable_Location_History__c: 'bool?',
    Barcode_Scan_Override__c: {
      type: 'bool',
      default: false,
    },
  },
};
