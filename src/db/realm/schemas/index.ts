import { DataSchema } from '~/db/realm/schemas/data.schema';
import { RouteSummarySchema } from '~/db/realm/schemas/route-summary.schema';
import { StopSchema } from '~/db/realm/schemas/stop.schema';
import { LocationSchema } from '~/db/realm/schemas/location.schema';
import { RequestQueueSchema } from '~/db/realm/schemas/request-queue.schema';
import {
  ParcelSchema,
  ParcelTypeSchema,
} from '~/db/realm/schemas/parcel.schema';
import { ImageSchema } from '~/db/realm/schemas/image.schema';
import { DailyScheduleSchema } from '~/db/realm/schemas/daily-schedule.schema';
import {
  ServiceSchema,
  ServiceTypeSchema,
} from '~/db/realm/schemas/service.schema';
import { ProtocolVerificationSchema } from '~/db/realm/schemas/protocol-verification.schema';

export const SCHEMAS: Realm.ObjectSchema[] = [
  DataSchema,
  RouteSummarySchema,
  DailyScheduleSchema,
  StopSchema,
  LocationSchema,
  RequestQueueSchema,
  ParcelSchema,
  ParcelTypeSchema,
  ImageSchema,
  ServiceSchema,
  ServiceTypeSchema,
  ProtocolVerificationSchema,
];
