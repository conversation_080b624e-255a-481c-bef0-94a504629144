import Realm from 'realm';
import { RequestQueueStatus, RequestType } from '~/types/request-queue.types';

export const RequestQueueSchema: Realm.ObjectSchema = {
  name: 'RequestQueue',
  primaryKey: 'entityId',
  properties: {
    entityId: 'string',
    entityName: 'string',
    status: {
      type: 'string',
      default: RequestQueueStatus.PENDING,
    },
    requestType: {
      type: 'string',
      default: RequestType.UPDATE,
    },
    payload: 'mixed',
    createdAt: {
      type: 'date',
      default: new Date(),
    },
    lastUpdatedAt: {
      type: 'date',
      default: new Date(),
    },
    retryCount: {
      type: 'int',
      default: 0,
    },
  },
};
