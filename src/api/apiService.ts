import { get, post } from '~/api/apiClient';
import { API_ENDPOINT_KEYS, API_ENDPOINTS } from '~/api/apiEndpoints';
import { isDeviceOnline } from '~/utils/network';
import { saveRoutes } from '~/db/realm/operations/route.operations';
import { saveStops } from '~/db/realm/operations/stop.operations';
import { saveDailySchedules } from '~/db/realm/operations/daily-schedule.operations';
import { saveParcelTypes } from '~/db/realm/operations/parcel.operations';
import { saveServiceTypes } from '~/db/realm/operations/service.operations';
import { saveProtocolVerifications } from '~/db/realm/operations/protocol-verification.operations';
import { ProtocolVerificationRecord } from '~/types/protocol.types';

type HttpMethod = 'get' | 'post' | 'patch' | 'put' | 'delete';

export interface ApiResponse<T = unknown> {
  totalSize: number;
  done: boolean;
  records: T[];
}

export interface DataOperationConfig {
  onSync: (data: ApiResponse<unknown> | null, error?: string) => void;
  requestIdentifier: keyof typeof API_ENDPOINTS;
  requestData?: Record<string, unknown>;
}

const buildDynamicUrl = (
  endpoint: string,
  params: Record<string, unknown>,
): string => {
  return endpoint.replace(/{(\w+)}/g, (_, key) => {
    if (!(key in params)) {
      throw new Error(`Missing required parameter: ${key}`);
    }
    return encodeURIComponent(String(params[key]));
  });
};

// Network operations
const performNetworkOperation = async ({
  method,
  requestIdentifier,
  requestData,
  onSync,
}: {
  method: HttpMethod;
  requestIdentifier: string;
  requestData?: Record<string, unknown>;
  onSync: DataOperationConfig['onSync'];
}): Promise<ApiResponse<unknown>> => {
  const endpoint = API_ENDPOINTS[requestIdentifier];

  if (!endpoint) {
    throw new Error(`No endpoint found for: ${requestIdentifier}`);
  }

  const url = buildDynamicUrl(endpoint, requestData || {});

  // TODO: Remove this once the patch stop endpoint is fixed
  if (
    requestIdentifier === API_ENDPOINT_KEYS.PATCH_STOP ||
    requestIdentifier === API_ENDPOINT_KEYS.PATCH_ROUTE_SUMMARY ||
    requestIdentifier === API_ENDPOINT_KEYS.PATCH_WORK_ORDER
  ) {
    delete requestData?.entityId;
  }

  if (requestIdentifier === API_ENDPOINT_KEYS.GET_CONTACT_BY_USER_ID) {
    delete requestData?.userId;
  }

  if (requestIdentifier === API_ENDPOINT_KEYS.GET_TODAYS_SCHEDULE) {
    delete requestData?.userId;
  }

  if (
    requestIdentifier === API_ENDPOINT_KEYS.GET_PARCEL_TYPES ||
    requestIdentifier === API_ENDPOINT_KEYS.GET_SERVICE_TYPES
  ) {
    delete requestData?.accountId;
  }

  try {
    const response =
      method === 'get'
        ? await get<ApiResponse>(url, { params: requestData })
        : await post<ApiResponse>({ url, data: { data: requestData }, method });

    // Handle caching based on request type
    const { records } = response;

    switch (requestIdentifier) {
      case API_ENDPOINT_KEYS.GET_ROUTE_LIST:
        await saveRoutes(records);
        break;
      case API_ENDPOINT_KEYS.GET_STOP_LIST:
        await saveStops(records);
        console.info('🔍 Stops saved to realm for user id', global.userId, {
          records: JSON.stringify(records),
        });
        break;
      case API_ENDPOINT_KEYS.GET_TODAYS_SCHEDULE:
        await saveDailySchedules(records);
        // console.info(
        //   '🔍 Daily schedules saved to realm for user id',
        //   global.userId,
        //   {
        //     records: JSON.stringify(records),
        //   },
        // );
        break;
      case API_ENDPOINT_KEYS.GET_PARCEL_TYPES:
        await saveParcelTypes(records ?? []);
        break;
      case API_ENDPOINT_KEYS.GET_SERVICE_TYPES:
        await saveServiceTypes(records);
        break;
      case API_ENDPOINT_KEYS.GET_PROTOCOL_VERIFICATIONS:
        await saveProtocolVerifications(
          (records as ProtocolVerificationRecord[]) ?? [],
        );
        break;
      default:
        console.info('Not saving data to realm', { requestIdentifier });
    }

    onSync(response);
    return response;
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : 'Unknown error';
    console.error(`ApiService: performNetworkOperation():`, errorMessage);
    onSync(null, errorMessage);
    throw error;
  }
};

// Main exported functions
export const fetchData = async ({
  onSync,
  requestIdentifier,
  requestData,
}: DataOperationConfig): Promise<ApiResponse | null> => {
  try {
    const isOnline = await isDeviceOnline();

    if (!isOnline) {
      throw new Error('Please check your internet connection to get data');
    }

    return await performNetworkOperation({
      method: 'get',
      requestIdentifier,
      requestData,
      onSync,
    });
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : 'Unknown error';
    onSync(null, `Failed to fetch data: ${errorMessage}`);
    throw error;
  }
};

export const postData = async ({
  onSync,
  requestIdentifier,
  requestData,
  method = 'post',
}: DataOperationConfig & {
  method?: Exclude<HttpMethod, 'get'>;
}): Promise<ApiResponse | null> => {
  try {
    const isOnline = await isDeviceOnline();
    if (!isOnline) {
      throw new Error('You are offline. Post request failed');
    }

    return await performNetworkOperation({
      method,
      requestIdentifier,
      requestData,
      onSync,
    });
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : 'Unknown error';
    onSync(null, `Failed to post data: ${errorMessage}`);
    throw error;
  }
};
