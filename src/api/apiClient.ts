/**
 * This module provides utility functions for making HTTP `GET` and `POST` requests using Axios.
 * It handles request configuration, including setting a base URL and attaching an authorization token.
 *
 * @module apiService
 */
import { AxiosRequestConfig, Method } from 'axios';
import { resetNavigationToScreen } from '~/navigation/navigationService';
import { axiosPrivate } from '~/services/AxiosService';
import { SentryService } from '~/services/SentryService';
import { ERROR_CODES } from '~/utils/constants';

/**
 * Creates a configured request object with authorization headers
 */
const createRequest = (config?: AxiosRequestConfig): AxiosRequestConfig => ({
  ...config,
  headers: {
    ...(config?.headers || {}),
  },
});

/**
 * Core request handler that all HTTP methods will use
 */
const makeRequest = async <T>(
  method: Method,
  url: string,
  data?: any,
  config?: AxiosRequestConfig,
): Promise<T> => {
  try {
    const requestConfig = createRequest(config);

    const response = await axiosPrivate.request<T>({
      method,
      url: url,
      data,
      ...requestConfig,
    });

    return response.data;
  } catch (error) {
    handleError(error);
    throw error;
  }
};

const get = async <T>(url: string, config?: AxiosRequestConfig): Promise<T> =>
  makeRequest<T>('get', url, undefined, config);

const post = async <T>({
  url,
  data: { data: requestData },
  config = {},
  method = 'post',
}: {
  url: string;
  data: { data: any };
  config?: AxiosRequestConfig;
  method?: 'post' | 'patch' | 'put' | 'delete';
}): Promise<T> => makeRequest<T>(method, url, requestData, config);

const handleError = async (error: any) => {
  if (error.response) {
    console.error(
      `ApiClient.ts: handleError(): ${error.response.status} - ${JSON.stringify(error.response.data)}`,
    );

    if (error.response.status === ERROR_CODES.UNAUTHORIZED) {
      await SentryService.logSentryError({
        error: new Error('Unauthorized - token expired'),
        tags: {
          file: 'ApiClient.ts',
          function: 'handleError',
          eventType: 'logout_triggered',
          httpStatusCode: error?.response?.status?.toString(),
        },
        extra: {
          responseData: error?.response?.data,
          requestUrl: error?.config?.url,
          requestMethod: error.config?.method,
          globalUserId: !!global.userId,
        },
        contexts: {
          tokenExpiration: {
            source: 'api_client',
            timestamp: new Date().toISOString(),
          },
        },
        level: 'warning',
      });
      // Token has expired and user should be logged out
      global.userId = null;
      resetNavigationToScreen('AuthStack', { screen: 'Login' });
    }
  } else if (error.request) {
    console.error(
      'ApiClient.ts: handleError(): No response received from server',
    );
  } else {
    console.error(
      `ApiClient.ts: handleError(): Error in request setup: ${error.message}`,
    );
  }
};

export { get, post };
