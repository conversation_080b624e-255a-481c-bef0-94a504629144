import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import RouteScreen from '~/screens/route/RouteScreen';
import ThemedHeader from '~/components/headers/ThemedHeader';
import colors from '~/styles/colors';
import RouteDetailScreen from '~/screens/route/RouteDetailScreen';
import { RouteSummary } from '~/types/routes.types';
import PhotoViewScreen, {
  PhotoViewScreenProps,
} from '~/screens/PhotoViewScreen';
import CollectSignature from '~/screens/CollectSignature';
import StopOverviewScreen from '~/screens/schedule/StopOverviewScreen';
import CameraViewScreen from '~/screens/CameraViewScreen';
import { Stop } from '~/types/stops.types';
import ParcelStack from '~/navigation/ParcelStack';
import SubHeader from '~/components/headers/SubHeader';
import { useSync } from '~/hooks/useSync';
import { SyncServiceProvider } from '~/services/sync/syncServiceContext';
import { validateThatDataIsNotEmpty } from '~/utils/validation/data';
import en from '~/localization/en';
import ConfirmationScreen from '~/screens/schedule/ConfirmationScreen';
import { Protocol } from '~/types/protocol.types';
import {
  ConfirmationType,
  ConfirmationSubType,
} from '~/types/confirmations.types';
import ProtocolEngineStack from '~/navigation/ProtocolEngineStack';
import { ProtocolContextData } from '~/types/protocolActions.types';

export type RootStackParamList = {
  RouteScreen: undefined;
  RouteDetailScreen: { route: RouteSummary; isCurrent: boolean };
  StopOverviewScreen: { stop: Stop; stopList: Stop[] };
  PhotoViewScreen: PhotoViewScreenProps['route']['params'];
  CollectSignature: {
    title: string;
    signatureURI: string;
    signatureFilePath: string;
    comments: string;
    onCompleteDropOff: (signature: string, comments: string) => void;
  };
  SelectParcelTypeScreen: { stop: Stop };
  ParcelInformationScreen: { stop: Stop };
  ParcelProofOfServiceScreen: { stop: Stop };
  ConfirmationScreen: {
    type: ConfirmationType;
    subType: ConfirmationSubType;
  };
  ProtocolEngineStack: {
    protocols: Protocol[];
    scenario: ProtocolContextData;
    onSuccessCallback?: () => void;
  };
};

const Stack = createNativeStackNavigator<RootStackParamList>();

function RouteStack() {
  const { syncUp, syncDown, status } = useSync();

  return (
    <SyncServiceProvider syncUp={syncUp} syncDown={syncDown} status={status}>
      <Stack.Navigator
        initialRouteName="Routes"
        screenOptions={{ headerShadowVisible: false }}>
        <Stack.Screen
          name="Routes"
          component={RouteScreen}
          options={{
            headerStyle: {
              backgroundColor: colors.darkBlue25,
            },
            header: () => <ThemedHeader />,
          }}
        />
        <Stack.Screen
          name="RouteDetailScreen"
          component={RouteDetailScreen}
          options={({ route, options }) => ({
            header: () => (
              <SubHeader
                route={route}
                options={options}
                title={route.params?.route.Name}
              />
            ),
          })}
        />
        <Stack.Screen
          name="PhotoViewScreen"
          component={PhotoViewScreen}
          options={({ route, options }) => ({
            header: () => (
              <SubHeader
                route={route}
                options={options}
                title={route.params?.title}
                backgroundColor={colors.black}
              />
            ),
          })}
        />
        <Stack.Screen
          name="CollectSignature"
          component={CollectSignature}
          options={({ route, options }) => ({
            header: () => (
              <SubHeader
                route={route}
                options={options}
                title={
                  validateThatDataIsNotEmpty(route.params?.title)
                    ? route.params?.title
                    : en.collect_signature
                }
              />
            ),
          })}
        />

        <Stack.Screen
          name="StopOverviewScreen"
          component={StopOverviewScreen}
          options={({ route, options }) => ({
            header: () => (
              <SubHeader
                route={route}
                options={options}
                title={
                  validateThatDataIsNotEmpty(route.params?.stop?.Type__c)
                    ? route.params?.stop?.Type__c
                    : en.stop_overview
                }
                backgroundColor={colors.backgroundLight}
              />
            ),
            headerBackVisible: false,
          })}
        />

        <Stack.Screen
          name="CameraViewScreen"
          component={CameraViewScreen}
          options={() => ({
            headerShown: false,
          })}
        />

        <Stack.Screen
          name="ParcelStack"
          component={ParcelStack}
          options={() => ({
            headerShown: false,
          })}
        />

        <Stack.Screen
          name="ConfirmationScreen"
          component={ConfirmationScreen}
          options={({ route, options }) => ({
            header: () => (
              <SubHeader
                route={route}
                options={options}
                showBackArrow={false}
                showContactDispatch={false}
              />
            ),
          })}
        />

        <Stack.Screen
          name="ProtocolEngineStack"
          component={ProtocolEngineStack}
          options={{
            headerShown: false,
          }}
        />
      </Stack.Navigator>
    </SyncServiceProvider>
  );
}

export default RouteStack;
