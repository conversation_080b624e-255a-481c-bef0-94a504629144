import React, { useEffect, useMemo } from 'react';
import { RouteProp } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';

import { RootStackParamList } from '~/navigation/RouteStack';
import {
  ProtocolScenarioProvider,
  useProtocolContext,
} from '~/services/protocol/context/ProtocolContext';
import { Step } from '~/types/step.types';
import SubHeader from '~/components/headers/SubHeader';
import StepPage from '~/screens/protocol/StepPage';
import StepModal from '~/screens/protocol/StepModal';
import colors from '~/styles/colors';
import { useProtocolDataStore } from '~/services/protocol/context/ProtocolDataStore';

const Stack = createNativeStackNavigator();

export type ProtocolEngineStackParams = {
  StepPage: {
    step: Step;
  };
  StepModal: {
    step: Step;
  };
};

const ProtocolEngineStack = ({
  route,
}: {
  route: RouteProp<RootStackParamList, 'ProtocolEngineStack'>;
}) => {
  const { protocols, scenario, onSuccessCallback } = route.params;
  const { resetUserData } = useProtocolDataStore();

  const currentProtocol = useMemo(() => {
    if (!Array.isArray(protocols) || protocols.length === 0) {
      throw new Error('No protocols provided or protocols array is empty');
    }
    return protocols[0];
  }, [protocols]);

  const initialStep = useMemo(() => {
    const step = currentProtocol?.steps?.at?.(0) ?? null;

    if (!step) {
      throw new Error('No initial step found in the protocol');
    }
    return step;
  }, [currentProtocol]);

  const initialRouteName = useMemo(() => {
    return initialStep.type === 'modal' ? 'StepModal' : 'StepPage';
  }, [initialStep]);

  useEffect(() => {
    return () => {
      resetUserData();
    };
  }, [resetUserData]);

  return (
    <ProtocolScenarioProvider
      scenario={scenario}
      protocol={currentProtocol}
      onSuccessCallback={onSuccessCallback}>
      <Stack.Navigator initialRouteName={initialRouteName}>
        <Stack.Screen
          name="StepPage"
          component={StepPage}
          initialParams={{ step: initialStep }}
          options={{ header: StepHeader }}
        />
        <Stack.Screen
          name="StepModal"
          component={StepModal}
          initialParams={{ step: initialStep }}
          options={{
            presentation: 'transparentModal',
            animation: 'fade_from_bottom',
            headerShown: false,
          }}
        />
      </Stack.Navigator>
    </ProtocolScenarioProvider>
  );
};

const StepHeader = ({
  route: currentRoute,
}: {
  route: RouteProp<ProtocolEngineStackParams, 'StepPage'>;
}) => {
  const { title: titleInfo, progress } = currentRoute.params.step.header ?? {};
  const { scenario } = useProtocolContext();
  const showProgress = !!progress;

  const title = useMemo(() => {
    if (titleInfo?.type === 'dynamic') {
      return scenario[titleInfo.value];
    }
    return titleInfo?.value;
  }, [scenario, titleInfo]);

  return (
    <SubHeader
      title={title}
      backgroundColor={colors.backgroundLight}
      progressHeaderText={progress}
      showProgressText={showProgress}
      showContactDispatch={!showProgress}
    />
  );
};

export default ProtocolEngineStack;
