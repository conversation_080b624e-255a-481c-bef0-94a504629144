import { createNavigationContainerRef } from '@react-navigation/native';

export const navigationRef = createNavigationContainerRef();

/**
 * Navigate to a specific screen.
 * @param {string} name - Screen name.
 * @param {object} [params] - Optional parameters to pass to the screen.
 */
export const navigate = (name, params) => {
  if (navigationRef.isReady()) {
    navigationRef.navigate(name, params);
  } else {
    console.warn('Navigation not ready yet!');
  }
};

/**
 * Go back to the previous screen.
 */
export const goBack = () => {
  if (navigationRef.isReady() && navigationRef.canGoBack()) {
    navigationRef.goBack();
  } else {
    console.warn('Cannot go back!');
  }
};

/**
 * Clear the navigation stack and navigate to a specific screen.
 * @param {string} stackName - Name of the stack to navigate to.
 * @param {string} screen - Name of the screen inside the stack.
 * @param {object} [params] - Optional parameters to pass to the screen.
 */
export const resetNavigationToScreen = (
  stackName: string,
  screen: any,
  params?: any,
) => {
  if (navigationRef.isReady()) {
    navigationRef.reset({
      index: 0,
      routes: [{ name: stackName, params: { screen, ...params } }],
    });
  } else {
    console.warn('Navigation not ready yet!');
  }
};
