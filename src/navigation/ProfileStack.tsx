import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';

import SubHeader from '~/components/headers/SubHeader';
import ProfileScreen from '~/screens/ProfileScreen';

const Stack = createNativeStackNavigator();

function ProfileStack() {
  return (
    <Stack.Navigator initialRouteName="Profile">
      <Stack.Screen
        name="Profile"
        component={ProfileScreen}
        options={() => ({
          header: () => (
            <SubHeader
              title="Profile"
              showBackArrow={false}
              showContactDispatch={false}
            />
          ),
        })}
      />
    </Stack.Navigator>
  );
}

export default ProfileStack;
