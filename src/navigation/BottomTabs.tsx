import React, { useEffect, useState } from 'react';
import {
  TouchableOpacity,
  Text,
  View,
  StyleSheet,
  Platform,
  TextStyle,
} from 'react-native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';

import ProfileStack from '~/navigation/ProfileStack';

import colors from '~/styles/colors';
import { shadowView } from '~/styles/views';
import { primaryText, grayText, bottomTabLabels } from '~/styles/text';
import { User, Route, Info } from '~/components/icons';

import { getFocusedRouteNameFromRoute } from '@react-navigation/native';
import { SCREENS_TO_EXCLUDE_FOR_BOTTOM_TABS } from '~/utils/constants';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { marginBottom10 } from '~/styles/spacing';
import TestScreen from '~/screens/TestScreen';
import { safeGetByPrimaryKey } from '~/db/realm/utils/safeRealm';
import { IData } from '~/types/data.types';
import { IUserProfile } from '~/types/users.types';
import { DataSchema } from '~/db/realm/schemas/data.schema';
import ScheduleStack from '~/navigation/ScheduleStack';

const Tab = createBottomTabNavigator();

interface TabItemProps {
  route: any;
  index: number;
  isFocused: boolean;
  options: any;
  onPress: () => void;
  onLongPress: () => void;
}

const TabIcon = ({ name, isFocused }: { name: string; isFocused: boolean }) => {
  const color = isFocused ? colors.red600 : colors.grey550;

  switch (name) {
    case 'RouteStack':
      return <Route color={color} />;
    case 'ScheduleStack':
      return <Route color={color} />;
    case 'ProfileStack':
      return <User color={color} filled={isFocused} />;
    default:
      return <Info />;
  }
};

const TabItem: React.FC<TabItemProps> = ({
  route,
  index,
  isFocused,
  options,
  onPress,
  onLongPress,
}) => {
  const label = options.tabBarLabel ?? options.title ?? route.name;

  const labelStyle = [
    bottomTabLabels,
    isFocused ? primaryText : grayText,
  ] as TextStyle;

  return (
    <TouchableOpacity
      key={`${index}-${route}`}
      accessibilityRole="button"
      accessibilityState={isFocused ? { selected: true } : {}}
      accessibilityLabel={options.tabBarAccessibilityLabel}
      testID={options.tabBarTestID}
      onPress={onPress}
      onLongPress={onLongPress}
      style={isFocused ? styles.activeTab : styles.inactiveTab}>
      <View style={styles.flexBox}>
        <TabIcon name={route.name} isFocused={isFocused} />
        <Text style={labelStyle}>{label}</Text>
      </View>
    </TouchableOpacity>
  );
};

const minimumBottomSpacing = marginBottom10.marginBottom;

function MyTabBar({
  state,
  descriptors,
  navigation,
}: {
  state: any;
  descriptors: any;
  navigation: any;
}) {
  const { bottom } = useSafeAreaInsets();

  const bottomSpacingStyle = {
    bottom:
      bottom === 0
        ? minimumBottomSpacing
        : Platform.select({
            ios: bottom,
            android: bottom + minimumBottomSpacing,
          }),
  };

  return (
    <View style={[styles.navbar, shadowView, bottomSpacingStyle]}>
      {state.routes.map((route: any, index: number) => (
        <TabItem
          key={route.key}
          route={route}
          index={index}
          isFocused={state.index === index}
          options={descriptors[route.key].options}
          onPress={() => {
            const event = navigation.emit({
              type: 'tabPress',
              target: route.key,
            });
            if (state.index !== index && !event.defaultPrevented) {
              navigation.navigate(route.name);
            }
          }}
          onLongPress={() => {
            navigation.emit({
              type: 'tabLongPress',
              target: route.key,
            });
          }}
        />
      ))}
    </View>
  );
}

function BottomTabs() {
  const userId = global.userId;
  const [userData, setUserData] = useState<IUserProfile | null>(null);

  useEffect(() => {
    if (!userId) return;

    const fetchUserData = async () => {
      const data = await safeGetByPrimaryKey<IData<IUserProfile>>(
        DataSchema.name,
        userId,
      );
      if (data) {
        setUserData(data.data);
      }
    };

    fetchUserData();
  }, [userId]);

  const routeName = (props: any) => {
    const routeState = props?.state;
    const selectedRoute = getFocusedRouteNameFromRoute(
      routeState?.routes[routeState?.index],
    );

    return selectedRoute ?? 'RouteStack';
  };

  const runningDev = __DEV__ === true;
  const isRapid = userData?.Email?.includes('@rapidmgmtsvcs.com');
  const isKSolves = userData?.Email?.includes('@ksolves.com');

  const shouldShowTestScreen = runningDev || isRapid || isKSolves;

  return (
    <Tab.Navigator
      id="tabNavigator"
      tabBar={props => {
        return SCREENS_TO_EXCLUDE_FOR_BOTTOM_TABS.includes(
          routeName(props),
        ) ? null : (
          <MyTabBar key="BottomTabBar" {...props} />
        );
      }}
      initialRouteName="ScheduleStack"
      screenOptions={{
        headerShown: false,
      }}>
      {/* <Tab.Screen
        name="RouteStack"
        component={RouteStack}
        options={{ title: 'Routes' }}
      /> */}
      <Tab.Screen
        name="ScheduleStack"
        component={ScheduleStack}
        options={{ title: 'Schedule' }}
      />
      <Tab.Screen
        name="ProfileStack"
        component={ProfileStack}
        options={{ title: 'Profile' }}
      />
      {shouldShowTestScreen && (
        <Tab.Screen
          name="TestScreen"
          component={TestScreen}
          options={{ title: 'Test' }}
        />
      )}
    </Tab.Navigator>
  );
}

const styles = StyleSheet.create({
  navbar: {
    alignSelf: 'center',
    position: 'absolute',
    width: '92%',
    flexDirection: 'row',
    backgroundColor: colors.white,
    borderColor: colors.backgroundLight,
    padding: 2,
    gap: 4,
    borderRadius: 999,
    borderWidth: 1,
  },
  flexBox: {
    paddingVertical: 2,
    alignItems: 'center',
  },
  activeTab: {
    flex: 1,
    borderRadius: 999,
    backgroundColor: colors.red10,
    borderColor: colors.red50,
    borderWidth: 1,
    paddingVertical: 2,
  },
  inactiveTab: {
    flex: 1,
    justifyContent: 'center',
  },
});

export default BottomTabs;
