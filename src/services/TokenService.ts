import { AuthService } from '~/services/AuthService';
import { SentryService } from '~/services/SentryService';
import { KeychainService } from '~/utils/keyChain';
import LogoutService from '~/services/LogoutService';

export interface TokenData {
  accessToken: string;
  refreshToken: string;
  tokenType: string;
}

export interface TokenResponse {
  access_token: string;
  refresh_token: string;
  token_type: string;
}

class TokenService {
  private static instance: TokenService;
  private currentTokens: TokenData | null = null;
  private isRefreshing = false;
  private refreshPromise: Promise<TokenData | null> | null = null;

  static getInstance(): TokenService {
    if (!TokenService.instance) {
      TokenService.instance = new TokenService();
    }
    return TokenService.instance;
  }

  async initialize(): Promise<boolean> {
    try {
      const tokens = await this.getStoredTokens();
      if (tokens) {
        this.currentTokens = tokens;
        return true;
      }
      return false;
    } catch (error) {
      console.error('TokenService: initialize():', error);
      return false;
    }
  }

  async storeTokens(tokenResponse: TokenResponse): Promise<void> {
    try {
      const tokenData: TokenData = {
        accessToken: tokenResponse.access_token,
        refreshToken: tokenResponse.refresh_token,
        tokenType: tokenResponse.token_type,
      };

      this.currentTokens = tokenData;
      await KeychainService.setTokensInKeychain(tokenData);
    } catch (error) {
      console.error('TokenService: storeTokens():', error);
      throw error;
    }
  }

  async getAccessToken(): Promise<string | null> {
    try {
      if (!this.currentTokens) {
        const tokens = await this.getStoredTokens();
        if (!tokens) return null;
        this.currentTokens = tokens;
      }

      return this.currentTokens?.accessToken || null;
    } catch (error) {
      console.error('TokenService: getAccessToken():', error);
      return null;
    }
  }

  async getRefreshToken(): Promise<string | null> {
    try {
      if (!this.currentTokens) {
        const tokens = await this.getStoredTokens();
        if (!tokens) return null;
        this.currentTokens = tokens;
      }

      return this.currentTokens?.refreshToken || null;
    } catch (error) {
      console.error('TokenService: getRefreshToken():', error);
      return null;
    }
  }

  async refreshAccessToken(): Promise<TokenData | null> {
    if (this.isRefreshing && this.refreshPromise !== null) {
      return this.refreshPromise;
    }

    this.isRefreshing = true;
    this.refreshPromise = this.performTokenRefresh();

    try {
      const result = await this.refreshPromise;
      this.currentTokens = result;
      return result;
    } finally {
      this.isRefreshing = false;
      this.refreshPromise = null;
    }
  }

  private async performTokenRefresh(): Promise<TokenData | null> {
    try {
      if (!this.currentTokens?.refreshToken) {
        throw new Error('No refresh token available');
      }

      const response = await AuthService.refreshToken(
        this.currentTokens.refreshToken,
      );

      if (response && response?.data) {
        const { access_token } = response.data;
        const refreshedTokenData: TokenData = {
          ...this.currentTokens,
          accessToken: access_token,
        };
        await KeychainService.setTokensInKeychain(refreshedTokenData);
        return refreshedTokenData;
      }

      await this.handleRefreshFailure();
      return null;
    } catch (error) {
      console.error('TokenService: performTokenRefresh():', error);
      await this.handleRefreshFailure();
      return null;
    }
  }

  private async handleRefreshFailure(): Promise<void> {
    try {
      await SentryService.logSentryError({
        error: new Error('Token refresh failed'),
        tags: {
          file: 'TokenService.ts',
          function: 'handleRefreshFailure',
          eventType: 'logout_triggered',
        },
        level: 'warning',
      });

      await LogoutService.logoutWithoutDeletingData();
    } catch (logoutError) {
      console.error('TokenService: handleRefreshFailure():', logoutError);
    }
  }

  private async getStoredTokens(): Promise<TokenData | null> {
    try {
      const result = await KeychainService.getTokenFromKeychain();
      if (result && result.password) {
        return JSON.parse(result.password) as TokenData;
      }
      return null;
    } catch (error) {
      console.error('TokenService: getStoredTokens():', error);
      return null;
    }
  }

  async clearTokens(): Promise<void> {
    try {
      this.currentTokens = null;
      await KeychainService.removeTokenFromKeychain();
    } catch (error) {
      console.error('TokenService: clearTokens():', error);
    }
  }
}

export default TokenService.getInstance();
