import Realm from 'realm';
import en from '~/localization/en';

import { Stop } from '~/types/stops.types';
import { getRealmInstance } from '~/db/realm/utils/realm.config';
import { RouteSummary } from '~/types/routes.types';
import { interpolateString } from '~/utils/strings';
import { ROUTE_STATUS, STOP_STATUS } from '~/utils/constants';
import { StopSchema } from '~/db/realm/schemas/stop.schema';
import { RouteSummarySchema } from '~/db/realm/schemas/route-summary.schema';
import { ActionResult } from '~/types/sync.types';
import { ParcelSchema } from '~/db/realm/schemas/parcel.schema';
import { Parcel } from '~/types/parcel.types';
import { RequestType } from '~/types/request-queue.types';
import uuid from 'react-native-uuid';
import { updateEntity } from '~/db/realm/operations';
import { addToRequestQueue } from '~/services/sync/syncUp';
import { EntityName } from '~/db/realm/utils/constants';
import { RequestQueueSchema } from '~/db/realm/schemas/request-queue.schema';

export const DriverActions = {
  markStopArrival: async (stopId: string): Promise<ActionResult | null> => {
    let realm: Realm | null = null;
    try {
      realm = await getRealmInstance();
      if (!realm || realm.isClosed) return null;
      const stop = realm.objectForPrimaryKey<Stop>(StopSchema.name, stopId);

      if (!stop) {
        return { success: false, message: en.stop_not_found };
      }

      if (validateTime(stop.Arrival_Time__c)) {
        return {
          success: false,
          message: en.already_marked_arrived,
        };
      }

      return updateStopAndQueue({
        stop,
        payload: {
          Arrival_Time__c: new Date().toISOString(),
          Status__c: STOP_STATUS.ARRIVED,
        },
      });
    } catch (error) {
      console.error('driverActions.ts: markStopArrival:', error);
      return { success: false, message: en.stop_update_failed };
    }
  },

  markStopCompletion: async (
    stopId: string,
    payload: Record<string, any>,
  ): Promise<ActionResult | null> => {
    let realm: Realm | null = null;

    try {
      realm = await getRealmInstance();
      if (!realm || realm.isClosed) return null;
      const stop = realm.objectForPrimaryKey<Stop>(StopSchema.name, stopId);

      if (!stop) {
        return { success: false, message: en.stop_not_found };
      }

      if (!validateTime(stop.Arrival_Time__c)) {
        return { success: false, message: en.must_arrive_first };
      }

      if (validateTime(stop.Completed_Time__c)) {
        return { success: false, message: en.already_marked_completed };
      }

      return updateStopAndQueue({
        stop,
        payload: {
          ...payload,
          Status__c: STOP_STATUS.COMPLETE,
          Completed_Time__c: new Date().toISOString(),
        },
      });
    } catch (error) {
      console.error('markStopCompletion error:', error);
      return { success: false, message: en.stop_update_failed };
    }
  },
};

export const validateTime = (dateString?: string | null): boolean => {
  if (!dateString) {
    return false;
  }

  const currentTime = new Date().getTime();
  const timeToValidate = new Date(dateString).getTime();
  return currentTime >= timeToValidate;
};

export const markRouteCompleted = async (
  route: RouteSummary,
): Promise<ActionResult> => {
  return updateRoute(route.Id, {
    Status__c: ROUTE_STATUS.Complete,
    End_Time__c: new Date().toISOString(),
  });
};

const markRouteStarted = async (route: RouteSummary): Promise<ActionResult> => {
  return updateRoute(route.Id, {
    Status__c: ROUTE_STATUS.InProgress,
    Start_Time__c: new Date().toISOString(),
  });
};

const updateStopAndQueue = async ({
  stop,
  payload,
}: {
  stop: Stop;
  payload: Record<string, any>;
}): Promise<ActionResult> => {
  try {
    if (payload.Status__c === STOP_STATUS.COMPLETE) {
      const { success, message } = await checkAndCreateParcelsRequest(stop.Id);

      if (!success) {
        return { success, message };
      }
    }

    const stopId = stop.Id;
    const routeId = stop.Summary__c;
    const payloadWithPrimaryKey = { Id: stopId, ...payload };

    const updateSuccess = await updateEntity({
      entityId: stopId,
      entityName: EntityName.STOP,
      updates: payloadWithPrimaryKey,
    });

    if (!updateSuccess) {
      return { success: false, message: en.stop_update_failed };
    }

    const wasQueued = await addToRequestQueue({
      entityId: stopId,
      entityName: EntityName.STOP,
      updates: payload,
    });

    if (!wasQueued) {
      return { success: false, message: en.stop_failed_to_queue };
    }

    await updateRouteStatusFromStop({ payload, routeId });

    return {
      success: true,
      message: interpolateString(en.stop_updated, {
        field: Object.keys(payload)[0],
      }),
    };
  } catch (error) {
    console.error('updateStopAndQueue error:', error);
    return { success: false, message: en.stop_update_failed };
  }
};

const updateRoute = async (
  routeId: string,
  payload: Record<string, any>,
): Promise<ActionResult> => {
  let realm: Realm | null = null;
  try {
    realm = await getRealmInstance();
    if (!realm || realm.isClosed)
      return { success: false, message: en.database_connection_failed };
    const routeToUpdate = realm.objectForPrimaryKey(
      RouteSummarySchema.name,
      routeId,
    );

    if (!routeToUpdate) {
      return { success: false, message: en.route_not_found };
    }

    const updatedRoute = { ...routeToUpdate, ...payload };

    const updateSuccess = await updateEntity({
      entityId: routeId,
      entityName: EntityName.ROUTE_SUMMARY,
      updates: updatedRoute,
    });

    if (!updateSuccess) {
      return { success: false, message: 'route_update_failed' };
    }

    return {
      success: true,
      message: interpolateString(en.route_updated, {
        field: Object.keys(payload)[0],
      }),
    };
  } catch (error) {
    console.error('updateRoute error:', error);
    return { success: false, message: 'route_update_failed' };
  }
};

const updateRouteStatusFromStop = async ({
  routeId,
  payload,
}: {
  routeId: string;
  payload: Record<string, any>;
}) => {
  if (!routeId) return;
  let realm: Realm | null = null;

  try {
    realm = await getRealmInstance();
    if (!realm || realm.isClosed) return;
    const route = realm.objectForPrimaryKey<RouteSummary>(
      RouteSummarySchema.name,
      routeId,
    );
    if (!route) return;

    const routeStops = realm
      .objects(StopSchema.name)
      .filtered('Summary__c == $0', routeId);

    // Check if this is the first stop being marked with arrival time
    const hasNoOtherArrivals = routeStops.every(
      stop => !stop.Arrival_Time__c || stop.Id === payload.stopId,
    );

    if (hasNoOtherArrivals && payload.Arrival_Time__c) {
      await markRouteStarted(route);
    }

    // Check if all stops are completed to mark route as completed
    if (payload?.Status__c === STOP_STATUS.COMPLETE) {
      const allStopsCompleted = routeStops.every(
        stop => stop.Status__c === STOP_STATUS.COMPLETE,
      );

      if (allStopsCompleted) {
        await markRouteCompleted(route);
      }
    }
  } catch (error) {
    console.error('updateRouteStatusFromStop error:', error);
  }
};

const checkAndCreateParcelsRequest = async (
  stopId: string,
): Promise<ActionResult> => {
  let realm: Realm | null = null;

  try {
    realm = await getRealmInstance();
    if (!realm || realm.isClosed)
      return { success: false, message: en.database_connection_failed };
    const pickupParcels = realm
      .objects<Partial<Parcel>>(ParcelSchema.name)
      .filtered('Pickup__c == $0', stopId);

    const deliveryOnlyParcels = realm
      .objects<Partial<Parcel>>(ParcelSchema.name)
      .filtered('Pickup__c == "" AND Delivery__c == $0', stopId);

    const allParcelsOfStop = [...pickupParcels, ...deliveryOnlyParcels];

    if (allParcelsOfStop.length === 0) {
      return { success: true, message: en.no_parcels_picked_at_stop };
    }

    const parcelsToCreate = allParcelsOfStop.filter(
      parcel => parcel.Work_Order__c === null,
    );

    const parcelsToUpdate = allParcelsOfStop.filter(
      parcel => parcel.Work_Order__c !== null,
    );

    if (parcelsToCreate.length > 0) {
      const parcelsPayload = parcelsToCreate.reduce<{
        allOrNone: boolean;
        records: Array<Record<string, any>>;
      }>(
        (currentPayload, parcel) => {
          const {
            // NOTE: Removing this field for successful parcel creation

            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            Parcel_Type_Name__c,
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            Work_Order__c,
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            Work_Order__r,
            ...parcelData
          } = parcel;

          currentPayload.records.push({
            attributes: { type: 'Parcel__c' },
            ...parcelData,
          });

          return currentPayload;
        },
        { allOrNone: false, records: [] },
      );

      const entityId = uuid.v4() as string;

      realm.beginTransaction();

      realm.create(
        RequestQueueSchema.name,
        {
          entityId: entityId,
          entityName: EntityName.PARCEL,
          requestType: RequestType.NEW,
          payload: parcelsPayload,
        },
        Realm.UpdateMode.Modified,
      );

      realm.commitTransaction();
    }

    if (parcelsToUpdate.length > 0) {
      const parcelsPayload = parcelsToUpdate.reduce<{
        allOrNone: boolean;
        records: Array<Record<string, any>>;
      }>(
        (currentPayload, parcel) => {
          const {
            // NOTE: Removing this field for successful parcel creation
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            Parcel_Type_Name__c,
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            Work_Order__c,
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            Work_Order__r,
            ...parcelData
          } = parcel;

          const updatedParcelData = {
            ...parcelData,
            Pickup__c:
              parcelData.Pickup__c === '' ? null : parcelData.Pickup__c,
          };

          currentPayload.records.push({
            attributes: { type: 'Parcel__c' },
            ...updatedParcelData,
          });

          return currentPayload;
        },
        { allOrNone: false, records: [] },
      );

      const entityId = uuid.v4() as string;

      realm.beginTransaction();

      realm.create(
        RequestQueueSchema.name,
        {
          entityId: entityId,
          entityName: EntityName.PARCEL,
          requestType: RequestType.UPDATE,
          payload: parcelsPayload,
        },
        Realm.UpdateMode.Modified,
      );

      realm.commitTransaction();
    }

    return {
      success: true,
      message: interpolateString(en.parcels_created, {
        count: allParcelsOfStop.length,
        stopId: stopId,
      }),
    };
  } catch (error) {
    console.error('driverActions.ts: checkAndCreateParcels:', error);
    return { success: false, message: en.parcel_create_failed };
  }
};
