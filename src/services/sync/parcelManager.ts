import { Parc<PERSON> } from '~/types/parcel.types';
import { ParcelSchema } from '~/db/realm/schemas/parcel.schema';
import { getRealmInstance } from '~/db/realm/utils/realm.config';
import uuid from 'react-native-uuid';
import en from '~/localization/en';
import { ActionResult } from '~/types/sync.types';
import { safeWrite } from '~/db/realm/utils/safeRealm';

export const ParcelManager = {
  createParcel: async (
    parcel: Partial<Parcel>,
  ): Promise<ActionResult & { parcelId?: string }> => {
    try {
      const realm = await getRealmInstance();
      if (!realm || realm.isClosed)
        return { success: false, message: en.parcel_create_failed };

      let existingParcel: Parcel | undefined;
      if (parcel.Work_Order__c && parcel.Reference__c) {
        existingParcel = realm
          .objects<Parcel>(ParcelSchema.name)
          .filtered(
            'Work_Order__c == $0 && Reference__c == $1',
            parcel.Work_Order__c,
            parcel.Reference__c,
          )[0];
      }

      if (existingParcel) {
        safeWrite(realm, () => {
          realm.create(
            ParcelSchema.name,
            { ...existingParcel, ...parcel },
            true,
          );
        });
        return {
          parcelId: existingParcel.Id,
          success: true,
          message: en.parcel_saved,
        };
      } else {
        const parcelId = uuid.v4() as string;

        safeWrite(realm, () => {
          realm.create(ParcelSchema.name, { ...parcel, Id: parcelId }, true);
        });

        return {
          parcelId,
          success: true,
          message: en.parcel_saved,
        };
      }
    } catch (error) {
      console.error('ParcelManager: createParcel(): ', error);
      return {
        success: false,
        message: en.parcel_create_failed,
      };
    }
  },

  updateParcel: async (
    parcelId: string,
    updates: Partial<Parcel>,
  ): Promise<ActionResult> => {
    try {
      const realm = await getRealmInstance();
      if (!realm || realm.isClosed)
        return { success: false, message: en.parcel_update_failed };

      const parcel = realm.objectForPrimaryKey<Parcel>(
        ParcelSchema.name,
        parcelId,
      );
      if (!parcel) {
        return { success: false, message: en.parcel_not_found };
      }

      safeWrite(realm, () => {
        Object.assign(parcel, updates);
      });

      return { success: true, message: en.parcel_saved };
    } catch (error) {
      console.error('ParcelManager: updateParcel(): ', error);
      return { success: false, message: en.parcel_update_failed };
    }
  },

  deleteParcel: async (parcelId: string): Promise<ActionResult> => {
    try {
      const realm = await getRealmInstance();
      if (!realm || realm.isClosed)
        return { success: false, message: en.parcel_delete_failed };
      const parcel = realm.objectForPrimaryKey<Parcel>(
        ParcelSchema.name,
        parcelId,
      );

      if (!parcel) {
        return {
          success: false,
          message: en.parcel_not_found,
        };
      }

      safeWrite(realm, () => {
        realm.delete(parcel);
      });

      return {
        success: true,
        message: en.parcel_deleted,
      };
    } catch (error) {
      console.error('ParcelManager: deleteParcel(): ', error);
      return {
        success: false,
        message: en.parcel_delete_failed,
      };
    }
  },
};
