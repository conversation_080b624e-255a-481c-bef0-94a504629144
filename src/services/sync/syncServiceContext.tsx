import React, { createContext, ReactNode, useContext, useMemo } from 'react';
import { SYNC_STATUS, SyncStatus } from '~/hooks/useSync';

type SyncServiceContextType = {
  syncUp: () => void;
  syncDown: () => void;
  status: SyncStatus;
};

type SyncServiceProviderProps = {
  children: ReactNode;
} & SyncServiceContextType;

export const SyncServiceContext = createContext<SyncServiceContextType>({
  syncUp: () => {},
  syncDown: () => {},
  status: SYNC_STATUS.IDLE,
});

export const SyncServiceProvider = ({
  children,
  syncUp,
  syncDown,
  status,
}: SyncServiceProviderProps) => {
  const value = useMemo(
    () => ({ syncUp, syncDown, status }),
    [syncUp, syncDown, status],
  );

  return (
    <SyncServiceContext.Provider value={value}>
      {children}
    </SyncServiceContext.Provider>
  );
};

export const useSyncService = () => {
  return useContext(SyncServiceContext);
};
