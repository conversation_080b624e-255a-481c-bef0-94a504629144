import { <PERSON><PERSON>, InteractionManager } from 'react-native';
import { postData } from '~/api/apiService';
import { API_ENDPOINT_KEYS } from '~/api/apiEndpoints';
import {
  IRequestQueue,
  RequestQueueStatus,
  RequestType,
} from '~/types/request-queue.types';
import { RequestQueueSchema } from '~/db/realm/schemas/request-queue.schema';
import { SyncResult, UpdateEntityProps } from '~/types/sync.types';
import { Parcel } from '~/types/parcel.types';
import { ParcelSchema } from '~/db/realm/schemas/parcel.schema';
import { ImageSchema } from '~/db/realm/schemas/image.schema';
import { IImage } from '~/types/image.types';
import { STOP_STATUS } from '~/utils/constants';
import { StopSchema } from '~/db/realm/schemas/stop.schema';
import { Stop } from '~/types/stops.types';
import { getRealmInstance } from '~/db/realm/utils/realm.config';
import { EntityName } from '~/db/realm/utils/constants';
import { RouteSummary } from '~/types/routes.types';
import { RouteSummarySchema } from '~/db/realm/schemas/route-summary.schema';
import { safeWrite } from '~/db/realm/utils/safeRealm';
import { SentryService } from '~/services/SentryService';
import { ApiError } from '~/types/error.types';

const BATCH_SIZE = 5;

export async function syncUp(
  pendingRequests: IRequestQueue[],
): Promise<SyncResult[]> {
  try {
    const results = await processAllBatches(pendingRequests);
    const realm = await getRealmInstance();
    if (!realm || realm.isClosed) return [];

    safeWrite(realm, () => {
      const successfulRequests = realm
        .objects(RequestQueueSchema.name)
        .filtered(
          'entityId IN $0',
          results.map(result => result.entityId),
        );
      realm.delete(successfulRequests);
    });

    return results;
  } catch (error) {
    console.error('syncUp.ts: syncUp(): Error syncing requests:', error);
    Alert.alert(`Sync up error: ${JSON.stringify(error)}`);
    return [];
  }
}

async function processAllBatches(
  pendingRequests: IRequestQueue[],
): Promise<SyncResult[]> {
  const syncResults: SyncResult[] = [];

  const processBatch = async (startIndex: number): Promise<void> => {
    return new Promise(resolve => {
      InteractionManager.runAfterInteractions(async () => {
        const batch = pendingRequests.slice(
          startIndex,
          startIndex + BATCH_SIZE,
        );

        if (batch.length === 0) {
          resolve();
          return;
        }

        const results = await processBatchOfRequests(batch);
        syncResults.push(...results);

        await processBatch(startIndex + BATCH_SIZE);
        resolve();
      });
    });
  };

  await processBatch(0);
  return syncResults;
}

async function processBatchOfRequests(
  batch: IRequestQueue[],
): Promise<SyncResult[]> {
  try {
    const batchPromises = batch.map(request => syncSingleRequest(request));
    const results = await Promise.allSettled(batchPromises);
    return results
      .map(result => {
        if (result.status === 'fulfilled') {
          return result.value;
        }
        return null;
      })
      .filter(result => result !== null);
  } catch (error) {
    console.error(
      'syncUp.ts: processBatchOfRequests():  Error processing batch:',
      error,
    );
    return [];
  }
}

const logSyncError = async ({
  error,
  request,
  response,
}: {
  error: any;
  request: IRequestQueue;
  response: any;
}) => {
  const { entityName, entityId, requestType, payload } = request;

  await SentryService.logSentryError({
    error,
    tags: {
      file: 'syncUp.ts',
      function: 'handleSyncResponse',
      entity: entityName,
      requestType,
    },
    extra: {
      payload,
      response,
    },
    contexts: {
      syncMeta: {
        entityId,
        entityName,
        requestType,
        timestamp: new Date().toISOString(),
      },
    },
    level: 'error',
  });

  console.error(
    `syncUp.ts: logSyncError():  Sync Error ${entityName}: ${entityId}`,
    JSON.stringify(error),
  );
};

const handleSyncResponse = ({
  request,
  resolve,
  reject,
}: {
  request: IRequestQueue;
  resolve: (value: SyncResult) => void;
  reject: (reason?: any) => void;
}) => {
  return async function (response: any, error: any) {
    try {
      const realm = await getRealmInstance();
      if (!realm || realm.isClosed) return;
      if (error) {
        safeWrite(realm, () => {
          request.status = RequestQueueStatus.FAILED;
          request.retryCount = request.retryCount + 1;
          request.lastUpdatedAt = new Date();
        });

        await logSyncError({ error, request, response });

        reject(null);
      } else {
        if (request.entityName === EntityName.PARCEL) {
          updateParcelWithSalesforceId({ request, response });
        } else if (request.entityName === EntityName.STOP) {
          updateStopImageStatus({ request });
        } else if (request.entityName === EntityName.ROUTE_SUMMARY) {
          updateRouteImageStatus({ request });
        }
        console.info(
          `Sync Response: ${request.entityName}: ${request.entityId}`,
          JSON.stringify(response),
        );
        resolve({
          entityId: request.entityId,
          entityName: request.entityName,
        });
      }
    } catch (syncError) {
      console.error(
        'syncUp.ts: handleSyncResponse(): Error getting realm:',
        syncError,
      );
    }
  };
};

async function syncSingleRequest(request: IRequestQueue): Promise<SyncResult> {
  return new Promise((resolve, reject) => {
    const responseHandler = handleSyncResponse({
      request,
      resolve,
      reject,
    });

    switch (request.entityName) {
      case EntityName.PARCEL:
        const payload =
          request.requestType === 'new'
            ? {
                ...request.payload,
                records:
                  request.payload.records?.map(
                    // eslint-disable-next-line @typescript-eslint/no-unused-vars
                    ({ Id, ...record }: any) => record,
                  ) || [],
              }
            : request.payload;

        postData({
          onSync: responseHandler,
          requestIdentifier: API_ENDPOINT_KEYS.POST_BATCH_SOBJECTS,
          requestData: payload,
          method: request.requestType === 'update' ? 'patch' : 'post',
        });

        break;

      case EntityName.STOP:
        postData({
          onSync: responseHandler,
          requestIdentifier: API_ENDPOINT_KEYS.PATCH_STOP,
          requestData: {
            ...(request.payload as object),
            entityId: request.entityId,
          },
          method: 'patch',
        });
        break;

      case EntityName.ROUTE_SUMMARY:
        postData({
          onSync: responseHandler,
          requestIdentifier: API_ENDPOINT_KEYS.PATCH_ROUTE_SUMMARY,
          requestData: {
            ...(request.payload as object),
            entityId: request.entityId,
          },
          method: 'patch',
        });
        break;
      default:
    }
  });
}

async function updateParcelWithSalesforceId({
  request,
  response,
}: {
  request: IRequestQueue;
  response: { id: string; success: boolean; errors: ApiError[] }[];
}) {
  try {
    const realm = await getRealmInstance();
    if (!realm || realm.isClosed) return;

    request.payload?.records?.forEach(
      ({ Id: localId }: Parcel, index: number) => {
        const { id: remoteId, success, errors } = response?.[index] || {};

        if (success) {
          const parcelWithLocalId = realm.objectForPrimaryKey<Parcel>(
            ParcelSchema.name,
            localId,
          );

          // Update images and parcel in a single transaction
          safeWrite(realm, () => {
            // Update images linked to parcel
            const imagesLinkedToParcel = realm
              .objects<IImage>(ImageSchema.name)
              .filtered(`ParcelId == $0`, localId);
            imagesLinkedToParcel.forEach(image => {
              image.PathOnClient = image.PathOnClient.replace(
                localId,
                remoteId,
              );
              image.ParcelId = remoteId;
              image.isSyncReady = true;
            });

            // Re-create parcel with remoteId as primary key cannot be modified
            const newParcel = { ...parcelWithLocalId, Id: remoteId };
            realm.create(ParcelSchema.name, newParcel, true);

            // Delete old parcel
            if (parcelWithLocalId) {
              realm.delete(parcelWithLocalId);
            }
          });
        } else {
          SentryService.logSentryError({
            error: JSON.stringify(errors ?? ['Unable to update parcel']),
            tags: {
              file: 'syncUp.ts',
              function: 'updateParcelWithSalesforceId',
            },
          });
        }
      },
    );
  } catch (error) {
    console.error(
      'syncUp.ts: updateParcelWithSalesforceId(): Error getting realm:',
      error,
    );
  }
}

async function updateRouteImageStatus({ request }: { request: IRequestQueue }) {
  try {
    const realm = await getRealmInstance();
    if (!realm || realm.isClosed) return;
    const route = realm.objectForPrimaryKey<RouteSummary>(
      RouteSummarySchema.name,
      request.entityId,
    );

    const routeIsCompleted = request.payload.Survey_Complete__c === true;

    if (!routeIsCompleted || !route) return;

    // Mark images linked to route as ready for sync
    const imagesLinkedToRoute = realm
      .objects<IImage>(ImageSchema.name)
      .filtered(
        `RouteSummaryId == $0 && StopId == null && ParcelId == null`,
        route.Id,
      );

    safeWrite(realm, () => {
      imagesLinkedToRoute.forEach(image => {
        image.isSyncReady = true;
      });
    });
  } catch (error) {
    console.error(
      'syncUp.ts: updateRouteImageStatus(): Error getting realm:',
      error,
    );
  }
}

async function updateStopImageStatus({ request }: { request: IRequestQueue }) {
  try {
    const realm = await getRealmInstance();
    if (!realm || realm.isClosed) return;

    const stop = realm.objectForPrimaryKey<Stop>(
      StopSchema.name,
      request.entityId,
    );

    const stopIsCompleted = request.payload.Status__c === STOP_STATUS.COMPLETE;

    if (!stopIsCompleted || !stop) return;

    const imagesToUpdate = getImagesLinkedToStop(realm, stop);

    // Mark images as ready for sync
    markImagesAsSyncReady(realm, imagesToUpdate);
  } catch (error) {
    console.error(
      'syncUp.ts: updateStopImageStatus(): Error getting realm:',
      error,
    );
  }
}

/**
 * Gets all images linked to a stop that should be marked as sync ready
 */
function getImagesLinkedToStop(realm: Realm, stop: Stop): IImage[] {
  const imagesLinkedToStop = realm
    .objects<IImage>(ImageSchema.name)
    .filtered(
      `ParcelId == null && (StopId == $0 OR RouteSummaryId == $1 OR (StopId == $0 AND RouteSummaryId == $1))`,
      stop.Id,
      stop.Summary__c,
    );

  const imagesLinkedToProtocol = realm
    .objects<IImage>(ImageSchema.name)
    .filtered(
      `ParcelId == null && StopId == $0 && DailyScheduleId == $1`,
      stop.Id,
      stop.Daily_Schedule__c,
    );

  return [...imagesLinkedToStop, ...imagesLinkedToProtocol];
}

/**
 * Marks the provided images as ready for sync within a safe write transaction
 */
function markImagesAsSyncReady(realm: Realm, images: IImage[]): void {
  if (images.length === 0) {
    return;
  }

  safeWrite(realm, () => {
    images.forEach(image => {
      image.isSyncReady = true;
    });
  });
}

export const addToRequestQueue = async ({
  entityId,
  entityName,
  updates,
  requestType = RequestType.UPDATE,
}: UpdateEntityProps): Promise<boolean> => {
  try {
    const realm = await getRealmInstance();
    if (!realm || realm.isClosed) return false;
    // Check if entityName is not Parcel__c
    const existingRequest = realm.objectForPrimaryKey(
      RequestQueueSchema.name,
      entityId,
    );

    const payload = existingRequest
      ? { ...(existingRequest.payload as object), ...updates }
      : updates;

    safeWrite(realm, () => {
      realm.create(
        RequestQueueSchema.name,
        { entityId, entityName, requestType, payload },
        true,
      );
    });

    return true;
  } catch (error) {
    console.error(
      `syncUp.ts: addToRequestQueue(): Failed to add ${entityName}: ${entityId} updating entity:`,
      error,
    );
    return false;
  }
};
