import { API_ENDPOINT_KEYS } from '~/api/apiEndpoints';
import { fetchData } from '~/api/apiService';
import { RouteResponse } from '~/types/sync.types';
import { DataSchema } from '~/db/realm/schemas/data.schema';
import { getRealmInstance } from '~/db/realm/utils/realm.config';
import { IUserProfile } from '~/types/users.types';
import { ParcelTypeResponse } from '~/types/parcel.types';
import { ServiceTypeResponse } from '~/types/service.types';
import { safeWrite } from '~/db/realm/utils/safeRealm';

/**
 * Synchronizes user routes and their associated stops.
 * @returns Promise with sync result containing success status and error
 */
export async function syncDown(): Promise<boolean> {
  try {
    await fetchUserData();
    await fetchDailySchedules();
    await fetchProtocolVerifications();
    await new Promise<RouteResponse>((resolve, reject) => {
      fetchData({
        onSync: (response, error) => {
          if (error) {
            const errorInstance =
              (error as any) instanceof Error
                ? error
                : new Error(String(error));
            reject(errorInstance);
          } else if (response) {
            resolve(response as RouteResponse);
          } else {
            reject(new Error('No response'));
          }
        },
        requestIdentifier: API_ENDPOINT_KEYS.GET_ROUTE_LIST,
      });
    });

    return true;
  } catch (error) {
    console.error(`syncDown.ts: Sync down: ${JSON.stringify(error)}`);
    return false;
  }
}

async function fetchUserData(): Promise<void> {
  if (!global.userId) {
    console.error('syncDown.ts: processRouteListResponse(): User id not set');
    return;
  }

  const userId = global.userId as string;

  fetchData({
    requestIdentifier: API_ENDPOINT_KEYS.GET_CONTACT_BY_USER_ID,
    requestData: { userId },
    onSync: (response, error) => {
      if (error) {
        console.error(
          'syncDown.ts: processRouteListResponse(): Fetching user data: ',
          error,
        );
      } else if (response) {
        const userRecords = (response?.records as IUserProfile[]) || [];
        handleUserResponse(userRecords);

        const accountId = userRecords[0]?.AccountId;

        if (accountId) {
          // TODO: Improve where this is called from
          fetchParcelAndServiceTypes({ accountId });
        }
      }
    },
  });
}

async function handleUserResponse(userRecords: IUserProfile[]) {
  try {
    const realm = await getRealmInstance();
    if (!realm || realm.isClosed) return;
    const { Name, Email, OwnerId, Id, AccountId, Salesforce_Account__c } =
      userRecords[0] as IUserProfile;

    const userData = {
      Name,
      Email,
      OwnerId,
      Id,
      AccountId,
      Salesforce_Account__c,
    };

    safeWrite(realm, () => {
      realm.create(
        DataSchema.name,
        { _id: Salesforce_Account__c, data: userData },
        true,
      );
    });
  } catch (error) {
    console.error('syncDown.ts: handleUserResponse:', error);
  }
}

async function fetchParcelAndServiceTypes({
  accountId,
}: {
  accountId: string;
}): Promise<void> {
  try {
    await new Promise<ParcelTypeResponse>((resolve, reject) => {
      fetchData({
        onSync: (response, error) => {
          if (error) {
            const errorInstance =
              (error as any) instanceof Error
                ? error
                : new Error(String(error));
            reject(errorInstance);
          } else if (response) {
            resolve(response as ParcelTypeResponse);
          } else {
            reject(new Error('No response'));
          }
        },
        requestIdentifier: API_ENDPOINT_KEYS.GET_PARCEL_TYPES,
        requestData: { accountId },
      });
    });
  } catch (error) {
    console.error(
      `syncDown.ts: fetchParcelAndServiceTypes: parcelTypeRecords(): ${JSON.stringify(error)}`,
    );
  }

  try {
    await new Promise<ServiceTypeResponse>((resolve, reject) => {
      fetchData({
        onSync: (response, error) => {
          if (error) {
            const errorInstance =
              (error as any) instanceof Error
                ? error
                : new Error(String(error));
            reject(errorInstance);
          } else if (response) {
            resolve(response as ServiceTypeResponse);
          } else {
            reject(new Error('No response'));
          }
        },
        requestIdentifier: API_ENDPOINT_KEYS.GET_SERVICE_TYPES,
        requestData: { accountId },
      });
    });
  } catch (error) {
    console.error(
      `syncDown.ts: fetchShipmentAndServiceTypes: serviceTypeRecords(): ${JSON.stringify(error)}`,
    );
  }
}

async function fetchDailySchedules(): Promise<void> {
  const userId = global.userId as string;
  fetchData({
    requestIdentifier: API_ENDPOINT_KEYS.GET_TODAYS_SCHEDULE,
    requestData: { userId },
    onSync: (_response, error) => {
      if (error) {
        console.error(
          'syncDown.ts: fetchDailySchedules(): Fetching schedule data: ',
          error,
        );
      }
    },
  });
}

async function fetchProtocolVerifications(): Promise<void> {
  const userId = global.userId as string;

  fetchData({
    requestIdentifier: API_ENDPOINT_KEYS.GET_PROTOCOL_VERIFICATIONS,
    requestData: { userId },
    onSync: (_response, error) => {
      if (error) {
        console.error(
          'syncDown.ts: fetchProtocols(): Fetching protocols data: ',
          error,
        );
      }
    },
  });
}
