/**
 * AsyncStorage Service
 * Note :: To use this service, we will need to add the below lines in package.json and after run yarn install to add the package.
 * "@react-native-async-storage/async-storage": "^2.1.0",
 * "@react-native-async-storage/root": "react-native-async-storage/async-storage",
 *
 * This service provides utility methods for managing persistent storage in React Native applications.
 * It uses `@react-native-async-storage/async-storage` for storing, retrieving, and managing data.
 *
 * Features:
 * - Type-safe getter and setter functions for various data types (string, number, boolean, object, array, etc.)
 * - Error handling to ensure robust and predictable behavior
 * - Methods for clearing or removing specific keys
 *
 * Usage Example:
 * ```
 * import AsyncStorageService from './AsyncStorageService';
 *
 * // Set a string value
 * await AsyncStorageService.setString('username', 'john_doe');
 *
 * // Get a string value
 * const username = await AsyncStorageService.getString('username');
 *
 * // Remove a value
 * await AsyncStorageService.removeItem('username');
 * ```
 */

// import AsyncStorage from '@react-native-async-storage/async-storage';

// class AsyncStorageService {
//   /**
//    * Save a string value in AsyncStorage.
//    * @param key - The key under which the value will be stored.
//    * @param value - The string value to store.
//    */
//   static async setString(key: string, value: string): Promise<void> {
//     try {
//       await AsyncStorage.setItem(key, value);
//     } catch (error) {
//       console.error(
//         `storage-service.ts: Error setting string for key "${key}": "${error}"`,
//         error,
//       );
//     }
//   }

//   /**
//    * Retrieve a string value from AsyncStorage.
//    * @param key - The key of the value to retrieve.
//    * @returns The stored string value, or null if not found.
//    */
//   static async getString(key: string): Promise<string | null> {
//     try {
//       return await AsyncStorage.getItem(key);
//     } catch (error) {
//       console.error(
//         `storage-service.ts: Error retrieving string for key "${key}": "${error}"`,
//         error,
//       );
//       return null;
//     }
//   }

//   /**
//    * Save a number in AsyncStorage.
//    * @param key - The key under which the value will be stored.
//    * @param value - The number to store.
//    */
//   static async setNumber(key: string, value: number): Promise<void> {
//     try {
//       await AsyncStorage.setItem(key, value.toString());
//     } catch (error) {
//       console.error(
//         `storage-service.ts: Error setting number for key "${key}": "${error}"`,
//         error,
//       );
//     }
//   }

//   /**
//    * Retrieve a number from AsyncStorage.
//    * @param key - The key of the value to retrieve.
//    * @returns The stored number, or null if not found.
//    */
//   static async getNumber(key: string): Promise<number | null> {
//     try {
//       const value = await AsyncStorage.getItem(key);
//       return value !== null ? parseFloat(value) : null;
//     } catch (error) {
//       console.error(
//         `storage-service.ts: Error retrieving number for key "${key}": "${error}"`,
//         error,
//       );
//       return null;
//     }
//   }

//   /**
//    * Save a boolean in AsyncStorage.
//    * @param key - The key under which the value will be stored.
//    * @param value - The boolean to store.
//    */
//   static async setBoolean(key: string, value: boolean): Promise<void> {
//     try {
//       await AsyncStorage.setItem(key, JSON.stringify(value));
//     } catch (error) {
//       console.error(
//         `storage-service.ts: Error setting boolean for key "${key}": "${error}"`,
//         error,
//       );
//     }
//   }

//   /**
//    * Retrieve a boolean from AsyncStorage.
//    * @param key - The key of the value to retrieve.
//    * @returns The stored boolean, or null if not found.
//    */
//   static async getBoolean(key: string): Promise<boolean | null> {
//     try {
//       const value = await AsyncStorage.getItem(key);
//       return value !== null ? JSON.parse(value) : null;
//     } catch (error) {
//       console.error(
//         `storage-service.ts: Error retrieving boolean for key "${key}": "${error}"`,
//         error,
//       );
//       return null;
//     }
//   }

//   /**
//    * Save an object in AsyncStorage.
//    * @param key - The key under which the value will be stored.
//    * @param value - The object to store.
//    */
//   static async setObject<T>(key: string, value: T): Promise<void> {
//     try {
//       await AsyncStorage.setItem(key, JSON.stringify(value));
//     } catch (error) {
//       console.error(
//         `storage-service.ts: Error setting object for key "${key}": "${error}"`,
//         error,
//       );
//     }
//   }

//   /**
//    * Retrieve an object from AsyncStorage.
//    * @param key - The key of the value to retrieve.
//    * @returns The stored object, or null if not found.
//    */
//   static async getObject<T>(key: string): Promise<T | null> {
//     try {
//       const value = await AsyncStorage.getItem(key);
//       return value !== null ? JSON.parse(value) : null;
//     } catch (error) {
//       console.error(
//         `storage-service.ts: Error retrieving object for key "${key}": "${error}"`,
//         error,
//       );
//       return null;
//     }
//   }

//   /**
//    * Save an array in AsyncStorage.
//    * @param key - The key under which the value will be stored.
//    * @param value - The array to store.
//    */
//   static async setArray<T>(key: string, value: T[]): Promise<void> {
//     try {
//       await AsyncStorage.setItem(key, JSON.stringify(value));
//     } catch (error) {
//       console.error(
//         `storage-service.ts: Error setting array for key "${key}": "${error}"`,
//         error,
//       );
//     }
//   }

//   /**
//    * Retrieve an array from AsyncStorage.
//    * @param key - The key of the value to retrieve.
//    * @returns The stored array, or null if not found.
//    */
//   static async getArray<T>(key: string): Promise<T[] | null> {
//     try {
//       const value = await AsyncStorage.getItem(key);
//       return value !== null ? JSON.parse(value) : null;
//     } catch (error) {
//       console.error(
//         `storage-service.ts: Error retrieving array for key "${key}": "${error}"`,
//         error,
//       );
//       return null;
//     }
//   }

//   /**
//    * Remove a specific item from AsyncStorage.
//    * @param key - The key of the value to remove.
//    */
//   static async removeItem(key: string): Promise<void> {
//     try {
//       await AsyncStorage.removeItem(key);
//     } catch (error) {
//       console.error(
//         `storage-service.ts: Error removing item for key "${key}": "${error}"`,
//         error,
//       );
//     }
//   }

//   /**
//    * Clear all data from AsyncStorage.
//    */
//   static async clearAll(): Promise<void> {
//     try {
//       await AsyncStorage.clear();
//     } catch (error) {
//       console.error('storage-service.ts: Error clearing AsyncStorage:', error);
//     }
//   }
// }

// export default AsyncStorageService;
