import { RouteSummary } from '~/types/routes.types';
import { Stop } from '~/types/stops.types';
import {
  calculateDistanceBetweenCoordinatesInMeters,
  getEffectiveGeofenceDistance,
  metersToMiles,
} from '~/utils/location';
import { getCurrentLocationFromGeolocation } from '~/services/location/LocationService';
import { Protocol, ProtocolType } from '~/types/protocol.types';
import { buildScenario } from '~/services/protocol/ProtocolScenarioDataService';
import { ProtocolService } from '~/services/protocol';
import { ProtocolContextData } from '~/types/protocolActions.types';

type Coordinates = { lat: number; lon: number };

type ProtocolFlowResult = {
  protocol: Protocol;
  scenario: ProtocolContextData;
};
/**
 * Retrieves the current driver's coordinates.
 */
async function fetchDriverCoordinates(
  enhancedAccuracy: boolean = false,
): Promise<Coordinates | null> {
  const location = await getCurrentLocationFromGeolocation(enhancedAccuracy);

  if (!location) {
    console.warn(
      'GeofenceService.ts: fetchDriverCoordinates(): Failed to retrieve current location',
    );
    return null;
  }

  const { latitude, longitude } = location.coords;
  return { lat: latitude, lon: longitude };
}

/**
 * Constructs the geofence update object from stop data and driver location.
 */
function createGeofenceData(
  stop: Stop,
  effectiveGeofenceThreshold: number,
  driverCoords: Coordinates,
): Record<string, any> {
  const {
    Stop_Coordinates__Latitude__s: lat,
    Stop_Coordinates__Longitude__s: lon,
  } = stop;

  return {
    geofenceBoundary: {
      reference: {
        lat,
        lon,
      },
      maxRadius: effectiveGeofenceThreshold,
    },
    driverCoordinates: driverCoords,
  };
}

/**
 * Prepares and launches the appropriate protocol flow.
 */
async function initiateProtocolFlow(
  protocolType: ProtocolType,
  stop: Stop,
  route: RouteSummary | null,
  updates: Record<string, any>,
  effectiveGeofenceThreshold: number,
): Promise<ProtocolFlowResult | null> {
  const scenario = await buildScenario({
    stopId: stop.Id,
    routeSummaryId: stop.Summary__c,
  });

  if (!scenario) {
    console.error(
      'GeofenceService.ts: buildScenario(): Unable to build scenario.',
    );
    return null;
  }

  const { driverCoordinates } = updates;
  const distanceInMeters = calculateDistanceBetweenCoordinatesInMeters(
    driverCoordinates.lat,
    driverCoordinates.lon,
    stop.Stop_Coordinates__Latitude__s,
    stop.Stop_Coordinates__Longitude__s,
  );

  try {
    const distanceInMiles = metersToMiles(distanceInMeters);

    const geofenceThresholdInMiles = metersToMiles(effectiveGeofenceThreshold);

    const scenarioWithUpdates = {
      ...scenario,
      'Stop__c.driverCoordinates': updates.driverCoordinates,
      'Stop__c.driverDistanceFromStop': distanceInMiles ?? 0,
      'Stop__c.geofenceThresholdInMiles': geofenceThresholdInMiles,
      effectiveGeofenceThreshold,
    };

    const protocols = ProtocolService.getApplicableProtocols({
      type: protocolType,
      scenario: scenarioWithUpdates,
    });

    if (protocols.length === 0) {
      console.error(
        'GeofenceService.ts: initiateProtocolFlow():  No matching protocol found for scenario.',
      );
      return null;
    }

    return { protocols, scenario: scenarioWithUpdates };
  } catch (error) {
    console.error(
      'GeofenceService.ts: initiateProtocolFlow():  No matching protocol found for scenario.',
      error,
    );
    return null;
  }
}

/**
 * Main function to check if the driver has entered the geofence and launch protocol if applicable.
 */
export async function getProtocolForGeofenceEntry(
  route: RouteSummary | null,
  stop: Stop,
  protocolType: ProtocolType,
): Promise<ProtocolFlowResult | null> {
  const effectiveGeofenceThreshold = getEffectiveGeofenceDistance({
    route,
    stop,
  });

  console.info('DEBUG: stop-level geofence check:', {
    stopLevel: stop?.Geofencing_Distance__c,
    locationStopLevel: stop?.Location__r?.Geofencing_Distance__c,
    routeLevel: route?.Geofencing_Distance__c,
    routeParentLevel: route?.Route__r?.Geofencing_Distance__c,
    effectiveGeofenceThreshold,
  });

  if (typeof effectiveGeofenceThreshold !== 'number') {
    console.info(
      'GeofenceService.ts: getProtocolForGeofenceEntry(): Invalid geofence effectiveGeofenceThreshold; skipping check.',
    );
    return null;
  }

  const driverCoords = await fetchDriverCoordinates(true);
  if (!driverCoords) return null;

  const updates = createGeofenceData(
    stop,
    effectiveGeofenceThreshold,
    driverCoords,
  );
  const result = await initiateProtocolFlow(
    protocolType,
    stop,
    route,
    updates,
    effectiveGeofenceThreshold,
  );

  return result;
}
