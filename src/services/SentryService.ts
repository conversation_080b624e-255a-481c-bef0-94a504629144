import * as Sentry from '@sentry/react-native';
import Config from 'react-native-config';

type ReportFailedSyncParams = {
  error: unknown;
  tags?: Record<string, string>;
  extra?: Record<string, any>;
  contexts?: Record<string, any>;
  level?: 'fatal' | 'error' | 'warning' | 'info' | 'debug';
};

export const SentryService = {
  /**
   * Sends an error to Sentry
   */
  initialize() {
    Sentry.init({
      dsn: Config.SENTRY_DSN,
      enabled: !__DEV__,
      // Set tracesSampleRate to 1.0 to capture 100%
      // of transactions for performance monitoring.
      // We recommend adjusting this value in production
      tracesSampleRate: 1.0,
      environment: Config.ENV,
      ignoreErrors: [
        'User is not authenticated',
        'Request failed with status code 401',
        'Network Error',
        'Error: Network Error',
      ],
      beforeSend(event, hint) {
        const isNonErrorException =
          event.exception?.values?.[0].value?.startsWith(
            'Non-Error exception captured',
          ) ||
          (hint?.originalException as any)?.message?.startsWith(
            'Non-Error exception captured',
          );

        if (isNonErrorException) {
          // We want to ignore those kind of errors
          return null;
        }
        return event;
      },
      debug: false, // If `true`, Sentry will try to print out useful debugging information if something goes wrong with sending the event. Set it to `false` in production
    });
  },
  /**
   * Sends an error to Sentry
   */
  setSentryUser(email: string, userId: string) {
    if (email && userId) {
      Sentry.setUser({
        email,
        id: userId,
      });

      return true;
    }

    return false;
  },

  /**
   * Sends an error to Sentry
   */

  formatError(error: unknown): Error {
    if (error instanceof Error) {
      return error;
    }
    if (typeof error === 'string') {
      return new Error(error);
    }
    return new Error(JSON.stringify(error));
  },

  async logSentryError({
    error,
    tags = {},
    extra = {},
    contexts = {},
    level = 'error',
  }: ReportFailedSyncParams) {
    const formattedError = this.formatError(error);

    Sentry.captureException(formattedError, {
      level,
      tags: {
        service: 'sync-up',
        ...tags,
      },
      extra: {
        errorMessage: formattedError.message,
        ...extra,
      },
      contexts: contexts,
    });

    await Sentry.flush();
  },
};
