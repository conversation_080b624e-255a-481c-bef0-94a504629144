import { useMemo } from 'react';
import { doesConditionHold } from '~/services/protocol/ProtocolConditionEvaluatorService';
import { Condition } from '~/types/condition.types';

export function useComponentDisplay({
  condition,
  userData,
}: {
  condition: Condition | undefined | null;
  userData: Record<string, any>;
}) {
  const shouldDisplay = useMemo(() => {
    if (condition) {
      return doesConditionHold(condition, userData);
    }
    return true;
  }, [condition, userData]);

  return shouldDisplay;
}
