/**
 * ProtocolConditionEvaluatorService
 *
 * This service provides core business logic for creating, evaluating, and managing conditions
 * associated with scenario-based protocols. It supports nested "all"/"any" conditions, as well as
 * custom operators for advanced checks. Through its exported methods, it determines whether a given
 * scenario or context meets each criterion defined in a condition object.
 *
 * Responsibilities:
 * - Evaluate nested "all" or "any" conditions to determine if a scenario meets a complex rule set.
 * - Support and apply custom operators (e.g., 'contains', 'range checks', etc.) for unique logic.
 * - Provide warnings when no custom operator is specified, or when an operator is unrecognized.
 * - Serve as a foundation for higher-level protocol logic that can act on these condition outcomes.
 *
 * Example Usage:
 * ```ts
 * import { doesConditionHold } from './protocolConditionEvaluatorService';
 *
 * // Define a condition with nested requirements:
 * const condition = {
 *   all: [
 *     { field: 'age', operator: '>=', value: 18 },
 *     { field: 'region', operator: '===', value: 'USA' }
 *   ]
 * };
 *
 * // Define a scenario object that maps fields like 'age' and 'region'
 * const scenarioData = {
 *   age: 20,
 *   region: 'USA',
 *   name: 'Sample User'
 * };
 *
 * // Evaluate the condition
 * const doesHold = doesConditionHold(condition, scenarioData);
 * console.log(doesHold); // true if the scenario meets all required conditions
 * ```
 *
 * Dependencies:
 * - Uses Condition and SimpleCondition definitions from '~/types/condition.types.ts'
 * - Relies on custom operator implementations in '~/types/customOperators.types.ts'
 *
 * Notes:
 * - Ensure custom operators are defined and registered in the 'customOperators' object as needed.
 * - Warnings are logged if an operator is missing or unrecognized.
 * - All condition checks are implemented synchronously; ensure any asynchronous needs are handled externally.
 */
import {
  Condition,
  SimpleCondition as BasicCondition,
} from '~/types/condition.types.ts';
import { customOperators } from '~/types/customOperators.types.ts';
import { ProtocolContextData } from '~/types/protocolActions.types';

/**
 * @description Evaluates the provided condition object against the scenario data to determine if the condition holds true.
 *
 * If the condition has an `all` property, each nested condition must hold true.
 * If the condition has an `any` property, at least one nested condition must hold true.
 * Otherwise, the condition is interpreted as a basic condition that is evaluated by `doesBasicConditionHold`.
 *
 * @param {Condition} currentCondition - A nested condition object that may contain `all`, `any`, or basic properties.
 * @param {Record<string, any>} scenario - A mapping of field names to their values in the current scenario.
 * @returns {boolean} True if the scenario satisfies the condition, false otherwise.
 */

export function resolveValue(context: ProtocolContextData, field: string): any {
  // Try direct match first
  if (context.hasOwnProperty(field)) {
    return context[field];
  }

  // Try nested resolution
  return field.split('.').reduce((obj, key) => {
    if (obj && typeof obj === 'object' && key in obj) {
      return obj[key];
    }
    return undefined;
  }, context);
}
export function doesConditionHold(
  currentCondition: Condition,
  context: ProtocolContextData,
): boolean {
  if ('all' in currentCondition) {
    return currentCondition.all.every((subCondition: Condition) =>
      doesConditionHold(subCondition, context),
    );
  }

  if ('any' in currentCondition) {
    return currentCondition.any.some((subCondition: Condition) =>
      doesConditionHold(subCondition, context),
    );
  }

  const basicCondition = currentCondition as BasicCondition;

  if (!basicCondition.field) {
    return false;
  }

  const scenarioValue = resolveValue(context, basicCondition.field);

  return doesBasicConditionHold(basicCondition, scenarioValue, context);
}

/**
 * @description Evaluates a single condition by applying either a standard operator (e.g., '==', '>=', etc.) or a custom operator.
 *
 * If the `operator` is set to 'custom', the function looks for a corresponding operator in `customOperators`.
 * If no custom operator is specified or the operator is unrecognized, it logs a warning.
 * For standard operators, it compares the scenario value to the condition's expected value.
 *
 * @param {BasicCondition} basicCondition - The condition object containing operator, field, and value.
 * @param {any} scenarioValue - The data from the scenario that needs to be evaluated against the condition.
 * @returns {boolean} True if the scenario value satisfies the condition; false otherwise.
 */
function doesBasicConditionHold(
  basicCondition: BasicCondition,
  scenarioValue: any,
  context?: ProtocolContextData,
): boolean {
  if (basicCondition.operator === 'custom') {
    if (!basicCondition.customOperator) {
      console.warn(
        `No custom operator specified for ${JSON.stringify(basicCondition)}`,
      );
      return false;
    }

    const customChecker = customOperators[basicCondition.customOperator];
    if (!customChecker) {
      console.warn(
        `Unrecognized custom operator: ${basicCondition.customOperator}`,
      );
      return false;
    }

    return customChecker(basicCondition, scenarioValue, context);
  }

  const expectedValue = basicCondition.value;

  switch (basicCondition.operator) {
    case '==':
      // eslint-disable-next-line eqeqeq
      return scenarioValue == expectedValue;
    case '===':
      return scenarioValue === expectedValue;
    case '!=':
      // eslint-disable-next-line eqeqeq
      return scenarioValue != expectedValue;
    case '!==':
      return scenarioValue !== expectedValue;
    case '>':
      return scenarioValue > expectedValue;
    case '>=':
      return scenarioValue >= expectedValue;
    case '<':
      return scenarioValue < expectedValue;
    case '<=':
      return scenarioValue <= expectedValue;
    default:
      console.warn(
        `Unrecognized standard operator: ${basicCondition.operator}`,
      );
      return false;
  }
}
