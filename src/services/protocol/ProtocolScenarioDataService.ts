import { getRealmInstance } from '~/db/realm/utils/realm.config';
import { ProtocolContextData } from '~/types/protocolActions.types';
import { EntityType } from '~/types/sync.types';
import { ParcelSchema } from '~/db/realm/schemas/parcel.schema';
import { RouteSummarySchema } from '~/db/realm/schemas/route-summary.schema';
import { StopSchema } from '~/db/realm/schemas/stop.schema';
import { DailyScheduleSchema } from '~/db/realm/schemas/daily-schedule.schema';

export type EntityObjectInfo = {
  entityName: EntityType;
  entityId: string;
  linkedEntities?: Array<{
    entityName: EntityType;
    idField: string;
  }>;
};

/**
 * Builds a ProtocolContextData object by fetching Realm objects for provided entity names and IDs.
 * Supports RouteSummary, Stop, and Parcel objects.
 *
 * @param entities Array of { entityName, entityId } pairs.
 * @returns ProtocolContextData object populated with matching Realm objects.
 */
export async function buildScenarioForEntities(
  entityObjectInfos: EntityObjectInfo[],
): Promise<ProtocolContextData | null> {
  const realm = await getRealmInstance();

  let result: ProtocolContextData = {};

  for (const { entityName, entityId, linkedEntities } of entityObjectInfos) {
    if (!entityId) {
      console.error(
        `ProtocolScenarioDataService.ts: buildScenarioForEntities(): Failed to fetch ${entityName} with id ${entityId} because entityId is null or empty.`,
      );
      return null;
    }

    try {
      const object = realm.objectForPrimaryKey(entityName, entityId) ?? null;
      const clonedObject = JSON.parse(JSON.stringify(object));
      const flattenedObject = flattenEntityWithPrefix(entityName, clonedObject);

      result = {
        ...result,
        ...flattenedObject,
      };

      // Fetch linked entities if specified
      if (linkedEntities && result) {
        for (const linkedEntity of linkedEntities) {
          const linkedId = result[linkedEntity.idField];
          if (linkedId) {
            const linkedObject =
              realm.objectForPrimaryKey(linkedEntity.entityName, linkedId) ??
              null;

            if (linkedObject) {
              const clonedLinkedObject = JSON.parse(
                JSON.stringify(linkedObject),
              );
              const flattenedLinkedObject = flattenEntityWithPrefix(
                linkedEntity.entityName,
                clonedLinkedObject,
              );
              result = {
                ...result,
                ...flattenedLinkedObject,
              };
            }
          }
        }
      }
    } catch (error) {
      console.error(
        `ProtocolScenarioDataService.ts: buildScenarioForEntities(): Failed to fetch ${entityName} with id ${entityId}:`,
        error,
      );
      return null;
    }
  }
  return result;
}

const flattenEntityWithPrefix = (
  entityName: string,
  data: ProtocolContextData,
): ProtocolContextData => {
  if (!data) return {};

  return Object.entries(data).reduce((updatedData, [propertyName, value]) => {
    // Skip Stops__r field for Daily_Schedule__c
    if (entityName === 'Daily_Schedule__c' && propertyName === 'Stops__r') {
      return updatedData;
    }
    if (updatedData) {
      updatedData[`${entityName}.${propertyName}`] = value;
    }
    return updatedData;
  }, {} as ProtocolContextData);
};

export const extractLinkedEntityIds = (
  scenario: Record<string, any>,
  linkedEntities: string[],
): Record<string, string | undefined> => {
  const result: Record<string, string | undefined> = {};

  linkedEntities.forEach(entity => {
    const idKey = `${entity}.Id`;
    result[entity] = scenario[idKey];
  });

  return result;
};

export const buildScenario = async ({
  stopId,
  routeSummaryId,
  parcelId,
  includeDailySchedule = false,
}: {
  stopId?: string | null;
  routeSummaryId?: string | null;
  parcelId?: string | null;
  includeDailySchedule?: boolean;
}): Promise<ProtocolContextData | null> => {
  const entities = [
    stopId && {
      entityName: StopSchema.name,
      entityId: stopId,
      linkedEntities: includeDailySchedule
        ? [
            {
              entityName: DailyScheduleSchema.name,
              idField: 'Stop__c.Daily_Schedule__c',
            },
          ]
        : undefined,
    },
    routeSummaryId && {
      entityName: RouteSummarySchema.name,
      entityId: routeSummaryId,
    },
    parcelId && {
      entityName: ParcelSchema.name,
      entityId: parcelId,
    },
  ].filter(Boolean) as EntityObjectInfo[];

  if (entities.length === 0) return null;

  const data = await buildScenarioForEntities(entities);
  return data;
};
