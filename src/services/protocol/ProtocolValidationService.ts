import {
  isRequired<PERSON>alid,
  is<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ali<PERSON>,
  isA<PERSON>y<PERSON>in<PERSON><PERSON>th<PERSON>alid,
  isMaxLengthValid,
  isRegexValid,
  isPhoneNumberValid,
  isEmailValid,
  isSSNValid,
  isFileSizeValid,
  isFileTypeAllowed,
} from '~/utils/validation/input.ts';
import { Validation } from '~/types/component.types';

/**
 * Evaluates an array of validation rules against a single field value.
 *
 * Developer instructions:
 * - Use this function whenever you need to check one field's value against multiple validation rules.
 * - Each rule is processed in a switch statement, leveraging helper functions (e.g., `isMinLengthValid`).
 * - If any rule fails, the function pushes the associated `validationErrorMsg` into an errors array.
 * - Extend this switch statement to add new validation types or modify existing ones.
 *
 * Example usage:
 * ```ts
 * const rules: Validation[] = [
 *   { type: 'Required', validationErrorMsg: 'Field is required' },
 *   { type: 'MinLength', value: 3, validationErrorMsg: 'At least 3 chars' }
 * ];
 * const result = evaluateFieldValidations('ab', rules);
 * // result -> ['At least 3 chars']
 * ```
 *
 * @param {any} value - The value (usually a string) we want to validate.
 * @param {Validation[]} rules - An array of validation rule objects, each with `type`, optional `value`, and a `validationErrorMsg`.
 * @returns {string[]} An array of error messages if any rules fail; an empty array if all pass.
 */
export function evaluateFieldValidations(
  value: any,
  rules: Validation[],
): string[] {
  const errors: string[] = [];

  for (const rule of rules) {
    let isValid = true; // assume pass unless proven otherwise

    switch (rule.type) {
      case 'Required': {
        isValid = isRequiredValid(value);
        break;
      }
      case 'MinLength': {
        const minLength = Number(rule.value) || 0;
        // Check if the value is an array or a string
        // and validate accordingly
        if (Array.isArray(value)) {
          isValid = isArrayMinLengthValid(value, minLength);
        } else {
          isValid = isMinLengthValid(String(value ?? ''), minLength);
        }
        break;
      }
      case 'MaxLength': {
        const maxLength = Number(rule.value) || Infinity;
        isValid = isMaxLengthValid(String(value ?? ''), maxLength);
        break;
      }
      case 'Regex': {
        const pattern = String(rule.value ?? '.*');
        isValid = isRegexValid(String(value ?? ''), pattern);
        break;
      }
      case 'PhoneNumber': {
        isValid = isPhoneNumberValid(String(value ?? ''));
        break;
      }
      case 'Email': {
        isValid = isEmailValid(String(value ?? ''));
        break;
      }
      case 'SSN': {
        isValid = isSSNValid(String(value ?? ''));
        break;
      }
      case 'FileSizeMax': {
        const fileSizeMB = Number(value) || 0;
        const maxSizeMB = Number(rule.value) || 5;
        isValid = isFileSizeValid(fileSizeMB, maxSizeMB);
        break;
      }
      case 'FileTypesAllowed': {
        const fileName = String(value ?? '');
        const allowedExtensions = Array.isArray(rule.value) ? rule.value : [];
        isValid = isFileTypeAllowed(fileName, allowedExtensions);
        break;
      }
      default: {
        console.warn(`Unsupported validation type: ${rule.type}`);
        break;
      }
    }

    // If the boolean check indicates invalid, push the custom error message
    if (!isValid && rule.validationErrorMsg) {
      errors.push(rule.validationErrorMsg);
    }
  }

  return errors;
}
