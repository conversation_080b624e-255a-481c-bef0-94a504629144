/*
 *
 * This service applies various data transformations to your protocol’s current state. In particular,
 * it provides functions like `runBeforeTransformations` to handle transformation instructions.
 *
 * Example:
 *
 * ```ts
 * import { runBeforeTransformations } from '~/services/protocol/ProtocolDataTransformationService';
 *
 * // Suppose we have some instructions:
 * const beforeInstructions = [
 *   {
 *     type: 'transform',
 *     operation: 'combine',
 *     sourceFields: ['user.firstName', 'user.lastName'],
 *     destinationField: 'user.fullName',
 *     delimiter: ' '
 *   }
 * ];
 *
 * const currentState = {
 *   user: {
 *     firstName: 'John',
 *     lastName: 'Doe'
 *   }
 * };
 *
 * const updatedState = runBeforeTransformations(beforeInstructions, currentState);
 * // updatedState.user.fullName will be 'John <PERSON>'
 * ```
 */

import { BeforeActionDefinition } from '~/types/protocolActions.types';
import { getValueByPath, setValueByPath } from '~/utils/objects.ts';
import { doesConditionHold } from '~/services/protocol/ProtocolConditionEvaluatorService';
import type {
  TransformationHandler,
  StructuredTransformation,
} from '~/types/protocolActions.types';

function combineFields(
  source: Array<string> | Array<StructuredTransformation>,
  currentState: Record<string, any>,
  delimiter: string,
): string {
  if (!Array.isArray(source) || source.length === 0) {
    return '';
  }

  // Determine if this is an array of string references or condition-based objects
  const firstItem = source[0];
  const isStringRefs = typeof firstItem === 'string';

  if (isStringRefs) {
    // Old approach: gather from currentState by path
    const gatheredValues: string[] = [];
    for (const path of source as string[]) {
      try {
        const value = getValueByPath(currentState, path);
        if (value !== undefined && value !== null) {
          const valStr = String(value).trim();
          if (valStr) {
            gatheredValues.push(valStr);
          }
        }
      } catch (error) {
        console.warn(
          `Error extracting field "${path}" for combination:`,
          error,
        );
      }
    }
    return gatheredValues.join(delimiter);
  } else {
    // New approach: condition-based objects
    const results: string[] = [];
    const structuredSource = source as StructuredTransformation[];
    structuredSource.forEach(structuredTransformation => {
      try {
        // Evaluate condition
        if (
          doesConditionHold(structuredTransformation.condition, currentState)
        ) {
          // If true, append obj.value (if non-empty)
          const valStr = structuredTransformation.value?.trim();
          if (valStr) {
            results.push(valStr);
          }
        }
      } catch (error) {
        console.warn('Error evaluating condition-based combine field:', error);
      }
    });

    return results.join(delimiter);
  }
}

function mapValueIfAllConditionsMatch(
  source: Array<string> | Array<StructuredTransformation>,
  currentState: Record<string, any>,
): any {
  if (!Array.isArray(source) || source.length === 0) {
    return;
  }

  for (const structuredTransformation of source as StructuredTransformation[]) {
    try {
      const condition = structuredTransformation.condition;
      const value = structuredTransformation.value;

      if (doesConditionHold(condition, currentState)) {
        return value;
      }
    } catch (error) {
      console.warn('Error evaluating condition-based value mapping:', error);
    }
  }

  return undefined;
}

function mapContextValue(
  source: Array<string> | Array<StructuredTransformation>,
  currentState: Record<string, any>,
): any {
  if (!Array.isArray(source) || source.length === 0) {
    return;
  }

  for (const structuredTransformation of source as StructuredTransformation[]) {
    try {
      const condition = structuredTransformation.condition;
      const value = structuredTransformation.value;
      let valuePath = currentState[value];

      // If direct access returns undefined, try getValueByPath for nested paths
      if (valuePath === undefined) {
        valuePath = getValueByPath(currentState, value);
      }
      if (doesConditionHold(condition, currentState)) {
        return valuePath;
      }
    } catch (error) {
      console.warn('Error evaluating condition-based value mapping:', error);
    }
  }

  return undefined;
}

/**
 * A registry of supported transformation operations. This can be extended with new operations as
 * needed.
 */
const transformationsMap: Record<string, TransformationHandler> = {
  /**
   * The 'combine' operation:
   * Gathers the specified source fields, joins them using a delimiter, and returns the combined value.
   */
  combine: ({ transformation, currentState }) => {
    const delimiter = transformation.delimiter ?? ';';
    const combinedValue = combineFields(
      transformation.sourceFields,
      currentState,
      delimiter,
    );
    return combinedValue;
  },

  mapValue: ({ transformation, currentState }) => {
    return mapValueIfAllConditionsMatch(
      transformation.sourceFields,
      currentState,
    );
  },

  mapValueFromContext: ({ transformation, currentState }) => {
    return mapContextValue(transformation.sourceFields, currentState);
  },
  // Example placeholder for another operation:
  // "split": ({ transformation, currentState, transformedState }) => {
  //   // Your custom logic here, e.g., reading a single string field and splitting it into multiple fields.
  // },
};

/**
 * Applies a series of transformations to a given state before an action.
 * Reads through each transformation step, gathers the necessary information, and merges it back.
 * This function tells the "story" of how we process each "before" instruction from the protocol.
 *
 * 1. We begin by cloning the current state.
 * 2. For each transformation instruction, if it's not recognized, we skip it.
 * 3. If the operation is found in the transformations registry, we apply it.
 * 4. We then return the updated state after applying all transformations.
 *
 * @param {BeforeActionDefinition[] | undefined} before - An optional array of transformation instructions.
 * @param {Record<string, any>} currentState - The current state object that transformations will be applied to.
 * @returns {Record<string, any>} The transformed state after applying all recognized instructions.
 */
export function runBeforeTransformations(
  before: BeforeActionDefinition[] | undefined,
  currentState: Record<string, any>,
): Record<string, any> {
  const hasValidBeforeInstructions = before && Array.isArray(before);
  if (!hasValidBeforeInstructions) {
    return currentState;
  }

  const clonedState = JSON.parse(JSON.stringify(currentState));

  for (const transformation of before) {
    const isRecognizedTransform = transformation.type === 'transform';
    if (!isRecognizedTransform) {
      continue;
    }

    const transformationHandler = transformationsMap[transformation.operation];
    const isSupportedOperation = !!transformationHandler;

    if (!isSupportedOperation) {
      console.warn(
        `No transformation handler available for operation "${transformation.operation}". Skipping.`,
      );
      continue;
    }

    const transformationResult = transformationHandler({
      transformation,
      currentState,
      transformedState: clonedState,
    });

    const hasReturnedData = transformationResult !== undefined;
    if (hasReturnedData) {
      setValueByPath(
        clonedState,
        transformation.destinationField,
        transformationResult,
      );
    }
  }
  return clonedState;
}
