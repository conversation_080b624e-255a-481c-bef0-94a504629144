import { create } from 'zustand';
import { AddImagePayload } from '~/services/ImageStoreService';
import { ImageTitle } from '~/utils/images';

type ImageTitleType = keyof typeof ImageTitle;

type ProtocolState = Record<string, any> & {
  imagesToUpload: Record<ImageTitleType, AddImagePayload | AddImagePayload[]>;
};

type ProtocolDataStore = {
  userData: ProtocolState;
  setUserData: (key: string, value: any) => void;
  resetUserData: () => void;
};

const initialUserData = {
  snoWaitingTimeStatus: 'Active',
  imagesToUpload: {} as Record<
    ImageTitleType,
    AddImagePayload | AddImagePayload[]
  >,
};

export const useProtocolDataStore = create<ProtocolDataStore>(set => ({
  userData: { ...initialUserData },
  setUserData: (key: string, value: any) =>
    set(state => ({
      userData: {
        ...state.userData,
        [key]: value,
      },
    })),
  resetUserData: () => set({ userData: initialUserData }),
}));

export const useProtocolData = (key: string): [any, (val: any) => void] => {
  const value = useProtocolDataStore(state => state.userData[key]);
  const setUserData = useProtocolDataStore(state => state.setUserData);
  return [value, (val: any) => setUserData(key, val)];
};
