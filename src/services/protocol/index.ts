import { Protocol, ProtocolType } from '~/types/protocol.types';
import { doesConditionHold } from '~/services/protocol/ProtocolConditionEvaluatorService';
import { StopWithRelations } from '~/types/stops.types';
import StopClosureDelayProtocol from '~/services/protocol/configs/StopClosureDelayProtocol.json';
import SNOProtocol from '~/services/protocol/configs/SNOProtocol.json';
import EndOfRouteSurvey from '~/services/protocol/configs/EndOfRouteSurvey.json';
import GeofenceProtocol from '~/services/protocol/configs/GeofenceProtocol.json';
import HeatProtocolPreRoute from '~/services/protocol/configs/HeatProtocolPreRoute.json';
import HeatProtocolPrePickup from '~/services/protocol/configs/HeatProtocolPrePickup.json';
import HeatProtocolPostRoute from '~/services/protocol/configs/HeatProtocolPostRoute.json';
import { ProtocolContextData } from '~/types/protocolActions.types';
import { buildScenario } from '~/services/protocol/ProtocolScenarioDataService';
import { getIncompleteEndOfRouteProtocols } from '~/db/realm/operations/protocol-verification.operations';

export interface ProtocolResult {
  protocols: Protocol[];
  scenario: ProtocolContextData;
}

interface ProtocolCheckResult {
  isLoading: boolean;
  protocolData: ProtocolResult | null;
  error: Error | null;
}

const protocols: Record<ProtocolType, Protocol[]> = {
  [ProtocolType.PRE_ARRIVE_STOP]: [StopClosureDelayProtocol as Protocol],
  [ProtocolType.PRE_COMPLETE_STOP]: [SNOProtocol as Protocol],
  [ProtocolType.POST_ROUTE_COMPLETE]: [
    HeatProtocolPostRoute as Protocol,
    EndOfRouteSurvey as Protocol,
  ],
  [ProtocolType.POST_ARRIVE_STOP]: [GeofenceProtocol as Protocol],
  [ProtocolType.PRE_ROUTE_START]: [HeatProtocolPreRoute as Protocol],
  [ProtocolType.PRE_PICKUP]: [HeatProtocolPrePickup as Protocol],
};

/**
 * Check if a protocol is applicable based on the context and the protocol type.
 * @param type - The type of protocol to check. Eg. PRE_ARRIVE_STOP, PRE_COMPLETE_STOP, POST_ROUTE_COMPLETE
 * @param context - The context to check the protocol against.
 * @returns An object containing the applicable protocol and the context if it is applicable, otherwise null.
 */
const getApplicableProtocols = ({
  type,
  scenario,
}: {
  type: ProtocolType;
  scenario: ProtocolContextData;
}): Protocol[] => {
  const protocolsForType = protocols[type];

  if (!protocolsForType?.length) {
    return [];
  }

  // Filter and map in one pass
  const applicableProtocols = protocolsForType.reduce<Protocol[]>(
    (acc, protocol) => {
      try {
        if (doesConditionHold(protocol.executionCriteria, scenario)) {
          acc.push(protocol);
        }
      } catch (error) {
        console.error(
          `Error evaluating protocol ${protocol.id || 'unknown'}:`,
          error,
        );
      }
      return acc;
    },
    [],
  );

  // Return early if no sorting needed
  if (applicableProtocols.length <= 1) {
    return applicableProtocols;
  }

  // Sort protocols by priority then ID
  return applicableProtocols.sort((a, b) => {
    const priorityA = a.priority ?? Number.MAX_SAFE_INTEGER;
    const priorityB = b.priority ?? Number.MAX_SAFE_INTEGER;

    // First sort by priority (lower = higher priority)
    const priorityDiff = priorityA - priorityB;
    if (priorityDiff !== 0) {
      return priorityDiff;
    }

    // If priorities are equal, sort by ID
    return a.id.localeCompare(b.id);
  });
};

/**
 * Checks if a hot weather protocol is applicable for the given stop
 * @param stop The stop to check protocols for
 * @returns Promise<ProtocolCheckResult> The result of the protocol check
 */
const checkProtocols = async (
  stop: StopWithRelations,
  protocolType: ProtocolType,
): Promise<ProtocolCheckResult> => {
  if (!stop || !protocolType) {
    return {
      isLoading: false,
      protocolData: null,
      error: null,
    };
  }

  try {
    const scenario = await buildScenario({
      stopId: stop.Id,
      routeSummaryId: stop.Summary__c,
      includeDailySchedule: true,
    });

    if (!scenario) {
      return {
        isLoading: false,
        protocolData: null,
        error: null,
      };
    }

    const applicableProtocols = getApplicableProtocols({
      type: protocolType,
      scenario,
    });

    if (applicableProtocols.length === 0) {
      return {
        isLoading: false,
        protocolData: null,
        error: null,
      };
    }

    return {
      isLoading: false,
      protocolData: {
        protocols: applicableProtocols,
        scenario,
      },
      error: null,
    };
  } catch (error) {
    console.error('Error checking protocols:', error);
    return {
      isLoading: false,
      protocolData: null,
      error: error as Error,
    };
  }
};

/**
 * Checks end-of-route protocols and returns only the incomplete ones
 * @param stop The stop to check protocols for
 * @returns Promise<ProtocolCheckResult> The result of the protocol check
 */

const checkEndOfRouteProtocols = async (
  stop: StopWithRelations,
): Promise<ProtocolCheckResult> => {
  if (!stop) {
    return {
      isLoading: false,
      protocolData: null,
      error: null,
    };
  }

  try {
    const result = await checkProtocols(stop, ProtocolType.POST_ROUTE_COMPLETE);

    if (result.error) {
      return result;
    }

    if (!result.protocolData) {
      return {
        isLoading: false,
        protocolData: null,
        error: null,
      };
    }

    const { protocols: allEndOfRouteProtocols, scenario } = result.protocolData;

    if (allEndOfRouteProtocols.length === 0) {
      return {
        isLoading: false,
        protocolData: null,
        error: null,
      };
    }

    const routeId = stop.Summary__c;
    if (!routeId) {
      return {
        isLoading: false,
        protocolData: null,
        error: null,
      };
    }

    const incompleteProtocols = await getIncompleteEndOfRouteProtocols(routeId);

    // Filter protocols based on completion status - show only incomplete ones
    const pendingEndOfRouteProtocols = allEndOfRouteProtocols.filter(
      protocol => {
        if (protocol.id === 'EndOfRouteSurvey') {
          return !incompleteProtocols.endOfRouteSurvey;
        }
        if (protocol.id === 'PostRouteSurvey') {
          return !incompleteProtocols.postRouteSurvey;
        }
        return true;
      },
    );

    if (pendingEndOfRouteProtocols.length === 0) {
      return {
        isLoading: false,
        protocolData: null,
        error: null,
      };
    }

    return {
      isLoading: false,
      protocolData: {
        protocols: pendingEndOfRouteProtocols,
        scenario,
      },
      error: null,
    };
  } catch (error) {
    console.error('Error checking end-of-route protocols:', error);
    return {
      isLoading: false,
      protocolData: null,
      error: error as Error,
    };
  }
};

const isStopEligibleForHeatProtocol = (stop: StopWithRelations): boolean => {
  return Boolean(stop && !stop.Arrival_Time__c);
};

export const ProtocolService = {
  getApplicableProtocols,
  isStopEligibleForHeatProtocol,
  checkProtocols,
  checkEndOfRouteProtocols,
};
