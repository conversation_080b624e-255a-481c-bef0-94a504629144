/****
 * The ProtocolActionService processes step-level actions (like navigate, close, invokeAction)
 * and dictionary-level actions (like save).
 *
 * Example usage:
 *   const result = processStepAction(stepAction, globalActions, currentState);
 *   if (result.status === 'navigate') {
 *     // do something with result.nextStepId
 *   }
 *
 * This service is synchronous and returns an ActionHandlerResult describing what the protocol engine should do next.
 */
import type {
  StepAction,
  StepActionTargetStepCondition,
  ProtocolActionDefinition,
  ProtocolActionsMap,
  AfterActionDefinition,
} from '~/types/protocolActions.types';

import type { Condition } from '~/types/condition.types';
import { doesConditionHold } from '~/services/protocol/ProtocolConditionEvaluatorService';
import { runBeforeTransformations } from '~/services/protocol/ProtocolDataTransformationService';

export type ActionHandlerResult =
  | {
      status: 'navigate';
      nextStepId: string | null;
    }
  | {
      status: 'close';
    }
  | {
      status: 'exit';
    }
  | {
      status: 'invokeAction';
      actionId: string;
      dictionaryAction: ProtocolActionDefinition;
    }
  | {
      status: 'save';
      objects: NonNullable<ProtocolActionDefinition['objects']>;
      transformedState?: Record<string, any>;
      after?: AfterActionDefinition;
    }
  | {
      status: 'capturePhoto';
      nextStepId: string | null;
    }
  | {
      status: 'error';
      message: string;
    };

/**
 * @description Processes a single step-level action in the protocol. This includes checking the action type and returning a structured response.
 * For example usage, see the top-level snippet in this file where we call `processStepAction` with a `stepAction`, `globalActions`, and `currentState`. In a typical scenario, you might retrieve the resulting `status` to decide how to transition in your UI.
 * @param {StepAction} stepAction The step action to process.
 * @param {ProtocolActionsMap} globalActions Map of global protocol actions.
 * @param {Record<string, any>} [currentState={}] State data used to evaluate conditions.
 * @return {ActionHandlerResult} The outcome of the step action (navigate, close, etc.).
 */
export function processStepAction(
  stepAction: StepAction,
  globalActions: ProtocolActionsMap,
  currentState: Record<string, any> = {},
): ActionHandlerResult {
  switch (stepAction.type) {
    case 'navigate':
    case 'capturePhoto':
      return handleStepNavigateAction(stepAction, currentState);

    case 'invokeAction':
      return processInvokeAction(stepAction, globalActions, currentState);

    case 'close':
      return handleCloseAction();

    case 'exit':
      return handleExitAction();

    default:
      return {
        status: 'error',
        message: `Unknown step action type: ${stepAction.type}`,
      };
  }
}

/**
 * @description Handles a dictionary-level action in a similar manner to step-level processing. This may involve navigation, closing, saving data, or other tasks.
 * An example usage is when a step-level action has type `invokeAction` referencing this global action. Calling `processGlobalAction` gives a structured result such as `navigate` or `save`.
 * @param {Record<string, any>} currentState The current state data for evaluating conditions.
 * @param {ProtocolActionDefinition} globalAction The dictionary-level action to process.
 * @return {ActionHandlerResult} The result of the action (navigate, close, save, etc.).
 */
export function processGlobalAction(
  currentState: Record<string, any>,
  globalAction: ProtocolActionDefinition,
): ActionHandlerResult {
  switch (globalAction.type) {
    case 'navigate': {
      const transformedState = runBeforeTransformations(
        globalAction.before,
        currentState,
      );
      return handleGlobalNavigateAction(globalAction, transformedState);
    }

    case 'close':
      return handleCloseAction();

    case 'save':
      // We now handle transformations *inside* the save handler
      return handleSaveAction(globalAction, currentState);

    default:
      return {
        status: 'error',
        message: `Unrecognized dictionary action type: ${globalAction.type}`,
      };
  }
}

/** ------------------------------------
 * STEP-LEVEL NAVIGATE
 * ------------------------------------ */
/**
 * @description Handles navigation at the step level, including checking any defined `targetStepConditions`. It returns a `navigate` result with the appropriate next step.
 * For instance, a `stepAction` might define multiple condition blocks, each pointing to a different step. This function decides which step should be navigated to.
 * @param {StepAction} stepAction The step action to process.
 * @param {Record<string, any>} currentState The current state data.
 * @return {ActionHandlerResult} A navigate result with either matched step or fallback.
 */
function handleStepNavigateAction(
  stepAction: StepAction,
  currentState: Record<string, any>,
): ActionHandlerResult {
  if (
    Array.isArray(stepAction.targetStepConditions) &&
    stepAction.targetStepConditions.length > 0
  ) {
    const matchedStepId = determineConditionalTargetStep(
      stepAction.targetStepConditions,
      currentState,
    );
    if (matchedStepId) {
      return { status: 'navigate', nextStepId: matchedStepId };
    }
  }
  return {
    status: stepAction.type,
    nextStepId: stepAction.targetStepId ?? null,
  };
}

/** ------------------------------------
 * GLOBAL-LEVEL NAVIGATE
 * ------------------------------------ */
/**
 * @description Handles navigation at the dictionary (global) level, using `targetStepConditions` if present. It returns a `navigate` result with the appropriate next step.
 * One example is a global action with multiple condition blocks to handle branching. The calling code can use the `nextStepId` to transition the protocol flow.
 * @param {ProtocolActionDefinition} globalAction The dictionary-level navigate action.
 * @param {Record<string, any>} currentState The current state data.
 * @return {ActionHandlerResult} A navigate result with either matched step or fallback.
 */
function handleGlobalNavigateAction(
  globalAction: ProtocolActionDefinition,
  currentState: Record<string, any>,
): ActionHandlerResult {
  // If the global action includes an array of target step conditions, we check them.
  // If any match, we navigate to that step.
  if (
    Array.isArray(globalAction.targetStepConditions) &&
    globalAction.targetStepConditions.length > 0
  ) {
    const matchedStepId = determineConditionalTargetStep(
      globalAction.targetStepConditions,
      currentState,
    );
    if (matchedStepId) {
      return { status: 'navigate', nextStepId: matchedStepId };
    }
  }

  // Fallback: if there's a defined target step ID, use it; otherwise null.
  return {
    status: 'navigate',
    nextStepId: globalAction.targetStepId ?? null,
  };
}

/** ------------------------------------
 * CLOSE
 * ------------------------------------ */
/**
 * @description Closes the current protocol flow, returning a `close` status to indicate that no further steps should be taken.
 * For instance, this might be invoked if the user taps a "Cancel" or "Finish" button in a step-level action.
 * @return {ActionHandlerResult} A close result.
 */
function handleCloseAction(): ActionHandlerResult {
  return { status: 'close' };
}

/** ------------------------------------
 * EXIT
 * ------------------------------------ */
/**
 * @description Exits the current protocol flow, returning an `exit` status to indicate that no further steps should be taken.
 * @return {ActionHandlerResult} An exit result.
 */
function handleExitAction(): ActionHandlerResult {
  return { status: 'exit' };
}

/** ------------------------------------
 * SAVE
 * ------------------------------------ */
/**
 * @description Handles saving data externally, typically by preparing a `save` result.
 * For example, a global action might define the objects and fields to update, which will then be queued or synced downstream.
 * Before returning these objects, we apply any defined transformations via `runBeforeTransformations`.
 * @param {ProtocolActionDefinition} globalAction The dictionary action containing save details.
 * @param {Record<string, any>} currentState The current state data.
 * @return {ActionHandlerResult} A save result.
 */
function handleSaveAction(
  globalAction: ProtocolActionDefinition,
  currentState: Record<string, any>,
): ActionHandlerResult {
  // Apply any defined transformations before returning objects.
  const transformedState = runBeforeTransformations(
    globalAction.before,
    currentState,
  );

  return {
    status: 'save',
    after: globalAction?.after,
    objects: globalAction.objects ?? [],
    transformedState: transformedState,
  };
}

/** ------------------------------------
 * INVOKE ACTION (step-level triggers dictionary-level)
 * ------------------------------------ */
/**
 * @description Processes a step-level "invokeAction" by finding the corresponding dictionary action and returning the appropriate result.
 * This is how step-level actions reference global actions such as `save`. In practice, you might pass the returned status to another function that does the actual data syncing or navigation.
 * @param {StepAction} stepAction The step action containing the actionId.
 * @param {ProtocolActionsMap} globalActions Map of global protocol actions.
 * @param {Record<string, any>} currentState The current state data for condition checks.
 * @return {ActionHandlerResult} The result of invoking the global action.
 */
function processInvokeAction(
  stepAction: StepAction,
  globalActions: ProtocolActionsMap,
  currentState: Record<string, any>,
): ActionHandlerResult {
  const { actionId } = stepAction;

  if (!actionId) {
    return {
      status: 'error',
      message: 'No actionId provided for invokeAction.',
    };
  }

  const globalAction = globalActions[actionId];
  if (!globalAction) {
    return {
      status: 'error',
      message: `Action ID "${actionId}" not found in protocol dictionary.`,
    };
  }

  const globalActionResult = processGlobalAction(currentState, globalAction);
  if (globalActionResult.status === 'error') {
    return globalActionResult;
  }

  if (['navigate', 'close', 'save'].includes(globalActionResult.status)) {
    return globalActionResult;
  }

  return {
    status: 'invokeAction',
    actionId,
    dictionaryAction: globalAction,
  };
}

/** ------------------------------------
 * DETERMINE CONDITIONAL TARGET
 * ------------------------------------ */
/**
 * @description Iterates over condition blocks to find the first matching target step. Each block can specify `all` or `any` arrays.
 * For example, if `all` conditions pass, or `any` condition passes, this function picks the associated `targetStepId`. If no blocks match, `null` is returned.
 * @param {StepActionTargetStepCondition[]} targetStepConditions The condition blocks to evaluate.
 * @param {Record<string, any>} currentState The state data used to evaluate each block.
 * @return {string | null} The target step if found, or null.
 */
function determineConditionalTargetStep(
  targetStepConditions: StepActionTargetStepCondition[],
  currentState: Record<string, any>,
): string | null {
  for (const conditionBlock of targetStepConditions) {
    const allPassed = conditionBlock.all
      ? checkAllConditions(conditionBlock.all, currentState)
      : true;
    const anyPassed = conditionBlock.any
      ? checkAnyConditions(conditionBlock.any, currentState)
      : true;

    if (allPassed && anyPassed) {
      return conditionBlock.targetStepId;
    }
  }
  return null;
}

/** ------------------------------------
 * CHECK CONDITIONS
 * ------------------------------------ */
/**
 * @description Returns true if *every* condition in the array holds, using `doesConditionHold`.
 * You might use this logic to ensure multiple fields meet certain thresholds (e.g., `age >= 21` AND `country == US`).
 * @param {Condition[]} conditions The list of conditions.
 * @param {Record<string, any>} currentState The state data.
 * @return {boolean} True if every condition passes.
 */
function checkAllConditions(
  conditions: Condition[],
  currentState: Record<string, any>,
): boolean {
  return conditions.every(cond => doesConditionHold(cond, currentState));
}

/**
 * @description Returns true if *at least one* condition in the array holds, using `doesConditionHold`.
 * One example is having multiple acceptable user input values (e.g., `country == UK` OR `country == US`).
 * @param {Condition[]} conditions The list of conditions.
 * @param {Record<string, any>} currentState The state data.
 * @return {boolean} True if at least one condition passes.
 */
function checkAnyConditions(
  conditions: Condition[],
  currentState: Record<string, any>,
): boolean {
  return conditions.some(cond => doesConditionHold(cond, currentState));
}
