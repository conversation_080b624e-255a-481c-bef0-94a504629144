{"id": "SNOPerimeterCheckFlow", "label": "SNO & Perimeter Check", "version": "1.0.0", "protocolType": "preCompleteStop", "executionCriteria": {"all": [{"field": "Route_Summary__c.SNO_Flow__c", "operator": "===", "value": true}, {"field": "Stop__c.Pieces__c", "operator": "===", "value": 0}]}, "steps": [{"stepId": "snoInitialQuestionsStep", "type": "page", "title": "<PERSON><PERSON> not out", "header": {"title": {"type": "dynamic", "value": "Stop__c.Name"}}, "components": [{"type": "RadioSelect", "key": "Stop__c.Is_Lockbox_Out__c", "validations": [{"type": "Required", "validationErrorMsg": "Please select yes or no."}], "options": {"label": "Is the lockbox out?", "choices": [{"label": "Yes", "value": true}, {"label": "No", "value": false}]}}, {"type": "RadioSelect", "key": "businessStatus", "validations": [{"type": "Required", "validationErrorMsg": "Please select open or closed."}], "options": {"label": "Is the business open or closed?", "choices": [{"label": "Open", "value": true}, {"label": "Closed", "value": false}]}}], "actions": [{"label": {"enabled": "Continue", "disabled": "Complete SNO tasks to continue "}, "type": "navigate", "options": {"variant": "filled", "requiresValidation": true}, "targetStepConditions": [{"targetStepId": "lockboxPhotosStep", "all": [{"field": "Stop__c.Is_Lockbox_Out__c", "operator": "===", "value": true}, {"field": "businessStatus", "operator": "===", "value": false}]}, {"targetStepId": "perimeterCheckStep", "all": [{"field": "Stop__c.Is_Lockbox_Out__c", "operator": "===", "value": false}, {"field": "businessStatus", "operator": "===", "value": false}]}, {"targetStepId": "businessOpenQuestions", "all": [{"field": "Stop__c.Is_Lockbox_Out__c", "operator": "===", "value": false}, {"field": "businessStatus", "operator": "===", "value": true}]}, {"targetStepId": "businessOpenQuestions", "all": [{"field": "Stop__c.Is_Lockbox_Out__c", "operator": "===", "value": true}, {"field": "businessStatus", "operator": "===", "value": true}]}]}]}, {"stepId": "lockboxPhotosStep", "type": "page", "header": {"title": {"type": "dynamic", "value": "Stop__c.Name"}}, "title": "<PERSON><PERSON> not out", "subtitle": "Please take the following photos", "components": [{"type": "PhotoCapture", "key": "photoEmptyLockbox", "linkedEntities": ["Route_Summary__c", "Stop__c"], "validations": [{"type": "Required", "validationErrorMsg": "Please take a photo of the empty lockbox."}], "options": {"label": "Empty lockbox", "cameraTitle": "Take a photo of the empty lockbox"}}, {"type": "PhotoCapture", "key": "photoSNOTag", "linkedEntities": ["Route_Summary__c", "Stop__c"], "validations": [{"type": "Required", "validationErrorMsg": "Please show the SNO tag."}], "options": {"label": "SNO tag left", "cameraTitle": "Take a photo of the SNO tag"}}, {"type": "PhotoCapture", "key": "photoLockboxArea", "linkedEntities": ["Route_Summary__c", "Stop__c"], "validations": [{"type": "Required", "validationErrorMsg": "Please show surrounding area."}], "options": {"label": "Area around the lockbox", "cameraTitle": "Take a photo of the area around the lockbox"}}], "actions": [{"label": {"enabled": "Complete SNO protocol", "disabled": "Take all photos to continue"}, "type": "invokeAction", "actionId": "sampleNotOut", "onTapAction": [{"keyValuePairs": [{"key": "Stop__c.Completed_Time__c", "setCurrentDateTime": true}], "actionType": "setValue"}], "options": {"variant": "filled", "requiresValidation": true}}]}, {"stepId": "businessOpenQuestions", "type": "page", "title": "<PERSON><PERSON> not out", "subtitle": "Confirm you have knocked and share who you spoke to at the clinic", "header": {"title": {"type": "dynamic", "value": "Stop__c.Name"}}, "components": [{"type": "RadioSelect", "key": "didKnockOnDoor", "validations": [{"type": "Required", "validationErrorMsg": "Please select yes or no."}], "options": {"label": "Did you knock on the door?", "choices": [{"label": "Yes", "value": true}, {"label": "No", "value": false}]}}, {"type": "TextInput", "key": "Stop__c.Proof_of_Service__c", "options": {"label": "Who did you talk to?", "placeholder": "Write the name of the person"}}, {"type": "RadioSelect", "key": "askWaitForSamples", "validations": [{"type": "Required", "validationErrorMsg": "Please select yes or no."}], "options": {"label": "Do they have additional samples?", "choices": [{"label": "Yes", "value": true}, {"label": "No", "value": false}]}}], "actions": [{"label": "Continue", "type": "navigate", "onTapAction": [{"actionType": "immediateUpdate", "payload": {"status": "save", "objects": [{"name": "Stop__c", "fields": ["SNO_Driver_Waiting__c"], "action": "update"}], "transformedState": {"Stop__c.SNO_Driver_Waiting__c": true}}, "updateCriteria": {"field": "askWaitForSamples", "operator": "==", "value": true}}], "options": {"variant": "filled", "requiresValidation": true}, "targetStepConditions": [{"targetStepId": "waitForSampleTimerStep", "all": [{"field": "askWaitForSamples", "operator": "===", "value": true}]}, {"targetStepId": "confirmSampleNotReceivedStep", "all": [{"field": "askWaitForSamples", "operator": "===", "value": false}]}]}]}, {"stepId": "perimeterCheckStep", "type": "page", "header": {"title": {"type": "dynamic", "value": "Stop__c.Name"}}, "title": "Check the location perimeter", "subtitle": "Make sure to take relevant photos while you're checking the perimeter", "components": [{"type": "PhotoCapture", "key": "photoPerimeter", "linkedEntities": ["Route_Summary__c", "Stop__c"], "validations": [{"type": "Required", "validationErrorMsg": "At least one perimeter photo is required."}], "options": {"label": "Photos taken", "cameraTitle": "Take a photo of the perimeter", "multiple": true}}, {"type": "<PERSON><PERSON>", "key": "lockboxFound", "options": {"label": "Lockbox found", "variant": "link", "priority": "primary", "icon": "briefcase", "iconPosition": "left", "action": {"type": "exit", "targetStepId": null}}}], "actions": [{"label": {"enabled": "Continue", "disabled": "Take photos to continue"}, "type": "invokeAction", "actionId": "perimeterChecked", "onTapAction": [{"keyValuePairs": [{"key": "perimeterNotCheckKey", "value": "PC"}, {"key": "Stop__c.Completed_Time__c", "setCurrentDateTime": true}], "actionType": "setValue"}], "options": {"variant": "filled", "requiresValidation": true}}, {"label": "Can't check the perimeter", "type": "navigate", "targetStepId": "perimeterNotCheckStep", "onTapAction": [{"keyValuePairs": [{"key": "perimeterNotCheckKey", "value": "NPC"}], "actionType": "setValue"}], "options": {"variant": "outlined"}, "displayIf": {"field": "imagesToUpload.photoPerimeter", "operator": "==", "value": null}}]}, {"stepId": "perimeterNotCheckStep", "type": "page", "header": {"title": {"type": "dynamic", "value": "Stop__c.Name"}}, "components": [{"type": "CheckboxGroup", "key": "perimeterNotCheckReasons", "validations": [{"type": "Required", "validationErrorMsg": "Please select at least one concern."}], "options": {"wrapInCard": true, "label": "Why wasn't the perimeter checked?", "choices": [{"label": "Not well lit", "value": "NWL"}, {"label": "People loitering", "value": "PL"}, {"label": "Unsafe", "value": "U"}, {"label": "Other", "value": "other"}]}}, {"type": "TextInput", "key": "Stop__c.Proof_of_Service__c", "options": {"label": "Expand on your reason", "placeholder": "Expand on your reason..."}, "validations": [{"type": "Required", "validationErrorMsg": "Please enter a reason."}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": 10, "validationErrorMsg": "Please expand on your reason."}], "displayIf": {"field": "perimeterNotCheckReasons", "operator": "custom", "customOperator": "includes", "value": "other"}}, {"type": "TextInput", "key": "Stop__c.No_Perimeter_Check_Notes__c", "options": {"label": "Comment (optional)", "placeholder": "Write more information..."}}], "actions": [{"label": {"enabled": "Continue", "disabled": "Complete information to continue"}, "type": "invokeAction", "actionId": "perimeterNotChecked", "onTapAction": [{"keyValuePairs": [{"key": "Stop__c.Completed_Time__c", "setCurrentDateTime": true}], "actionType": "setValue"}], "options": {"variant": "filled", "requiresValidation": true}}]}, {"stepId": "waitForSampleTimerStep", "type": "page", "header": {"title": {"type": "dynamic", "value": "Stop__c.Name"}}, "title": "Please wait up to 10 minutes", "subtitle": "The business is preparing samples for you", "components": [{"type": "Timer", "key": "snoWaitingTimeStatus", "options": {"duration": 10, "id": "Stop__c.Id"}, "displayIf": {"field": "snoWaitingTimeStatus", "operator": "===", "value": "Active"}}, {"type": "Timer", "key": "snoInactiveTimeStatus", "options": {"duration": 0, "id": "Stop__c.Id"}, "displayIf": {"field": "snoWaitingTimeStatus", "operator": "!==", "value": "Active"}}], "actions": [{"label": "<PERSON><PERSON> received", "type": "exit", "options": {"variant": "filled"}, "onTapAction": [{"actionType": "immediateUpdate", "payload": {"status": "save", "objects": [{"name": "Stop__c", "fields": ["SNO_Driver_Waiting__c"], "action": "update"}], "transformedState": {"Stop__c.SNO_Driver_Waiting__c": false}}}]}, {"label": "Depart early", "type": "navigate", "targetStepId": "confirmEarlyDepartureStep", "options": {"variant": "outlined"}, "displayIf": {"field": "snoWaitingTimeStatus", "operator": "===", "value": "Active"}}, {"label": "No samples received", "type": "navigate", "targetStepId": "confirmSampleNotReceivedStep", "options": {"variant": "outlined"}, "displayIf": {"field": "snoWaitingTimeStatus", "operator": "===", "value": "Inactive"}}]}, {"stepId": "confirmSampleNotReceivedStep", "type": "modal", "components": [{"type": "Icon", "key": "icon", "options": {"name": "warningWithBackground"}}, {"type": "DisplayText", "key": "modalTitle", "options": {"content": "No samples received", "type": "title", "align": "center"}}, {"type": "DisplayText", "key": "modalBody", "options": {"content": "Confirm all pickup procedures were followed and that you have made your best effort to contact dispatch", "type": "subtitle", "align": "center"}}], "actions": [{"label": "Close", "key": "closeModal", "type": "close", "targetStepId": null, "options": {"variant": "outlined", "priority": "secondary"}}, {"label": "Confirm", "key": "confirmWait", "type": "invokeAction", "actionId": "saveSnoStopData", "options": {"variant": "filled"}, "displayIf": {"field": "askWaitForSamples", "operator": "===", "value": true}, "onTapAction": [{"actionType": "setValue", "keyValuePairs": [{"key": "Stop__c.Completed_Time__c", "setCurrentDateTime": true}, {"key": "Stop__c.<PERSON><PERSON><PERSON>_Driver_Waiting__c", "value": false}]}]}, {"label": "Confirm", "key": "confirmNoWait", "type": "invokeAction", "actionId": "noPickupNoWait", "options": {"variant": "filled"}, "displayIf": {"field": "askWaitForSamples", "operator": "===", "value": false}, "onTapAction": [{"actionType": "setValue", "keyValuePairs": [{"key": "Stop__c.Completed_Time__c", "setCurrentDateTime": true}]}]}]}, {"stepId": "confirmEarlyDepartureStep", "type": "modal", "components": [{"type": "Icon", "key": "icon", "options": {"name": "warningWithBackground"}}, {"type": "DisplayText", "key": "modalTitle", "options": {"content": "Early departure", "type": "title", "align": "center"}}, {"type": "DisplayText", "key": "modalBody", "options": {"content": "Confirm all pickup procedures were followed and that you have made your best effort to contact dispatch", "type": "subtitle", "align": "center"}}], "actions": [{"label": "Close", "key": "earlyDepartClose", "type": "close", "targetStepId": null, "options": {"variant": "outlined", "priority": "secondary"}}, {"label": "Confirm", "key": "earlyDepartConfirm", "type": "close", "onTapAction": [{"keyValuePairs": [{"key": "timerDuration", "value": 0}, {"key": "snoWaitingTimeStatus", "value": "Inactive"}, {"key": "Stop__c.Driver_Initiated_Early_Departure__c", "value": true}, {"key": "Stop__c.SNO_Bypass_DateTime__c", "setCurrentDateTime": true}, {"key": "Stop__c.<PERSON><PERSON><PERSON>_Driver_Waiting__c", "value": false}], "actionType": "setValue"}, {"actionType": "immediateUpdate", "payload": {"status": "save", "objects": [{"name": "Stop__c", "fields": ["SNO_Driver_Waiting__c"], "action": "update"}], "transformedState": {"Stop__c.SNO_Driver_Waiting__c": false}}}], "displayIf": {"field": "askWaitForSamples", "operator": "===", "value": true}, "targetStepId": "confirmSampleNotReceivedStep", "options": {"variant": "filled"}}, {"label": "Confirm", "key": "earlyDepartConfirm1", "type": "invokeAction", "onTapAction": [{"actionType": "setValue", "keyValuePairs": [{"key": "Stop__c.Completed_Time__c", "setCurrentDateTime": true}]}], "actionId": "noPickupNoWait", "displayIf": {"field": "askWaitForSamples", "operator": "===", "value": false}, "targetStepId": null, "options": {"variant": "filled"}}]}], "actions": {"sampleNotOut": {"type": "save", "after": {"actionId": "confirmation", "options": {"type": "stop", "subType": "stop_completed"}}, "before": [{"type": "transform", "operation": "combine", "sourceFields": [{"condition": {"all": [{"field": "businessStatus", "operator": "===", "value": false}]}, "value": "CC"}], "destinationField": "Stop__c.<PERSON>NO_Tags__c", "delimiter": ";"}], "objects": [{"name": "Stop__c", "fields": ["SNO_Tags__c", "Completed_Time__c"], "action": "update"}]}, "noPickupNoWait": {"type": "save", "after": {"actionId": "confirmation", "options": {"type": "stop", "subType": "stop_completed"}}, "before": [{"type": "transform", "operation": "combine", "sourceFields": [{"condition": {"all": [{"field": "didKnockOnDoor", "operator": "===", "value": true}]}, "value": "KOD"}, {"condition": {"all": [{"field": "Stop__c.Is_Lockbox_Out__c", "operator": "===", "value": false}]}, "value": "NBO"}, {"condition": {"all": [{"field": "Stop__c.Proof_of_Service__c", "operator": "!==", "value": ""}]}, "value": "TT"}], "destinationField": "Stop__c.<PERSON>NO_Tags__c", "delimiter": ";"}], "objects": [{"name": "Stop__c", "fields": ["SNO_Tags__c", "Proof_of_Service__c", "Completed_Time__c"], "action": "update"}]}, "perimeterNotChecked": {"type": "save", "after": {"actionId": "confirmation", "options": {"type": "stop", "subType": "stop_completed"}}, "before": [{"type": "transform", "operation": "combine", "sourceFields": [{"condition": {"all": [{"field": "perimeterNotCheckKey", "operator": "===", "value": "NPC"}]}, "value": "NPC"}, {"condition": {"all": [{"field": "perimeterNotCheckReasons", "operator": "custom", "customOperator": "includes", "value": "NWL"}]}, "value": "NWL"}, {"condition": {"all": [{"field": "perimeterNotCheckReasons", "operator": "custom", "customOperator": "includes", "value": "PL"}]}, "value": "PL"}, {"condition": {"all": [{"field": "perimeterNotCheckReasons", "operator": "custom", "customOperator": "includes", "value": "U"}]}, "value": "U"}, {"condition": {"all": [{"field": "businessStatus", "operator": "===", "value": false}]}, "value": "CC"}, {"condition": {"all": [{"field": "Stop__c.Is_Lockbox_Out__c", "operator": "===", "value": false}]}, "value": "NBO"}], "destinationField": "Stop__c.<PERSON>NO_Tags__c", "delimiter": ";"}], "objects": [{"name": "Stop__c", "fields": ["SNO_Tags__c", "Proof_of_Service__c", "No_Perimeter_Check_Notes__c", "Completed_Time__c"], "action": "update"}]}, "perimeterChecked": {"type": "save", "after": {"actionId": "confirmation", "options": {"type": "stop", "subType": "stop_completed"}}, "before": [{"type": "transform", "operation": "combine", "sourceFields": [{"condition": {"all": [{"field": "perimeterNotCheckKey", "operator": "===", "value": "PC"}]}, "value": "PC"}, {"condition": {"all": [{"field": "businessStatus", "operator": "===", "value": false}]}, "value": "CC"}, {"condition": {"all": [{"field": "Stop__c.Is_Lockbox_Out__c", "operator": "===", "value": false}]}, "value": "NBO"}], "destinationField": "Stop__c.<PERSON>NO_Tags__c", "delimiter": ";"}], "objects": [{"name": "Stop__c", "fields": ["SNO_Tags__c", "Completed_Time__c"], "action": "update"}]}, "saveSnoStopData": {"type": "save", "after": {"actionId": "confirmation", "options": {"type": "stop", "subType": "stop_completed"}}, "before": [{"type": "transform", "operation": "combine", "sourceFields": [{"condition": {"all": [{"field": "didKnockOnDoor", "operator": "===", "value": true}]}, "value": "KOD"}, {"condition": {"all": [{"field": "businessStatus", "operator": "===", "value": false}]}, "value": "CC"}, {"condition": {"all": [{"field": "Stop__c.Is_Lockbox_Out__c", "operator": "===", "value": false}]}, "value": "NBO"}, {"condition": {"all": [{"field": "Stop__c.Proof_of_Service__c", "operator": "!==", "value": ""}]}, "value": "TT"}], "destinationField": "Stop__c.<PERSON>NO_Tags__c", "delimiter": ";"}], "objects": [{"name": "Stop__c", "fields": ["SNO_Tags__c", "Proof_of_Service__c", "Completed_Time__c", "SNO_Driver_Waiting__c", "Driver_Initiated_Early_Departure__c", "SNO_Bypass_DateTime__c"], "action": "update"}]}}}