{"id": "HighTemperatureLockboxPlacementVerification", "label": "High Temperature Lockbox Placement Verification", "version": "1.0.0", "protocolType": "prePickup", "executionCriteria": {"all": [{"field": "Route_Summary__c.High_Temperature__c", "operator": ">=", "value": "75"}]}, "steps": [{"stepId": "lockboxPlacementVerification", "type": "page", "header": {"title": {"type": "static", "value": "Lockbox Placement Verification"}, "progress": "1/3"}, "title": "Sunlight exposure", "components": [{"type": "RadioSelect", "key": "lockboxPlacementStatus", "options": {"wrapInCard": true, "label": "Is the lockbox in a shaded area or away from direct sunlight?", "choices": [{"label": "Yes, the lockbox is in a shaded area or away from direct sunlight", "value": "shaded"}, {"label": "No, the lockbox is directly exposed to sun", "value": "exposed"}]}}, {"type": "PhotoCapture", "key": "lockboxPhoto", "linkedEntities": ["Stop__c", "Daily_Schedule__c"], "validations": [{"type": "Required", "validationErrorMsg": "Please take a photo of the empty lockbox."}], "options": {"label": "Photo of the lockbox", "cameraTitle": "Take a photo of the lockbox", "wrapInCard": true}}], "actions": [{"label": {"enabled": "Continue", "disabled": "Complete all steps to continue"}, "type": "navigate", "targetStepId": "lockboxThermometerPhoto", "options": {"requiresValidation": true, "variant": "primary", "priority": "primary"}}]}, {"stepId": "lockboxThermometerPhoto", "type": "page", "header": {"title": {"type": "static", "value": "Lockbox thermometer"}, "progress": "2/3"}, "title": "Lockbox thermometer", "leftIcon": "thermometer", "subtitle": "Take a photo of the thermometer inside the lockbox, or the empty lockbox if it's missing.", "components": [{"type": "PhotoCapture", "key": "thermometerPhoto", "linkedEntities": ["Stop__c", "Daily_Schedule__c"], "validations": [{"type": "Required", "validationErrorMsg": "Please take a photo of the empty lockbox."}], "options": {"label": "Photo of the thermometer", "cameraTitle": "Take a photo of the thermometer", "wrapInCard": true}}], "actions": [{"label": {"enabled": "Continue", "disabled": "Take a photo to continue"}, "type": "navigate", "targetStepId": "temperatureRecording", "options": {"requiresValidation": true, "variant": "filled", "priority": "primary"}}]}, {"stepId": "temperatureRecording", "type": "page", "header": {"title": {"type": "static", "value": "Record temperature"}, "progress": "3/3"}, "title": "Temperature record", "leftIcon": "thermometer", "subtitle": "Please take a reading of the thermometer inside the lockbox to log the current temperature", "components": [{"type": "Slide<PERSON>", "key": "lockboxTemperature", "options": {"label": "Lockbox temperature", "min": 50, "max": 104, "step": 6, "defaultValue": 77, "suffix": "°F"}}], "actions": [{"label": "Confirm temperature", "type": "invokeAction", "actionId": "confirmTemperature", "options": {"variant": "filled", "priority": "primary", "requiresValidation": true}, "onTapAction": [{"keyValuePairs": [{"key": "protocolStepId", "value": "LockboxThermometerVerification"}], "actionType": "setValue"}]}, {"label": "No thermometer in lockbox", "type": "invokeAction", "actionId": "noThermometer", "options": {"variant": "outlined", "priority": "secondary"}, "onTapAction": [{"keyValuePairs": [{"key": "Field_Value__c", "value": "No Thermometer Found"}, {"key": "Field_Comments__c", "value": "Driver reported no thermometer present in lockbox"}, {"key": "protocolStepId", "value": "LockboxThermometerVerification"}], "actionType": "setValue"}]}]}], "actions": {"confirmTemperature": {"type": "save", "after": {"actionId": "close"}, "objects": [{"name": "Protocol_Verification__c", "fields": ["Stop__c", "Route_Summary__c", "Field_Value__c", "Field_Comments__c", "Daily_Schedule__c", "Protocol_Step_Id__c"], "action": "create"}], "before": [{"type": "transform", "operation": "mapValueFromContext", "sourceFields": [{"condition": {"all": [{"field": "Daily_Schedule__c.Id", "operator": "!==", "value": null}]}, "value": "Daily_Schedule__c.Id"}], "destinationField": "Protocol_Verification__c.Daily_Schedule__c"}, {"type": "transform", "operation": "mapValueFromContext", "sourceFields": [{"condition": {"all": [{"field": "Stop__c.Id", "operator": "!==", "value": null}]}, "value": "Stop__c.Id"}], "destinationField": "Protocol_Verification__c.Stop__c"}, {"type": "transform", "operation": "mapValueFromContext", "sourceFields": [{"condition": {"all": [{"field": "Route_Summary__c.Id", "operator": "!==", "value": null}]}, "value": "Route_Summary__c.Id"}], "destinationField": "Protocol_Verification__c.Route_Summary__c"}, {"type": "transform", "operation": "combine", "sourceFields": ["lockboxTemperature"], "destinationField": "Protocol_Verification__c.Field_Value__c"}, {"type": "transform", "operation": "combine", "sourceFields": ["lockboxPlacementStatus"], "destinationField": "Protocol_Verification__c.Field_Comments__c"}, {"type": "transform", "operation": "combine", "sourceFields": ["protocolStepId"], "destinationField": "Protocol_Verification__c.Protocol_Step_Id__c"}]}, "noThermometer": {"type": "save", "after": {"actionId": "close"}, "objects": [{"name": "Protocol_Verification__c", "fields": ["Stop__c", "Route_Summary__c", "Field_Value__c", "Field_Comments__c", "Daily_Schedule__c", "Protocol_Step_Id__c"], "action": "create"}], "before": [{"type": "transform", "operation": "mapValueFromContext", "sourceFields": [{"condition": {"all": [{"field": "Daily_Schedule__c.Id", "operator": "!==", "value": null}]}, "value": "Daily_Schedule__c.Id"}], "destinationField": "Protocol_Verification__c.Daily_Schedule__c"}, {"type": "transform", "operation": "mapValueFromContext", "sourceFields": [{"condition": {"all": [{"field": "Stop__c.Id", "operator": "!==", "value": null}]}, "value": "Stop__c.Id"}], "destinationField": "Protocol_Verification__c.Stop__c"}, {"type": "transform", "operation": "mapValueFromContext", "sourceFields": [{"condition": {"all": [{"field": "Route_Summary__c.Id", "operator": "!==", "value": null}]}, "value": "Route_Summary__c.Id"}], "destinationField": "Protocol_Verification__c.Route_Summary__c"}, {"type": "transform", "operation": "combine", "sourceFields": ["protocolStepId"], "destinationField": "Protocol_Verification__c.Protocol_Step_Id__c"}, {"type": "transform", "operation": "combine", "sourceFields": ["Field_Comments__c"], "destinationField": "Protocol_Verification__c.Field_Comments__c"}, {"type": "transform", "operation": "combine", "sourceFields": ["Field_Value__c"], "destinationField": "Protocol_Verification__c.Field_Value__c"}]}}}