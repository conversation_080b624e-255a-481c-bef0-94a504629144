{"id": "enhanced-protocol", "label": "Enhanced Protocol Template", "version": "1.0.0", "protocolType": "preArriveStop", "executionCriteria": {"all": [{"field": "summary.Number_Of_Open_Stops__c", "operator": "===", "value": 0}, {"field": null, "operator": "custom", "customOperator": "minutesSincePreviousStopClosure", "value": 5}]}, "steps": [{"stepId": "pageStep", "type": "page", "title": "Regular Page Step", "components": [{"type": "PhotoDisplay", "key": "photoDisplay", "options": {"label": "Example photos", "photos": [{"type": "url", "source": "https://example.com/photo.png"}, {"type": "path", "source": "assets/photos/local.png"}]}}, {"type": "DisplayText", "key": "displayText", "options": {"type": "title", "content": "Display Text Example", "align": "center"}}, {"type": "PhotoCapture", "key": "photoCapture", "options": {"label": "Capture Photo", "linkedEntities": ["summary.Id"], "photoTitle": "Photo_Title"}}, {"type": "TextInput", "key": "textInput", "options": {"label": "Text Input", "placeholder": "Enter text here"}, "validations": [{"type": "Required", "validationErrorMsg": "This field is required"}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "value": 5, "validationErrorMsg": "Minimum 5 characters required"}], "displayIf": {"field": "someField", "operator": "===", "value": true}}, {"type": "Checkbox", "key": "checkbox", "label": "Checkbox Example", "options": {"defaultValue": false}}, {"type": "Icon", "key": "icon", "options": {"name": "timer"}}], "actions": [{"label": "Continue", "type": "navigate", "targetStepId": "modalStep", "priority": "primary"}]}, {"stepId": "modalStep", "type": "modal", "title": "Modal Step Example", "components": [{"type": "DisplayText", "key": "modalText", "options": {"content": "Modal Content", "type": "subtitle", "align": "center"}}], "actions": [{"label": "Close", "type": "close", "targetStepId": null, "priority": "secondary"}, {"label": "Submit", "type": "invokeAction", "actionId": "submitProtocol", "priority": "primary"}]}], "actions": {"submitProtocol": {"type": "save", "objects": [{"name": "Protocol", "fields": ["status"], "action": "update"}]}}}