{"id": "PreventStopClosureIfUnder5Min", "label": "Prevent Closing Stop If Previous Stop < 5 Minutes Ago", "version": "1.0.0", "protocolType": "preArriveStop", "executionCriteria": {"all": [{"field": "Stop__c.Completed_Time__c", "operator": "custom", "customOperator": "isWithinTimeSince", "value": 300}, {"field": "Route_Summary__c.Rapid_Closure_Warning__c", "operator": "===", "value": true}]}, "steps": [{"stepId": "blockStopArrive", "type": "modal", "components": [{"type": "Icon", "key": "timer", "options": {"name": "stopwatchWithBackground"}}, {"type": "DisplayText", "key": "arrivalBlockedMessage", "options": {"content": "{{Stop__c.Name}} was completed less than 5 minutes ago", "type": "title", "align": "center"}}, {"type": "DisplayText", "key": "arrivalBlockedMessage", "options": {"content": "You must wait at least 5 minutes between stops. Please wait until the timer has elapsed to mark next stop as arrived", "type": "subtitle", "align": "center"}}], "actions": [{"label": "OK", "type": "close", "targetStepId": null}]}], "actions": {}}