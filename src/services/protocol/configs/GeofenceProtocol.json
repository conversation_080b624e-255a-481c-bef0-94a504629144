{"id": "TriggerOnGeofenceEntry", "label": "Trigger On Geofence Entry", "version": "1.0.0", "protocolType": "postArriveStop", "executionCriteria": {"all": [{"field": "Stop__c.driverCoordinates", "operator": "custom", "customOperator": "isOutsideGeofence", "reference": {"lat": "Stop__c.Stop_Coordinates__Latitude__s", "lon": "Stop__c.Stop_Coordinates__Longitude__s", "radius": "effectiveGeofenceThreshold"}}]}, "steps": [{"stepId": "geofenceEntryModal", "type": "modal", "components": [{"type": "Icon", "key": "geofenceIcon", "options": {"name": "mapMarkerCheckWithBackground"}}, {"type": "DisplayText", "key": "geofenceEntryTitle", "options": {"content": "Geofence: Out of bounds", "type": "title", "align": "center"}}, {"type": "DisplayText", "key": "geofenceEntrySubtitle", "options": {"content": "You are currently {{Stop__c.driverDistanceFromStop}} miles away from {{Stop__c.Name}}. You must be within {{Stop__c.geofenceThresholdInMiles}} miles to check in.", "type": "subtitle", "align": "center"}}], "actions": [{"label": "Close", "type": "close", "targetStepId": null}]}], "actions": {}}