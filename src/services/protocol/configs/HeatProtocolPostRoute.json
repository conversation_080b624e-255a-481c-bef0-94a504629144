{"id": "PostRouteSurvey", "priority": 1, "external_id": "", "label": "Heat protocol: Post-route", "description": "Must be completed after route completion", "version": "1.0.0", "protocolType": "routeComplete", "executionCriteria": {"all": [{"field": "Route_Summary__c.Status__c", "operator": "===", "value": "Complete"}]}, "steps": [{"stepId": "initialPostRouteChecklistStep", "type": "page", "header": {"title": {"type": "dynamic", "value": "Route_Summary__c.Name"}, "progress": "1/6"}, "title": "Post route checklist", "components": [{"type": "CheckboxGroup", "key": "initialPostRouteChecklist", "validations": [{"type": "<PERSON><PERSON><PERSON><PERSON>", "value": 2, "validationErrorMsg": "Please confirm all items before proceeding."}], "options": {"wrapInCard": false, "style": {"backgroundColor": "#FFFFFF"}, "label": "Confirm completion of the following:", "choices": [{"label": "Area where samples are stored is cleared", "value": "sampleStorageAreaCleared"}, {"label": "Capture a photo which shows that your cooler is empty", "value": "readyToTakePhoto"}]}}], "actions": [{"label": {"enabled": "Continue", "disabled": "Confirm protocols to complete "}, "type": "navigate", "targetStepId": "emptyCoolerPhotoStep", "options": {"variant": "filled", "requiresValidation": true}}]}, {"stepId": "emptyCoolerPhotoStep", "type": "page", "header": {"title": {"type": "dynamic", "value": "Route_Summary__c.Name"}, "progress": "2/6"}, "title": "Take a photo of empty cooler in vehicle", "components": [{"type": "Image", "key": "emptyCoolerInVehicleStepExamplePhotos", "options": {"label": "Example photos", "multiple": true, "readonly": true, "uris": ["assets/images/survey_empty_cooler.webp"]}}, {"type": "DisplayText", "key": "emptyCoolerPhotoInstruction", "options": {"type": "title", "wrapInCard": true, "content": "Photo should be clear "}}, {"type": "DisplayText", "key": "emptyCoolerPhotoInstruction", "options": {"type": "title", "wrapInCard": true, "content": "Should capture the entire cooler"}}], "actions": [{"label": "Take photo", "type": "capturePhoto", "targetStepId": "coolerStorageAreaPhotoStep", "targetKey": "emptyCoolerInVehicleStepExamplePhotos", "linkedEntities": ["Route_Summary__c"], "options": {"icon": "camera", "iconPosition": "left", "cameraScreenTitle": "Take a photo of empty cooler"}}]}, {"stepId": "coolerStorageAreaPhotoStep", "type": "page", "header": {"title": {"type": "dynamic", "value": "Route_Summary__c.Name"}, "progress": "3/6"}, "title": "Take photo of area where cooler is stored", "components": [{"type": "Image", "key": "areaWhereCoolerIsStoredPhotos", "options": {"label": "Example photos", "multiple": true, "readonly": true, "uris": ["assets/images/survey_cooler_area.webp", "assets/images/survey_cooler_area_1.webp"]}}, {"type": "DisplayText", "key": "coolerStorageAreaPhotoInstruction", "options": {"type": "title", "wrapInCard": true, "content": "Photo should be clear"}}, {"type": "DisplayText", "key": "coolerStorageAreaPhotoInstruction", "options": {"type": "title", "wrapInCard": true, "content": "Should capture the entire cooler"}}], "actions": [{"label": "Take photo", "type": "capturePhoto", "linkedEntities": ["Route_Summary__c"], "targetKey": "areaWhereCoolerIsStoredPhotos", "targetStepId": "sampleHandlingAreaPhotoStep", "options": {"icon": "camera", "iconPosition": "left", "cameraScreenTitle": "Take a photo of where cooler is stored"}}]}, {"stepId": "sampleHandlingAreaPhotoStep", "type": "page", "header": {"title": {"type": "dynamic", "value": "Route_Summary__c.Name"}, "progress": "4/6"}, "title": "Take photo of area where samples are handled", "components": [{"type": "Image", "key": "areaWhereSamplesAreHandledPhotos", "options": {"label": "Example photos", "multiple": true, "readonly": true, "uris": ["assets/images/survey_area_specimens_handled.webp"]}}, {"type": "DisplayText", "key": "sampleHandlingAreaPhotoInstruction", "options": {"type": "title", "wrapInCard": true, "content": "Front driver or passenger seat area "}}], "actions": [{"label": "Take photo", "type": "capturePhoto", "linkedEntities": ["Route_Summary__c"], "targetKey": "areaWhereSamplesAreHandledPhotos", "targetStepId": "logCoolerTemperatureStep", "options": {"icon": "camera", "iconPosition": "left", "cameraScreenTitle": "Take photo of area where samples are handled"}}]}, {"stepId": "logCoolerTemperatureStep", "type": "page", "header": {"title": {"type": "dynamic", "value": "Route_Summary__c.Name"}, "progress": "5/6"}, "title": "Temperature record", "leftIcon": "thermometer", "subtitle": "Please take a reading of the thermometer inside your cooler to log the current temperature", "components": [{"type": "Slide<PERSON>", "key": "coolerTemperature", "options": {"label": "Lockbox temperature", "min": 50, "max": 104, "step": 6, "defaultValue": 77, "suffix": "°F"}}], "actions": [{"label": "Continue", "type": "navigate", "targetStepId": "routeProtocolConfirmationStep", "options": {"variant": "filled", "requiresValidation": true}}]}, {"stepId": "routeProtocolConfirmationStep", "header": {"title": {"type": "dynamic", "value": "Route_Summary__c.Name"}, "progress": "6/6"}, "type": "page", "title": "Confirm that the following route protocols were completed", "components": [{"type": "CheckboxGroup", "key": "protocolCompleteConfirmationStep", "validations": [{"type": "<PERSON><PERSON><PERSON><PERSON>", "value": 4, "validationErrorMsg": "Please confirm all items before proceeding"}], "options": {"wrapInCard": false, "style": {"backgroundColor": "#ffffff"}, "label": "Confirm that the following route protocols were completed", "choices": [{"label": "No customer sample was left behind", "value": "noCustomerSampleLeftBehind"}, {"label": "Samples were not left in vehicles for over 12 hours", "value": "samplesNotLeftInVehiclesForOver12Hours"}, {"value": "youAreResponsibleForDeliveringAllPickups", "label": "You are responsible for delivering all pickups "}, {"value": "leavingPropertyBehindMayResultInPenalties", "label": "Leaving property behind may result in penalties"}]}}], "actions": [{"label": {"enabled": "Complete post-route survey", "disabled": "Confirm protocols to complete"}, "type": "invokeAction", "actionId": "completePostRouteSurvey", "options": {"requiresValidation": true}, "onTapAction": [{"keyValuePairs": [{"key": "protocolStepId", "value": "PostRouteSurvey"}], "actionType": "setValue"}]}]}], "actions": {"completePostRouteSurvey": {"type": "save", "after": {"actionId": "confirmation", "options": {"type": "protocol", "subType": "protocol_completed"}}, "before": [{"type": "transform", "operation": "combine", "sourceFields": ["coolerTemperature"], "destinationField": "Protocol_Verification__c.Field_Value__c"}, {"type": "transform", "operation": "mapValueFromContext", "sourceFields": [{"condition": {"all": [{"field": "Route_Summary__c.Id", "operator": "!==", "value": null}]}, "value": "Route_Summary__c.Id"}], "destinationField": "Protocol_Verification__c.Route_Summary__c"}, {"type": "transform", "operation": "combine", "sourceFields": ["protocolStepId"], "destinationField": "Protocol_Verification__c.Protocol_Step_Id__c"}, {"type": "transform", "operation": "mapValueFromContext", "sourceFields": [{"condition": {"all": [{"field": "Daily_Schedule__c.Id", "operator": "!==", "value": null}]}, "value": "Daily_Schedule__c.Id"}], "destinationField": "Protocol_Verification__c.Daily_Schedule__c"}], "objects": [{"name": "Protocol_Verification__c", "fields": ["Route_Summary__c", "Field_Value__c", "Daily_Schedule__c", "Protocol_Step_Id__c"], "action": "create"}]}}}