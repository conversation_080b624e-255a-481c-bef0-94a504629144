/**
 * BlurDetectionService
 * 
 * A service that provides blur detection functionality for images using the blurry-detector package.
 * This service acts as a wrapper around the blurry-detector library and handles potential 
 * React Native compatibility issues.
 */

interface BlurDetectionResult {
  isBlurry: boolean;
  variance: number;
  threshold: number;
}

interface BlurDetectionError {
  success: false;
  error: string;
}

type BlurDetectionResponse = BlurDetectionResult | BlurDetectionError;

class BlurDetectionService {
  private detector: any;
  private threshold: number;

  constructor(threshold: number = 300) {
    this.threshold = threshold;
    try {
      // Dynamically import the blurry-detector package
      const BlurryDetector = require('blurry-detector');
      this.detector = new BlurryDetector(threshold);
    } catch (error) {
      console.error('BlurDetectionService: Failed to initialize blurry-detector:', error);
      this.detector = null;
    }
  }

  /**
   * Analyzes an image to determine if it's blurry
   * @param imagePath - Path to the image file
   * @returns Promise<BlurDetectionResponse> - Result containing blur status and metrics
   */
  async analyzeImage(imagePath: string): Promise<BlurDetectionResponse> {
    try {
      if (!this.detector) {
        return {
          success: false,
          error: 'BlurryDetector not initialized. This may be due to React Native compatibility issues.',
        };
      }

      if (!imagePath) {
        return {
          success: false,
          error: 'Image path is required',
        };
      }

      // Check if the image exists and is accessible
      const cleanPath = imagePath.replace('file://', '');
      
      // Get the Laplacian variance
      const variance = await this.detector.computeLaplacianVariance(cleanPath);
      const isBlurry = await this.detector.isImageBlurry(cleanPath);

      return {
        isBlurry,
        variance,
        threshold: this.threshold,
      };
    } catch (error) {
      console.error('BlurDetectionService: Error analyzing image:', error);
      return {
        success: false,
        error: `Failed to analyze image: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  }

  /**
   * Updates the blur detection threshold
   * @param newThreshold - New threshold value (lower values are more sensitive to blur)
   */
  updateThreshold(newThreshold: number): void {
    this.threshold = newThreshold;
    if (this.detector) {
      try {
        const BlurryDetector = require('blurry-detector');
        this.detector = new BlurryDetector(newThreshold);
      } catch (error) {
        console.error('BlurDetectionService: Failed to update threshold:', error);
      }
    }
  }

  /**
   * Checks if the service is available and properly initialized
   * @returns boolean - True if the service is ready to use
   */
  isAvailable(): boolean {
    return this.detector !== null;
  }
}

// Export a singleton instance
export const blurDetectionService = new BlurDetectionService();
export default BlurDetectionService;
