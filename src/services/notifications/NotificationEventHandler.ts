import { Alert } from 'react-native';
import { API_ENDPOINT_KEYS } from '~/api/apiEndpoints';
import { postData } from '~/api/apiService';
import {
  AdditionalData,
  NewWorkOrderAssignedAdditionalData,
  NotificationType,
  ShipmentPickupAddedAdditionalData,
  StopRemovedAdditionalData,
} from '~/types/notifications.types';
import { TWO_MINUTES_IN_MS } from '~/utils/constants';
import { syncDown } from '~/services/sync/syncDown';
import { NotificationHandleInstructions } from '~/utils/notifications';

type NotificationMap = {
  count: number;
  lastProcessedTime: number | null;
};

let notificationMap: Record<string, NotificationMap> = {
  [NotificationType.stopAdded]: {
    count: 0,
    lastProcessedTime: null,
  },
  [NotificationType.shipmentPickupAdded]: {
    count: 0,
    lastProcessedTime: null,
  },
};

export const handleNotificationEventForNonMultipleNotifications = async (
  notificationReceivedEvent: any,
  notificationType: NotificationType,
  additionalData: AdditionalData,
) => {
  const { count, lastProcessedTime } = notificationMap[notificationType];
  const notificationInstructions =
    NotificationHandleInstructions[notificationType];
  console.info(
    'NotificationEventHandler: handleNotificationEventForNonMultipleNotifications():',
    notificationInstructions,
  );

  if (
    additionalData &&
    notificationInstructions.dataRequired.every(
      key => additionalData[key as keyof AdditionalData],
    )
  ) {
    if (notificationInstructions.showMultipleNotifications === false) {
      const hasReachedMaxNotifications =
        notificationInstructions.maxNotifications &&
        count >= notificationInstructions.maxNotifications;
      const isWithinDebounceTime =
        lastProcessedTime === null
          ? false
          : notificationInstructions.debounceTimeInMs &&
            Date.now() - lastProcessedTime <
              notificationInstructions.debounceTimeInMs;
      console.info(
        'NotificationEventHandler: isWithinDebounceTime ',
        isWithinDebounceTime,
        ', hasReachedMaxNotifications ',
        hasReachedMaxNotifications,
      );

      if (hasReachedMaxNotifications || isWithinDebounceTime) {
        console.info(
          'NotificationEventHandler: handleNotificationEventForNonMultipleNotifications():',
          'Max notifications reached and within debounce time, ignoring notification',
        );

        if (isWithinDebounceTime && count > 0) {
          // Reset the count if the notification is within the debounce time
          notificationMap[notificationType].count = 0;
        }

        return;
      }

      notificationMap[notificationType].count =
        notificationMap[notificationType].count + 1;
      notificationMap[notificationType].lastProcessedTime = Date.now();

      if (notificationReceivedEvent) {
        const notification = notificationReceivedEvent.getNotification();
        notificationReceivedEvent.complete(notification);
      }

      if (notificationInstructions.shouldRefresh) {
        await syncDown();
      }
    }
  } else {
    console.info(
      'NotificationEventHandler: handleNotificationEventForNonMultipleNotifications():',
      'Additional data does not contain all required data, ignoring notification',
    );
  }

  console.info(
    'NotificationEventHandler: handleNotificationEventForNonMultipleNotifications():',
    'notificationMap',
    notificationMap,
  );
};

export const handleStopActionNotificationEvent = async (
  notificationReceivedEvent: any,
  additionalData: AdditionalData,
  isForeground = false,
  isOnClick = false,
) => {
  handleNotificationEventForNonMultipleNotifications(
    notificationReceivedEvent,
    NotificationType.stopAdded,
    additionalData,
  );
};

export const handleStopRemovedNotificationEvent = async (
  notificationReceivedEvent: any,
  additionalData: StopRemovedAdditionalData,
  isForeground = false,
  isOnClick = false,
) => {
  if (additionalData?.stopId) {
    // TODO: Remove the stop and all its services and parcels from the database
  }
};

export const handleShipmentPickupAddedNotificationEvent = async (
  notificationReceivedEvent: any,
  additionalData: ShipmentPickupAddedAdditionalData,
  isForeground = false,
  isOnClick = false,
) => {
  handleNotificationEventForNonMultipleNotifications(
    notificationReceivedEvent,
    NotificationType.shipmentPickupAdded,
    additionalData,
  );
};

const confirmWorkOrderAssignment = async (workOrderId: string) => {
  const requestData = {
    Assignment_Confirmation_Status__c: 'Confirmed',
  };

  postData({
    onSync: (response, error) => {
      if (response) {
        console.log(
          'NotificationEventHandler: confirmWorkOrderAssignment(): response',
          response,
        );
      } else if (error) {
        console.error(
          'NotificationEventHandler: confirmWorkOrderAssignment(): error',
          error,
        );
      }
    },
    requestIdentifier: API_ENDPOINT_KEYS.PATCH_WORK_ORDER,
    requestData: {
      ...requestData,
      entityId: workOrderId,
    },
    method: 'patch',
  });

  return null;
};

export const handleNewWorkOrderAssignedNotificationEvent = async (
  notificationReceivedEvent: any,
  additionalData: NewWorkOrderAssignedAdditionalData,
  isForeground = false,
  isOnClick = false,
) => {
  const { workOrderId, timeAssigned } = additionalData;

  if (isForeground) {
    Alert.alert(
      NotificationHandleInstructions[NotificationType.newWorkOrderAssigned]
        .title,
      NotificationHandleInstructions[NotificationType.newWorkOrderAssigned]
        .message,
      [
        {
          text: 'Confirm',
          onPress: () => confirmWorkOrderAssignment(workOrderId),
        },
      ],
      { cancelable: false },
    );

    if (notificationReceivedEvent) {
      const notification = notificationReceivedEvent.getNotification();
      notificationReceivedEvent.complete(notification);
    }
  }

  const { dataRequired } =
    NotificationHandleInstructions[NotificationType.newWorkOrderAssigned];
  const isWithin2Minutes =
    new Date(timeAssigned).getTime() - new Date().getTime() < TWO_MINUTES_IN_MS;

  if (
    dataRequired.every(
      key => additionalData[key as keyof NewWorkOrderAssignedAdditionalData],
    ) &&
    isWithin2Minutes &&
    isOnClick
  ) {
    confirmWorkOrderAssignment(workOrderId);
  }
};

export const handleResyncNotificationEvent = async (
  notificationReceivedEvent: any,
  additionalData: AdditionalData,
  isForeground = false,
) => {
  handleNotificationEventForNonMultipleNotifications(
    null,
    NotificationType.autoSyncUp,
    additionalData,
  );

  if (isForeground) {
    const notification = notificationReceivedEvent.getNotification();
    notificationReceivedEvent.complete(notification);
  }
};
