import {
  handleStopActionNotificationEvent,
  handleStopRemovedNotificationEvent,
  handleResyncNotificationEvent,
  handleNewWorkOrderAssignedNotificationEvent,
  handleShipmentPickupAddedNotificationEvent,
} from '~/services/notifications/NotificationEventHandler';
import { NotificationType } from '~/types/notifications.types';

export const handleNotificationOpenedData = (openedEvent: any) => {
  const openedEventAdditionalData = openedEvent.notification.additionalData;
  const notificationType = openedEventAdditionalData?.type;

  switch (true) {
    case notificationType === NotificationType.stopAdded:
      handleStopActionNotificationEvent(
        null,
        openedEventAdditionalData,
        false,
        true,
      );
      break;

    case notificationType === NotificationType.autoSyncUp:
      handleResyncNotificationEvent(null, openedEventAdditionalData, false);
      break;

    case notificationType === NotificationType.stopRemoved:
      handleStopRemovedNotificationEvent(
        null,
        openedEventAdditionalData,
        false,
        true,
      );
      break;

    case notificationType === NotificationType.shipmentPickupAdded:
      handleShipmentPickupAddedNotificationEvent(
        null,
        openedEventAdditionalData,
        false,
        true,
      );
      break;

    case notificationType === NotificationType.newWorkOrderAssigned:
      handleNewWorkOrderAssignedNotificationEvent(
        null,
        openedEventAdditionalData,
        false,
        true,
      );
      break;

    default:
      break;
  }
};

export const handleForegroundNotification = (
  notificationReceivedEvent: any,
) => {
  const notification = notificationReceivedEvent.getNotification();
  // // silence notification when app is in foreground
  // notificationReceivedEvent.complete(undefined);
  const additionalData = notification.additionalData?.data;
  const notificationType = notification.additionalData?.type;

  switch (true) {
    case notificationType === NotificationType.stopAdded:
      handleStopActionNotificationEvent(
        notificationReceivedEvent,
        additionalData,
        true,
        false,
      );
      break;

    case notificationType === NotificationType.autoSyncUp:
      handleResyncNotificationEvent(
        notificationReceivedEvent,
        additionalData,
        true,
      );
      break;

    case notificationType === NotificationType.stopRemoved:
      handleStopRemovedNotificationEvent(
        notificationReceivedEvent,
        additionalData,
        true,
        false,
      );
      break;

    case notificationType === NotificationType.shipmentPickupAdded:
      handleShipmentPickupAddedNotificationEvent(
        notificationReceivedEvent,
        additionalData,
        true,
        false,
      );
      break;

    case notificationType === NotificationType.newWorkOrderAssigned:
      handleNewWorkOrderAssignedNotificationEvent(
        notificationReceivedEvent,
        additionalData,
        true,
        false,
      );
      break;

    default:
      break;
  }
};
