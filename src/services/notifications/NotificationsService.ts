import { useEffect } from 'react';
import { Alert } from 'react-native';
import Config from 'react-native-config';
import { OneSignal, LogLevel } from 'react-native-onesignal';
import { openSettings } from 'react-native-permissions';
import en from '~/localization/en';
import {
  handleForegroundNotification,
  handleNotificationOpenedData,
} from '~/services/notifications/NotificationHandler';

export const initializeNotificationService = () => {
  try {
    OneSignal.Debug.setLogLevel(LogLevel.Verbose);
    const oneSignalAppId: string = Config.ONE_SIGNAL_APP_ID as string;
    OneSignal.initialize(oneSignalAppId);
  } catch (error) {
    console.error(
      'NotificationsService: initializeNotificationService(): ',
      error,
    );
  }
};

export const logoutOneSignalUser = () => {
  try {
    OneSignal.logout();
  } catch (error) {
    console.error('NotificationsService: logoutOneSignalUser(): ', error);
  }
};

export const setOneSignalUser = () => {
  try {
    OneSignal.login((global as any).userId);
  } catch (error) {
    console.error('NotificationsService: setOneSignalUser(): ', error);
  }
};

export const setOneSignalUserInfo = (email: string) => {
  try {
    OneSignal.User.addEmail(email);
  } catch (error) {
    console.error('NotificationsService: setOneSignalUserInfo(): ', error);
  }
};

export const getOneSignalId = async () => {
  try {
    return await OneSignal.User.getOnesignalId();
  } catch (error) {
    console.error('NotificationsService: getOneSignalId(): ', error);
  }
};

export const clearOneSignalHandlers = () => {
  try {
    OneSignal.Notifications.removeEventListener('permissionChange', () => {
      console.info(
        'NotificationsService: clearOneSignalHandlers(): permission changed listener removed',
      );
    });

    OneSignal.Notifications.removeEventListener('click', () => {
      console.info(
        'NotificationsService: clearOneSignalHandlers(): On click listener removed',
      );
    });
  } catch (error) {
    console.error('NotificationsService: clearOneSignalHandlers(): ', error);
  }
};

export const useOneSignalObservers = () => {
  useEffect(() => {
    OneSignal.Notifications.addEventListener(
      'permissionChange',
      (granted: boolean) => {
        console.info('NotificationsService: permission changed:', granted);

        if (granted === false) {
          Alert.alert(
            en.notification_permission,
            en.push_notifications_disabled,
            [
              {
                text: en.open_settings,
                onPress: () => openSettings('application'),
              },
              { text: en.cancel },
            ],
          );
        }
      },
    );

    OneSignal.Notifications.addEventListener('foregroundWillDisplay', event => {
      console.info('Foreground notification:', event.getNotification());
      handleForegroundNotification(event);
    });

    OneSignal.Notifications.addEventListener('click', event => {
      console.info('NotificationsService: notification clicked:', event);
      handleNotificationOpenedData(event);
    });

    return () => {
      OneSignal.Notifications.removeEventListener('permissionChange', () => {
        console.info(
          'NotificationsService: useOneSignalObservers(): permission changed listener removed',
        );
      });

      OneSignal.Notifications.removeEventListener('click', () => {
        console.info(
          'NotificationsService: useOneSignalObservers(): On click listener removed',
        );
      });
    };
  }, []);
};
