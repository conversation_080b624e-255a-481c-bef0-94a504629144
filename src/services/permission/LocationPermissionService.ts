/**
 * LocationPermissionService handles checking and requesting location permissions,
 * including "always-on" location access when applicable.
 */

import { Platform } from 'react-native';
import {
  request,
  RESULTS,
  Permission,
  PERMISSIONS,
  checkMultiple,
  checkLocationAccuracy,
} from 'react-native-permissions';

import { PermissionType } from '~/types/permission.types';
import PermissionAlert from '~/services/permission/PermissionAlert';

const IOS_VERSION_WITH_LOCATION_ACCURACY = 14;

const foregroundLocationPermission: Permission = Platform.select({
  ios: PERMISSIONS.IOS.LOCATION_WHEN_IN_USE,
  android: PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION,
}) as Permission;

const backgroundLocationPermission: Permission = Platform.select({
  ios: PERMISSIONS.IOS.LOCATION_ALWAYS,
  android: PERMISSIONS.ANDROID.ACCESS_BACKGROUND_LOCATION,
}) as Permission;

export const LocationPermissionService = {
  /**
   * Checks if the location permission, including "always-on" for background access, is granted.
   */
  checkPermission: async (): Promise<boolean> => {
    const locationPermissions = await checkMultiple([
      foregroundLocationPermission,
      backgroundLocationPermission,
    ]);

    const isPermissionGranted =
      locationPermissions[foregroundLocationPermission] === RESULTS.GRANTED &&
      locationPermissions[backgroundLocationPermission] === RESULTS.GRANTED;

    if (!isPermissionGranted) {
      return false;
    }

    if (Platform.OS === 'ios') {
      const majorVersionIOS = parseInt(Platform.Version, 10);

      if (majorVersionIOS >= IOS_VERSION_WITH_LOCATION_ACCURACY) {
        const locationAccuracy = await checkLocationAccuracy();
        return locationAccuracy === 'full';
      }
    }

    return isPermissionGranted;
  },
  /**
   * Requests location permission, including "always-on" for background access if applicable.
   */
  requestPermission: async (): Promise<boolean> => {
    const foregroundStatus = await request(foregroundLocationPermission);

    if (foregroundStatus !== RESULTS.GRANTED) {
      PermissionAlert.show(PermissionType.LOCATION);
      return false;
    }

    const backgroundStatus = await request(backgroundLocationPermission);

    if (backgroundStatus !== RESULTS.GRANTED) {
      PermissionAlert.show(PermissionType.LOCATION);
      return false;
    }

    if (Platform.OS === 'ios') {
      const locationAccuracy = await checkLocationAccuracy();

      if (locationAccuracy === 'reduced') {
        PermissionAlert.show(PermissionType.LOCATION);
        return false;
      }
    }

    return true;
  },
};
