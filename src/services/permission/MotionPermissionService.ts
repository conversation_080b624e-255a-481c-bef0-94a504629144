/**
 * MotionPermissionService handles checking and requesting motion/activity recognition permissions.
 */

import { Platform } from 'react-native';
import {
  check,
  request,
  RESULTS,
  PERMISSIONS,
  Permission,
} from 'react-native-permissions';
import { PermissionType } from '~/types/permission.types';
import PermissionAlert from '~/services/permission/PermissionAlert';

const motionPermission: Permission = Platform.select({
  ios: PERMISSIONS.IOS.MOTION,
  android: PERMISSIONS.ANDROID.ACTIVITY_RECOGNITION,
}) as Permission;

export const MotionPermissionService = {
  /**
   * Checks if the motion permission is granted.
   */
  checkPermission: async (): Promise<boolean> => {
    const status = await check(motionPermission);
    return status === RESULTS.GRANTED;
  },

  /**
   * Shows Open settings Alert if motion permission denied.
   */
  requestPermission: async (): Promise<boolean> => {
    const permissionStatus = await request(motionPermission);

    if (permissionStatus === RESULTS.GRANTED) {
      return true;
    } else {
      PermissionAlert.show(PermissionType.MOTION);
    }

    return false;
  },
};
