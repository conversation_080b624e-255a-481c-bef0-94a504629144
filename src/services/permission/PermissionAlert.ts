import { <PERSON><PERSON>, Al<PERSON><PERSON><PERSON>on, Platform } from 'react-native';
import { openSettings } from 'react-native-permissions';
import en from '~/localization/en';
import { PermissionType } from '~/types/permission.types';

const permissionDescriptions = {
  [PermissionType.CAMERA]: en.cameraPermissionDesc,
  [PermissionType.MOTION]: Platform.select({
    ios: en.motionPermissionDescIOS,
    android: en.motionPermissionDescAndroid,
  }),
  [PermissionType.LOCATION]: Platform.select({
    ios: en.locationPermissionDescIOS,
    android: en.locationPermissionDescAndroid,
  }),
  [PermissionType.NOTIFICATION]: en.push_notifications_disabled,
};

const permissionCancellable = {
  [PermissionType.CAMERA]: true,
  [PermissionType.LOCATION]: false,
  // PATCH: Motion permission is not available in simulators
  [PermissionType.MOTION]: __DEV__,
  [PermissionType.NOTIFICATION]: false,
};

const permissionAlertInProgress = new Set<PermissionType>();

const PermissionAlert = {
  show(permissionType: PermissionType) {
    if (permissionAlertInProgress.has(permissionType)) {
      return;
    }

    permissionAlertInProgress.add(permissionType);

    const description = permissionDescriptions[permissionType];

    Alert.alert(en.warning, description, createAlertButtons(permissionType), {
      onDismiss: () => {
        // NOTE: It is must on Android to clear the permissionAlertInProgress
        permissionAlertInProgress.delete(permissionType);
      },
    });
  },
};

const createAlertButtons = (permissionType: PermissionType) => {
  const buttons: AlertButton[] = [];

  if (permissionCancellable[permissionType]) {
    buttons.push({
      text: en.cancel,
      style: 'cancel',
      onPress: () => {
        permissionAlertInProgress.delete(permissionType);
        console.info(
          `User cancelled prompt to provide ${permissionType} permission`,
        );
      },
    });
  }

  buttons.push({
    text: en.open_settings,
    style: 'default',
    onPress: () => {
      permissionAlertInProgress.delete(permissionType);
      openSettings('application').catch(() => {
        Alert.alert(en.unable_to_open_settings);
      });
    },
  });

  return buttons;
};

export default PermissionAlert;
