/**
 * CameraPermissionService handles checking and requesting camera permissions.
 */

import { Platform } from 'react-native';
import {
  check,
  request,
  RESULTS,
  Permission,
  PERMISSIONS,
} from 'react-native-permissions';
import { PermissionType } from '~/types/permission.types';
import PermissionAlert from '~/services/permission/PermissionAlert';

const cameraPermission: Permission = Platform.select({
  ios: PERMISSIONS.IOS.CAMERA,
  android: PERMISSIONS.ANDROID.CAMERA,
}) as Permission;

export const CameraPermissionService = {
  /**
   * Checks if the camera permission is granted.
   */
  checkPermission: async (): Promise<boolean> => {
    const status = await check(cameraPermission);
    return status === RESULTS.GRANTED;
  },

  /**
   * Requests camera permission.
   */
  requestPermission: async () => {
    await request(cameraPermission);
  },

  /**
   * Shows Open settings Alert if camera permission denied.
   */
  forceRequestPermission: async (): Promise<boolean> => {
    const permissionStatus = await request(cameraPermission);

    if (permissionStatus === RESULTS.GRANTED) {
      return true;
    } else {
      PermissionAlert.show(PermissionType.CAMERA);
    }

    return false;
  },
};
