import {
  RESULTS,
  requestNotifications,
  checkNotifications,
} from 'react-native-permissions';

export const NotificationPermissionService = {
  /**
   * Checks if the notification permission is granted.
   */
  checkPermission: async (): Promise<boolean> => {
    const result = await checkNotifications();
    return result.status === RESULTS.GRANTED;
  },

  /**
   * Requests notification permission.
   */
  requestPermission: async () => {
    try {
      const { status: permissionStatus } = await checkNotifications();

      switch (permissionStatus) {
        case 'granted':
          return true;
        case 'blocked':
          return false;
        case 'denied':
          break;
        case 'limited':
          return false;
        case 'unavailable':
          return false;
        default:
          return false;
      }

      const result = await requestNotifications(['alert', 'sound', 'badge']);

      return result.status === RESULTS.GRANTED;
    } catch (error) {
      console.error(
        'NotificationPermissionService: requestPermission(): ',
        error,
      );
      throw error;
    }
  },
};
