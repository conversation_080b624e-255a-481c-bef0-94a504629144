/* eslint-disable no-unused-vars */
/**
 * Permission<PERSON><PERSON><PERSON> serves as a generic handler for managing and checking permissions,
 * while also utilizing specific permission services (e.g., Location, Camera) when needed.
 * It can handle individual permissions or an array of permissions.
 */

import { LocationPermissionService } from '~/services/permission/LocationPermissionService';
import { CameraPermissionService } from '~/services/permission/CameraPermissionService';
import { MotionPermissionService } from '~/services/permission/MotionPermissionService';
import { PermissionType } from '~/types/permission.types';
import PermissionAlert from '~/services/permission/PermissionAlert';
import { NotificationPermissionService } from './NotificationPermissionService';

/**
 * Function mapping for specific permission services.
 */
const PermissionServiceRegistry = {
  [PermissionType.LOCATION]: LocationPermissionService,
  [PermissionType.CAMERA]: CameraPermissionService,
  [PermissionType.MOTION]: MotionPermissionService,
  [PermissionType.NOTIFICATION]: NotificationPermissionService,
};

/**
 * Core PermissionHandler class. Can be extended to handle new permissions.
 */
const PermissionHandler = {
  permissionAlertInProgress: new Set<PermissionType>(),

  async checkPermissions(
    permissionTypes: PermissionType[],
  ): Promise<{ [key in PermissionType]: boolean }> {
    const results: { [key in PermissionType]: boolean } = {} as {
      [key in PermissionType]: boolean;
    };

    for (const permissionType of permissionTypes) {
      const isGranted =
        await PermissionServiceRegistry[permissionType].checkPermission();
      results[permissionType] = isGranted;
    }

    return results;
  },
  /**
   * Checks and requests a single permission.
   * @param {PermissionType} permissionType - The type of permission to handle.
   */
  async checkAndRequestPermission(
    permissionType: PermissionType,
  ): Promise<boolean> {
    if (PermissionServiceRegistry[permissionType]) {
      const service = PermissionServiceRegistry[permissionType];
      const isGranted = await service.checkPermission();

      if (!isGranted) {
        const result = await service.requestPermission();

        if (result === false) {
          PermissionAlert.show(permissionType);
        }

        return result ?? false;
      }

      return true;
    } else {
      throw new Error('Permission request not handled');
    }
  },

  /**
   * Checks and requests an array of permissions sequentially.
   * @param {PermissionType[]} permissionTypes - Array of permission types to handle.
   */
  async checkAndRequestPermissions(
    permissionTypes: PermissionType[],
  ): Promise<boolean> {
    for (const permissionType of permissionTypes) {
      const isGranted = await this.checkAndRequestPermission(permissionType);

      if (!isGranted) {
        return false;
      }
    }

    return true;
  },
};

export default PermissionHandler;
