import { Platform, Linking, Alert } from 'react-native';

export const callPhoneNumber = (phone: string) => {
  let number = phone;

  if (Platform.OS !== 'android') {
    number = `telprompt:${phone}`;
  } else {
    number = `tel:${phone}`;
  }

  if (Platform.OS === 'android') {
    return Linking.openURL(number);
  }

  if (Platform.OS === 'ios') {
    Linking.canOpenURL(number)
      .then(supported => {
        if (!supported) {
          Alert.alert('', 'Phone number not available');
        } else {
          return Linking.openURL(number);
        }
      })

      .catch(error =>
        console.error('CallService.ts: callPhoneNumber()', error),
      );
  } else {
    throw new Error(`Unsupported platform -${phone}`);
  }
};
