import { Platform, Linking, Alert } from 'react-native';

export const sendSMS = (phone: string) => {
  const smsUrl = `sms:${phone}`;

  if (Platform.OS === 'android') {
    return Linking.openURL(smsUrl);
  }

  if (Platform.OS === 'ios') {
    Linking.canOpenURL(smsUrl)
      .then(supported => {
        if (!supported) {
          Alert.alert('', 'Phone number not available for SMS');
        } else {
          return Linking.openURL(smsUrl);
        }
      })
      .catch(error => console.error('SmsService.ts: sendSMS(): ', error));
  } else {
    throw new Error(`Unsupported platform -${phone}`);
  }
};
