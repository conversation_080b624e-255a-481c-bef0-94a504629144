import { useEffect, useState, useCallback } from 'react';
import { API_ENDPOINT_KEYS } from '~/api/apiEndpoints';
import { fetchData, ApiResponse } from '~/api/apiService';

type RequestIdentifier =
  (typeof API_ENDPOINT_KEYS)[keyof typeof API_ENDPOINT_KEYS];
/**
 * This is a custom hook that serves as a wrapper to fetch data from a backend API.
 * It retrieves cached data first (if available), then fetches fresh data from the backend.
 *
 * @param {RequestIdentifier} requestIdentifier - A unique identifier for the API endpoint or resource being fetched.
 * @param {string} [id] - An optional identifier for fetching specific data, such as a resource ID.
 * @param {any} [requestData] - Additional parameters or request body data to include in the API call.
 *
 * @returns {Object} - An object containing the following properties:
 *   - `data` (any): The response data from the API. Initially `null` until data is fetched successfully.
 *   - `error` (string | null): An error message if the request fails. Defaults to `null`.
 *   - `loading` (boolean): A flag indicating if the request is in progress (`true`) or completed (`false`).
 *   - `refetch` (function): A function to manually re-trigger the API request, useful for refreshing the data.
 *
 * @example
 * // Example usage of the useFetchData hook
 * import React, { useEffect } from 'react';
 * import { useFetchData } from '~/hooks/useFetchData';
 * import { GET_ROUTE_LIST } from '~/api/apiEndpoints';
 *
 * const ExampleComponent = () => {
 *   const { data, error, loading, refetch } = useFetchData(GET_ROUTE_LIST);
 *
 *   useEffect(() => {
 *     if (data) {
 *       console.log("Fetched data:", data);
 *     }
 *   }, [data]);
 *
 *   if (loading) return <p>Loading...</p>;
 *   if (error) return <p>Error: {error}</p>;
 *
 *   return (
 *     <div>
 *       <button onClick={refetch}>Refresh Data</button>
 *       <pre>{JSON.stringify(data, null, 2)}</pre>
 *     </div>
 *   );
 * };
 *
 * export default ExampleComponent;
 */
const useFetchData = <T>(
  requestIdentifier: RequestIdentifier,
  requestData?: any,
) => {
  const [data, setData] = useState<T | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);

  const handleAPIResponse = useCallback(
    (response: ApiResponse<T> | null, apiError?: string) => {
      if (apiError) {
        setError(apiError);
      } else if (response) {
        setData(response as T);
      }
    },
    [],
  );

  const fetchAndSyncData = useCallback(async () => {
    setLoading(true);
    try {
      const result = await fetchData({
        onSync: handleAPIResponse,
        requestIdentifier,
        requestData,
      });

      setData(result);
      setError(null);
    } catch (err: any) {
      setError(`Error: ${err.message}`);
    } finally {
      setLoading(false);
    }
  }, [handleAPIResponse, requestIdentifier, requestData]);

  useEffect(() => {
    fetchAndSyncData();
  }, [fetchAndSyncData]);

  return { data, error, loading, refetch: fetchAndSyncData };
};

export { useFetchData };
