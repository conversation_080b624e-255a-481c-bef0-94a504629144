import { useQuery } from '@realm/react';
import { RouteSummarySchema } from '~/db/realm/schemas/route-summary.schema';
import { RouteSummary } from '~/types/routes.types';
import { ROUTE_STATUS } from '~/utils/constants';

export const useRouteQueries = (date: string) => {
  const routesWithHighTemp = useQuery<RouteSummary>({
    type: RouteSummarySchema.name,
    query: routeSummaryRecords =>
      routeSummaryRecords
        .filtered('Date__c == $0 AND High_Temperature__c != null', date)
        .sorted([['Planned_Start__c', false]]),
  });

  const routeSummariesForSelectedDate = useQuery<RouteSummary>({
    type: RouteSummarySchema.name,
    query: routeSummaryRecords =>
      routeSummaryRecords
        .filtered(
          'Date__c == $0 AND Status__c == $1 AND Number_of_Stops__c == 0',
          date,
          ROUTE_STATUS.Scheduled,
        )
        .sorted([['Planned_Start__c', false]]),
  });

  return { routesWithHighTemp, routeSummariesForSelectedDate };
};
