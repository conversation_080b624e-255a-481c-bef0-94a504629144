import { useCallback, useEffect, useRef } from 'react';
import { useAppState } from '~/hooks/useAppState';
import PermissionHandler from '~/services/permission/PermissionHandler';
import { PermissionType } from '~/types/permission.types';
import PermissionAlert from '~/services/permission/PermissionAlert';

export const usePermissionHandler = ({
  permissionTypes,
  successCallback,
}: {
  permissionTypes: PermissionType[];
  successCallback: () => void;
}) => {
  const callbackRef = useRef(successCallback);
  const permissionTypesRef = useRef(permissionTypes);
  const isFirstTimeForeground = useRef(true);
  const currentPermissionIndexRef = useRef(0);

  const handlePermissionSuccess = useCallback(() => {
    currentPermissionIndexRef.current = 0;
    callbackRef.current();
  }, []);

  const checkAndRequestPermissions = useCallback(async () => {
    const currentPermission =
      permissionTypesRef.current[currentPermissionIndexRef.current];

    if (!currentPermission) {
      handlePermissionSuccess();
      return;
    }

    const permissionResults = await PermissionHandler.checkPermissions([
      currentPermission,
    ]);
    const isGranted = permissionResults[currentPermission];

    if (isGranted) {
      currentPermissionIndexRef.current += 1;
      setTimeout(() => {
        checkAndRequestPermissions();
      }, 500);
      return;
    }

    const granted =
      await PermissionHandler.checkAndRequestPermission(currentPermission);

    if (!granted) {
      PermissionAlert.show(currentPermission);
      return;
    }

    currentPermissionIndexRef.current += 1;
    setTimeout(() => {
      checkAndRequestPermissions();
    }, 500);
  }, [handlePermissionSuccess]);

  const handleForegroundPermissions = useCallback(async () => {
    const currentPermission =
      permissionTypesRef.current[currentPermissionIndexRef.current];
    if (!currentPermission) return;

    const permissionResults = await PermissionHandler.checkPermissions([
      currentPermission,
    ]);
    const isGranted = permissionResults[currentPermission];

    if (isGranted) {
      currentPermissionIndexRef.current += 1;
      await checkAndRequestPermissions();
    } else if (
      currentPermission !== PermissionType.MOTION &&
      currentPermission !== PermissionType.NOTIFICATION
    ) {
      PermissionAlert.show(currentPermission);
    }
  }, [checkAndRequestPermissions]);

  const onForeground = useCallback(async () => {
    if (isFirstTimeForeground.current) {
      isFirstTimeForeground.current = false;
      return;
    }
    await handleForegroundPermissions();
  }, [handleForegroundPermissions]);

  useEffect(() => {
    checkAndRequestPermissions();
  }, [checkAndRequestPermissions]);

  useAppState({ onForeground });
};
