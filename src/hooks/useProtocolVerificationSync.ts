import { useCallback, useEffect, useRef, useState } from 'react';
import { useNetInfo } from '@react-native-community/netinfo';
import { getRealmInstance } from '~/db/realm/utils/realm.config';
import { API_ENDPOINT_KEYS } from '~/api/apiEndpoints';
import { postData } from '~/api/apiService';
import { InteractionManager } from 'react-native';
import { safeWrite } from '~/db/realm/utils/safeRealm';
import { useQuery } from '@realm/react';
import { ProtocolVerificationRecord } from '~/types/protocol.types';
import { ProtocolVerificationSchema } from '~/db/realm/schemas/protocol-verification.schema';

const PROTOCOL_VERIFICATION_BATCH_SIZE = 5;
const MAX_RETRY_COUNT = 4;

export const PROTOCOL_VERIFICATION_SYNC_STATUS = {
  IDLE: 'idle',
  SYNCING: 'syncing',
  ERROR: 'error',
  SUCCESS: 'success',
} as const;

type ProtocolVerificationSyncStatus =
  (typeof PROTOCOL_VERIFICATION_SYNC_STATUS)[keyof typeof PROTOCOL_VERIFICATION_SYNC_STATUS];

export const useProtocolVerificationSync = () => {
  const { isConnected, isInternetReachable } = useNetInfo();

  const statusRef = useRef<ProtocolVerificationSyncStatus>(
    PROTOCOL_VERIFICATION_SYNC_STATUS.IDLE,
  );

  const [
    protocolVerificationSyncRequests,
    setProtocolVerificationSyncRequests,
  ] = useState<ProtocolVerificationRecord[]>([]);

  const pendingProtocolVerifications = useQuery<ProtocolVerificationRecord>({
    type: ProtocolVerificationSchema.name,
    query: records => {
      return records.filtered(
        `retryCount < ${MAX_RETRY_COUNT} && isSynced == false && isSyncReady == true`,
      );
    },
  });

  const shouldCheckForSync =
    pendingProtocolVerifications.length !==
    protocolVerificationSyncRequests.length;
  const isOnline = isConnected && isInternetReachable;

  // Fetch unsynced protocol verifications from Realm
  const fetchProtocolVerificationSyncRequests = useCallback(async () => {
    const realm = await getRealmInstance();

    const results = realm
      .objects<ProtocolVerificationRecord>(ProtocolVerificationSchema.name)
      .filtered(
        `retryCount < ${MAX_RETRY_COUNT} && isSynced == false && isSyncReady == true`,
      );

    setProtocolVerificationSyncRequests(prevRequests => {
      const newRequests = Array.from(results);
      if (
        prevRequests.length === newRequests.length &&
        prevRequests.every((req, idx) => req.Id === newRequests[idx].Id)
      ) {
        return prevRequests;
      }
      return newRequests;
    });
  }, []);

  useEffect(() => {
    if (isOnline && shouldCheckForSync) fetchProtocolVerificationSyncRequests();
  }, [isOnline, shouldCheckForSync, fetchProtocolVerificationSyncRequests]);

  const handleSyncResponse = useCallback(
    async (request: ProtocolVerificationRecord, response: any, error: any) => {
      const realm = await getRealmInstance();

      let isSuccess = false;
      let remoteId = null;
      let errorDetails = null;

      if (!error && response && Array.isArray(response)) {
        const result = response[0];
        if (result) {
          isSuccess = result.success === true;
          if (isSuccess) {
            remoteId = result.id;
          } else {
            errorDetails = result.errors;
          }
        }
      }

      safeWrite(realm, () => {
        const verification =
          realm.objectForPrimaryKey<ProtocolVerificationRecord>(
            ProtocolVerificationSchema.name,
            request.Id,
          );

        if (verification) {
          if (isSuccess) {
            verification.isSynced = true;
            verification.isSyncReady = false;
            verification.updatedAt = new Date();

            if (remoteId) {
              const newVerification = {
                ...verification,
                Id: remoteId,
                isSynced: true,
                isSyncReady: false,
                updatedAt: new Date(),
              };

              realm.create(ProtocolVerificationSchema.name, newVerification);

              realm.delete(verification);
            }
          } else {
            verification.retryCount = verification.retryCount + 1;
            verification.isSyncReady = true;
            verification.updatedAt = new Date();
          }
        }
      });

      if (error || !isSuccess) {
        console.error(
          `Protocol Verification Upload Error: ${request.Id}`,
          JSON.stringify(error ?? errorDetails ?? response),
        );
      } else {
        console.info(
          `Protocol Verification Upload Success: ${request.Id} -> ${remoteId}`,
          JSON.stringify(response),
        );
      }
    },
    [],
  );

  const syncProtocolVerificationBatch = useCallback(
    async (requests: ProtocolVerificationRecord[]) => {
      try {
        const uploadPromises = requests.map(async request => {
          return postData({
            onSync: async (response, error) =>
              handleSyncResponse(request, response, error),
            requestIdentifier: API_ENDPOINT_KEYS.POST_BATCH_SOBJECTS,
            requestData: {
              allOrNone: false,
              records: [
                {
                  attributes: { type: 'Protocol_Verification__c' },
                  Protocol_Step_Id__c: request.Protocol_Step_Id__c,
                  Protocol__c: request.Protocol__c,
                  Field_Value__c: request.Field_Value__c,
                  Daily_Schedule__c: request.Daily_Schedule__c,
                  Stop__c: request.Stop__c,
                  Route_Summary__c: request.Route_Summary__c,
                  Field_Comments__c: request.Field_Comments__c,
                },
              ],
            },
          });
        });
        await Promise.allSettled(uploadPromises);
        return true;
      } catch (error) {
        console.error(
          'useProtocolVerificationSync: syncProtocolVerificationBatch():',
          error,
        );
        return false;
      }
    },
    [handleSyncResponse],
  );

  const performProtocolVerificationSync = useCallback(() => {
    InteractionManager.runAfterInteractions(async () => {
      if (statusRef.current === PROTOCOL_VERIFICATION_SYNC_STATUS.SYNCING)
        return;
      statusRef.current = PROTOCOL_VERIFICATION_SYNC_STATUS.SYNCING;

      try {
        const requests = protocolVerificationSyncRequests;
        for (
          let i = 0;
          i < requests.length;
          i += PROTOCOL_VERIFICATION_BATCH_SIZE
        ) {
          const batch = requests.slice(i, i + PROTOCOL_VERIFICATION_BATCH_SIZE);
          await syncProtocolVerificationBatch(batch);
        }
        statusRef.current = PROTOCOL_VERIFICATION_SYNC_STATUS.SUCCESS;

        fetchProtocolVerificationSyncRequests();
      } catch (error) {
        console.error(
          'useProtocolVerificationSync: performProtocolVerificationSync():',
          error,
        );
        statusRef.current = PROTOCOL_VERIFICATION_SYNC_STATUS.ERROR;
      }
    });
  }, [
    protocolVerificationSyncRequests,
    syncProtocolVerificationBatch,
    fetchProtocolVerificationSyncRequests,
  ]);

  // Automatically sync on changes or online
  useEffect(() => {
    if (!isOnline || protocolVerificationSyncRequests.length === 0) return;
    performProtocolVerificationSync();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOnline, protocolVerificationSyncRequests.length]);

  return {
    status: statusRef.current,
    syncProtocolVerifications: performProtocolVerificationSync,
  };
};
