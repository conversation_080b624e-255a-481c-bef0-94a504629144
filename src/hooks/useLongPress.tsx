import { useRef } from 'react';

export default function useLongPress(onLongPress: () => void, interval = 500) {
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const callbackRef = useRef(onLongPress);

  callbackRef.current = onLongPress;

  const handlePressIn = () => {
    callbackRef.current();
    intervalRef.current = setInterval(() => {
      callbackRef.current();
    }, interval);
  };

  const handlePressOut = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  };

  return { handlePressIn, handlePressOut };
}
