import { useEffect, useCallback, useRef } from 'react';
import { RequestQueueSchema } from '~/db/realm/schemas/request-queue.schema';
import { useQuery } from '@realm/react';
import { IRequestQueue } from '~/types/request-queue.types';
import { syncDown } from '~/services/sync/syncDown';
import { syncUp } from '~/services/sync/syncUp';
import { useImageSync } from '~/hooks/useImageSync';
import { GarbageCollector } from '~/services/GarbageCollectionService.ts';
import { ONE_SECOND_IN_MS } from '~/utils/dateAndTime';
import { EntityName } from '~/db/realm/utils/constants';
import { MAX_RETRY_COUNT } from '~/utils/constants';
import { useIsOnline } from '~/hooks/useIsOnline';
import { useProtocolVerificationSync } from '~/hooks/useProtocolVerificationSync';

export const SYNC_STATUS = {
  IDLE: 'idle',
  SYNCING_UP: 'syncing_up',
  SYNCING_DOWN: 'syncing_down',
  ERROR: 'error',
  SUCCESS: 'success',
};

export type SyncStatus = (typeof SYNC_STATUS)[keyof typeof SYNC_STATUS];

const INTERVAL_BETWEEN_SYNC_UP_AND_DOWN = ONE_SECOND_IN_MS;
const INTERVAL_BETWEEN_SYNC_DOWN_AND_CLEANUP = ONE_SECOND_IN_MS;

export const useSync = () => {
  useImageSync();
  useProtocolVerificationSync();

  // TODO: Handle the failed requests in a better way
  const requests = useQuery<IRequestQueue>({
    type: RequestQueueSchema.name,
    query: collection => {
      return collection.filtered(`retryCount < ${MAX_RETRY_COUNT}`);
    },
  });

  const isSyncDownPendingRef = useRef<boolean>(false);
  const statusRef = useRef<SyncStatus>(SYNC_STATUS.IDLE);

  const { isOnline, isNetInfoReady } = useIsOnline();
  const syncStatus = statusRef.current;

  const performSyncDown = useCallback(async () => {
    if (statusRef.current === SYNC_STATUS.SYNCING_DOWN) return;

    if (statusRef.current === SYNC_STATUS.SYNCING_UP) {
      isSyncDownPendingRef.current = true;
      return;
    }

    if (requests.length > 0) {
      console.warn('Skipping sync down - pending requests exist');
      return;
    }

    statusRef.current = SYNC_STATUS.SYNCING_DOWN;

    try {
      const success = await syncDown();
      statusRef.current = success ? SYNC_STATUS.SUCCESS : SYNC_STATUS.ERROR;

      setTimeout(() => {
        GarbageCollector.runFullCleanup(status =>
          console.info('🚮 Garbage Collection Status:', status),
        );
      }, INTERVAL_BETWEEN_SYNC_DOWN_AND_CLEANUP);
    } catch (error) {
      console.error('useSync.ts: performSyncDown():', error);
      statusRef.current = SYNC_STATUS.ERROR;
    }
  }, [requests]);

  const performSyncUp = useCallback(
    async (syncDownAfterSuccess: boolean = false) => {
      if (statusRef.current === SYNC_STATUS.SYNCING_UP) return;

      statusRef.current = SYNC_STATUS.SYNCING_UP;
      try {
        const pendingRequests = Array.from(requests);
        const syncResults = await syncUp(pendingRequests);
        statusRef.current =
          syncResults.length > 0 ? SYNC_STATUS.SUCCESS : SYNC_STATUS.ERROR;

        const stopIds = syncResults
          .filter(({ entityName }) => entityName === EntityName.STOP)
          .map(({ entityId }) => entityId);

        if (
          stopIds.length > 0 &&
          (isSyncDownPendingRef.current || syncDownAfterSuccess)
        ) {
          isSyncDownPendingRef.current = false;

          setTimeout(() => {
            performSyncDown();
          }, INTERVAL_BETWEEN_SYNC_UP_AND_DOWN);
        }
      } catch (error) {
        console.error('useSync.ts: performSyncUp():', error);
        statusRef.current = SYNC_STATUS.ERROR;
        isSyncDownPendingRef.current = false;
      }
    },
    [requests, performSyncDown],
  );

  useEffect(() => {
    if (!isOnline || !isNetInfoReady) return;
    if (requests.length > 0) {
      performSyncUp(true);
    } else {
      performSyncDown();
    }
  }, [
    isOnline,
    isNetInfoReady,
    requests.length,
    performSyncUp,
    performSyncDown,
  ]);

  useEffect(() => {
    console.info('🔄 Sync Status:', syncStatus);
  }, [syncStatus]);

  return {
    status: syncStatus,
    syncUp: performSyncUp,
    syncDown: performSyncDown,
  };
};
