import { useEffect, useState } from 'react';
import { isDeviceOnline } from '~/utils/network';
import {
  getLocations,
  deleteLocations,
} from '~/db/realm/operations/location.operations';
import { Location } from '~/types/location.types';
import { postData } from '~/api/apiService';
import { API_ENDPOINT_KEYS } from '~/api/apiEndpoints';

const handleAPIResponse = (apiResponse: any, apiError?: string) => {
  if (apiError) {
    console.error(`Error: ${apiError}`);
  } else if (apiResponse) {
    console.info(`Location update response: ${JSON.stringify(apiResponse)}`);
  }
};

const postLocationsToServer = async (locations: Location[]) => {
  const reqData = {
    allOrNone: false,
    records: locations.map(location => ({
      attributes: { type: 'Location_History__c' },
      Location__Latitude__s: location.Location__Latitude__s,
      Location__Longitude__s: location.Location__Longitude__s,
      Route_Summary__c: location.Route_Summary__c,
      Stop__c: location.Stop__c,
      Timestamp__c: location.Timestamp__c,
      Activity_Type__c: location.Activity_Type__c,
      Speed__c:
        location.Speed__c !== undefined && location.Speed__c >= 0
          ? location.Speed__c
          : 0,
      Heading__c:
        location.Heading__c !== undefined && location.Heading__c >= 0
          ? location.Heading__c
          : undefined,
    })),
  };

  try {
    await postData({
      onSync: handleAPIResponse,
      requestIdentifier: API_ENDPOINT_KEYS.POST_BATCH_SOBJECTS,
      requestData: reqData,
    });
    return locations.map(location => location.Id);
  } catch (error) {
    console.error('Error posting locations batch:', error);
  }
  return [];
};

const useSyncLocation = () => {
  const [isSyncing, setIsSyncing] = useState(false);

  useEffect(() => {
    let isUnmounted = false;

    const syncLocations = async () => {
      setIsSyncing(true);

      try {
        let hasMoreLocations = true;

        while (hasMoreLocations && !isUnmounted) {
          const locations = await getLocations(50);

          console.info(
            'LOCATION SYNC-- syncLocations locations: ',
            locations?.length,
          );

          if (locations.length === 0) {
            console.info('LOCATION SYNC-- syncLocations No location to sync');
            hasMoreLocations = false;
            break;
          }

          const successfulIds = await postLocationsToServer(locations);

          if (successfulIds && successfulIds.length > 0) {
            await deleteLocations(successfulIds);
          }
        }
      } catch (error) {
        console.error('Error syncing locations:', error);
      } finally {
        if (!isUnmounted) {
          setIsSyncing(false);
        }
      }
    };

    const checkAndSync = async () => {
      const online = await isDeviceOnline();
      if (online) {
        syncLocations().catch(error => {
          console.error('Error syncing locations:', error);
        });
      } else {
        console.info('LOCATION SYNC-- No network connection');
      }
    };

    checkAndSync();

    return () => {
      isUnmounted = true;
    };
  }, []);

  return isSyncing;
};

export { useSyncLocation };
