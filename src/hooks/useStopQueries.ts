import { useQuery } from '@realm/react';
import { StopSchema } from '~/db/realm/schemas/stop.schema';
import { StopWithRelations } from '~/types/stops.types';

export const useStopQueries = (date: string) => {
  const activeStopsForSelectedDate = useQuery<StopWithRelations>({
    type: StopSchema.name,
    query: stopRecords =>
      stopRecords
        .filtered(
          'Post_Date__c = $0 AND Completed_Time__c == null AND Status__c != "COMPLETED" AND Status__c != "CANCELLED" AND Status__c != "INACTIVE"',
          date,
        )
        .sorted([['Stop_Time_Preferred__c', false]]),
  });

  const completeStopsForSelectedDate = useQuery<StopWithRelations>({
    type: StopSchema.name,
    query: stopRecords =>
      stopRecords
        .filtered('Post_Date__c = $0 AND Completed_Time__c != null', date)
        .sorted([['Stop_Time_Preferred__c', false]]),
  });

  return { activeStopsForSelectedDate, completeStopsForSelectedDate };
};
