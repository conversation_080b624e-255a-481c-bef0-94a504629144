import { useQuery } from '@realm/react';
import { Parcel } from '~/types/parcel.types';
import { Service } from '~/types/service.types';
import { ParcelSchema } from '~/db/realm/schemas/parcel.schema';
import { ServiceSchema } from '~/db/realm/schemas/service.schema';
import { StopWithRelations } from '~/types/stops.types';

export const useStopServices = (stop: StopWithRelations) => {
  const pickupParcels = useQuery<Parcel>({
    type: ParcelSchema.name,
    query: parcels => parcels.filtered('Pickup__c == $0', stop.Id),
  });

  const deliveryParcels = useQuery<Parcel>({
    type: ParcelSchema.name,
    query: parcels => parcels.filtered('Delivery__c == $0', stop.Id),
  });

  const stopServices = useQuery<Service>({
    type: ServiceSchema.name,
    query: services => services.filtered('Stop__c == $0', stop.Id),
  });

  return {
    pickupParcels,
    deliveryParcels,
    stopServices,
  };
};
