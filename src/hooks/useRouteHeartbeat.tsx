import { useEffect, useCallback } from 'react';
import { RouteSummary } from '~/types/routes.types';
import { getStopsByRouteId } from '~/db/realm/operations/stop.operations';
import { Stop } from '~/types/stops.types';
import { saveDataToRealm } from '~/db/realm';
import {
  setRouteAndStopId,
  startHeartbeat,
  stopHeartbeat,
} from '~/services/location/LocationService';
import { STOP_STATUS } from '~/utils/constants';

type UseRouteHeartbeatProps = {
  route?: RouteSummary;
};

export const useRouteHeartbeat = ({ route }: UseRouteHeartbeatProps) => {
  /**
   * Calculates the time difference in minutes between the current time
   * and a given planned start time.
   *
   * @param {Date} plannedStart - The planned start time to compare with the current time.
   * @returns {number} The time difference in minutes.
   */
  const calculateTimeDifferenceInMinutes = (plannedStart: Date): number => {
    const now = new Date();
    return (plannedStart.getTime() - now.getTime()) / (1000 * 60);
  };

  /**
   * Fetches the next stop ID for a given route ID.
   */
  const getNextStopId = useCallback(async (nextRouteId: string) => {
    try {
      const stopList: Stop[] = await getStopsByRouteId(nextRouteId);

      if (!stopList || stopList.length === 0) {
        console.info('No stops found for the given routeId:', nextRouteId);
        return '';
      }

      // Filter out completed stops
      const incompleteStops = stopList.filter(
        stop => stop.Status__c !== STOP_STATUS.COMPLETE,
      );

      if (incompleteStops.length === 0) {
        console.info('All stops are completed for routeId:', nextRouteId);
        return '';
      }

      // Perform sorting in a separate statement
      const sortedStops = [...incompleteStops].sort((a, b) => {
        const timeA = a.Stop_Time_Preferred__c || ''; // Handle null values safely
        const timeB = b.Stop_Time_Preferred__c || '';

        return timeA.localeCompare(timeB);
      });

      // Return the ID of the first stop from the sorted list
      const nextStopId = sortedStops[0]?.Id || '';
      console.info('Next stop ID:', nextStopId);
      return nextStopId;
    } catch (error) {
      console.error('Error fetching next stop ID:', error);
      return '';
    }
  }, []);

  /**
   * Handles routes with status READY or PROGRESS.
   */
  const handleHeartbeatForReadyOrProgressRoute = useCallback(
    async (routeId: string, status: string) => {
      const latestStopId = await getNextStopId(routeId);
      setRouteAndStopId(routeId, latestStopId);

      console.info(
        `[FINAL] Going to start location tracking for Route id: ${routeId}, Stop id: ${latestStopId} for ${status} route`,
      );
      startHeartbeat();

      await saveDataToRealm<string>('routeId', routeId);
      await saveDataToRealm<string>('stopId', latestStopId);
    },
    [getNextStopId],
  );

  /**
   * Handles scheduled routes (time-based logic).
   */
  const handlePlannedRoute = useCallback(async () => {
    const plannedStart = new Date(route.Planned_Start__c!);
    const timeDifference = calculateTimeDifferenceInMinutes(plannedStart);
    console.info('Minutes left for next scheduled route: ', timeDifference);

    //If route is not In Progress or Ready, we check and start tracking if route will be ready within 15 mins
    if (timeDifference <= 15) {
      const latestStopId = await getNextStopId(route?.Id);
      console.info(
        `[FINAL] Going to start location tracking for Route id: ${route?.Id} and Stop id: ${latestStopId} for ${route?.Status__c} route`,
      );
      await saveDataToRealm<string | null>('routeId', route?.Id);
      await saveDataToRealm<string | null>('stopId', latestStopId);
      startHeartbeat();
      setRouteAndStopId(route?.Id, latestStopId);
      return;
    }
    console.info(
      '[FINAL] | Location Tracking criteria for none of the route met, Cleaning up',
    );
    await saveDataToRealm<string | null>('routeId', null);
    await saveDataToRealm<string | null>('stopId', null);
    stopHeartbeat();
    setRouteAndStopId('', '');
  }, [getNextStopId, route?.Id, route?.Planned_Start__c, route?.Status__c]);

  /**
   * Handles route selection and heartbeat logic.
   */
  const handleRouteSelection = useCallback(async () => {
    if (!route) {
      console.info(
        '[useRouteHeartbeat] No eligible route for location tracking. Stopping heartbeat.',
      );
      stopHeartbeat();
      await saveDataToRealm<string | null>('routeId', null);
      await saveDataToRealm<string | null>('stopId', null);
      setRouteAndStopId('', '');
      return;
    }
    console.info('First route current status: ', route?.Status__c);
    if (
      (route?.Status__c?.toLowerCase().includes('ready') ||
        route?.Status__c?.toLowerCase().includes('progress')) &&
      route?.Id
    ) {
      await handleHeartbeatForReadyOrProgressRoute(route.Id, route.Status__c);
      return;
    }

    await handlePlannedRoute();
  }, [handleHeartbeatForReadyOrProgressRoute, handlePlannedRoute, route]);

  useEffect(() => {
    console.info('Route for location tracking: ', JSON.stringify(route));
    if (!route) return;
    const debounceTimer = setTimeout(() => {
      handleRouteSelection();
    }, 5000); // Debounce duration: 5 seconds

    // Cleanup the timer if the dependency changes before 5 seconds
    return () => {
      clearTimeout(debounceTimer);
    };
  }, [handleRouteSelection, route]);

  return { handleRouteSelection };
};
