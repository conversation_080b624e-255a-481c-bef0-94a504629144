import { useCallback, useEffect, useRef } from 'react';
import { AppState } from 'react-native';
import type { AppStateStatus } from 'react-native';

interface UseAppStateProps {
  onForeground?: () => void | Promise<void>;
  onBackground?: () => void | Promise<void>;
}

export function useAppState({ onForeground, onBackground }: UseAppStateProps) {
  const appState = useRef<AppStateStatus>(AppState.currentState);

  const handleAppStateChange = useCallback(
    (nextAppState: AppStateStatus) => {
      if (
        appState.current.match(/inactive|background/) &&
        nextAppState === 'active'
      ) {
        onForeground?.();
      } else if (nextAppState !== 'active' && appState.current === 'active') {
        onBackground?.();
      }
      appState.current = nextAppState;
    },
    [onBackground, onForeground],
  );

  useEffect(() => {
    const subscription = AppState.addEventListener(
      'change',
      handleAppStateChange,
    );
    return () => {
      subscription.remove();
    };
  }, [handleAppStateChange]);

  return { appState };
}
