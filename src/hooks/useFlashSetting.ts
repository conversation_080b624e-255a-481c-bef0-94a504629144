import { useCallback, useState, useEffect } from 'react';
import {
  saveDataToRealm,
  getDataFromRealm,
} from '~/db/realm/operations/data.operations';

type FlashSettingOption = 'on' | 'off' | 'auto';

const useFlashSetting = () => {
  const [flashSetting, setFlashSetting] = useState<FlashSettingOption>('auto');

  useEffect(() => {
    const fetchSetting = async () => {
      const stored =
        await getDataFromRealm<FlashSettingOption>('FLASH_SETTING');
      if (stored) {
        setFlashSetting(stored);
      }
    };
    fetchSetting();
  }, []);

  const toggleFlashSetting = useCallback(async () => {
    let nextFlashSetting: FlashSettingOption;

    if (flashSetting === 'on') {
      nextFlashSetting = 'auto';
    } else if (flashSetting === 'auto') {
      nextFlashSetting = 'off';
    } else {
      nextFlashSetting = 'on';
    }

    setFlashSetting(nextFlashSetting);

    await saveDataToRealm<FlashSettingOption>(
      'FLASH_SETTING',
      nextFlashSetting,
    );
  }, [flashSetting]);

  return { flashSetting, toggleFlashSetting };
};

export default useFlashSetting;
