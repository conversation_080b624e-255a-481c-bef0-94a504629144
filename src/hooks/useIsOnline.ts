import { useEffect, useState } from 'react';
import NetInfo from '@react-native-community/netinfo';

export const useIsOnline = () => {
  const [isOnline, setIsOnline] = useState(true);
  const [isNetInfoReady, setNetInfoReady] = useState(false);

  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener(state => {
      // Ignore until state.isInternetReachable is known
      if (state.isInternetReachable === null) return;

      const online = !!(state.isConnected && state.isInternetReachable);
      setIsOnline(online);
      setNetInfoReady(true);
    });

    return () => unsubscribe();
  }, []);

  return { isOnline, isNetInfoReady };
};
