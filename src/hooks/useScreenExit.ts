import { useFocusEffect } from '@react-navigation/native';
import { useCallback, useEffect, useRef } from 'react';
import { AppState, AppStateStatus } from 'react-native';

export function useScreenExit(callback: () => void) {
  const appState = useRef<AppStateStatus>(AppState.currentState);

  // Runs when screen is blurred (navigated away)
  useFocusEffect(
    useCallback(() => {
      return () => {
        callback();
      };
    }, [callback]),
  );

  // Runs when the app goes to the background or is killed
  useEffect(() => {
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (appState.current === 'active' && nextAppState !== 'active') {
        callback();
      }
      appState.current = nextAppState;
    };

    const subscription = AppState.addEventListener(
      'change',
      handleAppStateChange,
    );
    return () => {
      subscription.remove();
    };
  }, [callback]);
}
