import React from 'react';
import {
  TouchableWithoutFeedback,
  View,
  Text,
  StyleSheet,
  TextStyle,
} from 'react-native';
import Clipboard from '@react-native-clipboard/clipboard';
import colors from '~/styles/colors';
import { h4, h5, small } from '~/styles/text';
import { marginTop8 } from '~/styles/views';
import { iconView } from '~/styles/icons';
import { Copy } from '~/components/icons';
import { useToast } from '~/components/toast/ToastProvider';
import en from '~/localization/en';

interface InfoItemProps {
  title: string;
  infoText: string;
  enableCopy?: boolean;
  icon: React.ReactElement;
}

const InfoItem: React.FC<InfoItemProps> = ({
  title,
  infoText,
  enableCopy = false,
  icon,
}) => {
  const isPressable = enableCopy;
  const toast = useToast();

  const handlePress = () => {
    Clipboard.setString(infoText);
    toast.showToast({
      type: 'success',
      message: en.address_copied,
      duration: 3000,
    });
  };

  return (
    <View>
      <TouchableWithoutFeedback
        onPress={isPressable ? handlePress : undefined}
        disabled={!isPressable}
        testID={`InfoItem.Root`}>
        <View>
          <View style={styles.titleView}>
            {React.cloneElement(icon, {
              color: colors.darkBlue600,
              testID: 'InfoItem.Title.icon',
              size: 24,
            })}

            <Text style={h5 as TextStyle} testID="InfoItem.Title.Label">
              {title}
            </Text>
          </View>

          <View style={styles.infoTextView}>
            <Text style={styles.infoText} testID="InfoItem.Info.text">
              {infoText}
            </Text>
            {isPressable && <Copy />}
          </View>
        </View>
      </TouchableWithoutFeedback>
    </View>
  );
};

const styles = StyleSheet.create({
  titleView: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  infoTextView: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginLeft: iconView.width + 2,
  },
  infoText: {
    ...h4,
    ...marginTop8,
    marginLeft: 8,
    color: colors.grey800,
    fontSize: small.fontSize,
    flex: 1,
  } as TextStyle,
});

export default InfoItem;
