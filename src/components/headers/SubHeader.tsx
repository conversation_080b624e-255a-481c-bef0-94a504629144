import React, { useState } from 'react';
import {
  Text,
  StyleSheet,
  View,
  TouchableOpacity,
  StatusBar,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import colors from '~/styles/colors';
import { fontFamilyBasedOnRunningOS, stepperText } from '~/styles/text';
import ContactDispatch from '~/components/ContactDispatch';
import { useNavigation } from '@react-navigation/native';
import { STOP_TYPE } from '~/utils/constants';
import en from '~/localization/en';
import { Info, BackArrow } from '~/components/icons';
import { stepperView } from '~/styles/views';
import BannerMessage from '~/components/headers/BannerMessage';
import { ToastType } from '~/types/toast.types';
import { useIsOnline } from '~/hooks/useIsOnline';

type SubHeaderProps = {
  title: string;
  showBackArrow?: boolean;
  showContactDispatch?: boolean;
  showProgressText?: boolean;
  backgroundColor?: string;
  progressHeaderText?: string;
};

const SubHeader = ({
  title,
  showProgressText = false,
  progressHeaderText,
  showBackArrow = true,
  showContactDispatch = true,
  backgroundColor = colors.white,
}: SubHeaderProps) => {
  const navigation = useNavigation();

  const [showContactDispatchModal, setShowContactDispatchModal] =
    useState(false);
  const { isOnline } = useIsOnline();

  const titleToDisplay = title === STOP_TYPE.Delivery ? en.dropoff : title;

  return (
    <View style={{ backgroundColor }}>
      <StatusBar backgroundColor={colors.white} barStyle={'dark-content'} />
      <SafeAreaView edges={['top']} style={styles.container}>
        <ContactDispatch
          isVisible={showContactDispatchModal}
          onClose={() => setShowContactDispatchModal(false)}
        />

        <View style={styles.leftView}>
          {showBackArrow && (
            <TouchableOpacity
              hitSlop={20}
              style={styles.iconColumn}
              onPress={navigation.goBack}>
              <BackArrow color={colors.red600} />
            </TouchableOpacity>
          )}
        </View>

        <Text numberOfLines={1} ellipsizeMode="tail" style={styles.title}>
          {titleToDisplay}
        </Text>

        <View style={styles.rightView}>
          {showProgressText && (
            <View style={stepperView as ViewStyle}>
              <Text style={stepperText as TextStyle}>{progressHeaderText}</Text>
            </View>
          )}

          {showContactDispatch && (
            <TouchableOpacity
              style={[styles.iconColumn]}
              onPress={() =>
                setShowContactDispatchModal(!showContactDispatchModal)
              }>
              <Info />
            </TouchableOpacity>
          )}
        </View>
      </SafeAreaView>
      {!isOnline && (
        <BannerMessage type={ToastType.WARNING} message="Offline" />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    gap: 8,
    paddingTop: 8,
    paddingHorizontal: 16,
    paddingBottom: 16,
    alignItems: 'center',
    backgroundColor: colors.white,
    borderBottomRightRadius: 24,
    borderBottomLeftRadius: 24,
    shadowColor: colors.grayishBlue,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 16,
  },
  iconColumn: {
    height: 24,
    width: 24,
  },
  leftView: {
    flex: 3,
    justifyContent: 'flex-start',
  },
  rightView: {
    flex: 3,
    gap: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
  title: {
    flex: 10,
    fontSize: 20,
    letterSpacing: -0.2,
    fontWeight: '500',
    fontFamily: fontFamilyBasedOnRunningOS,
    color: colors.grey900,
    textAlign: 'center',
  },
});

export default SubHeader;
