import React from 'react';
import {
  StyleSheet,
  Text,
  StatusBar,
  View,
  TextStyle,
  TouchableOpacity,
} from 'react-native';
import { RMBlueHeaderIcon } from '~/assets/icons';
import en from '~/localization/en';
import colors from '~/styles/colors';
import { routesHeaderTitle } from '~/styles/text';

import { SafeAreaView } from 'react-native-safe-area-context';
import { BackArrow } from '~/components/icons';
import { useNavigation } from '@react-navigation/native';
import { row } from '~/styles/views';

// TODO: Update to correct theme header
const ThemedHeader = ({
  title,
  showBackArrow = false,
}: {
  title: string;
  showBackArrow?: boolean;
}) => {
  const navigation = useNavigation();

  const titleStyle = {
    ...routesHeaderTitle,
    position: 'absolute',
  } as TextStyle;

  return (
    <SafeAreaView edges={['top']} style={styles.container}>
      <StatusBar
        backgroundColor={colors.backgroundLight}
        barStyle={'dark-content'}
      />
      <View style={styles.body}>
        <View style={styles.leftView}>
          {showBackArrow && (
            <TouchableOpacity
              hitSlop={20}
              style={row}
              onPress={navigation.goBack}>
              <BackArrow color={colors.darkBlue500} />
            </TouchableOpacity>
          )}
        </View>

        <RMBlueHeaderIcon style={styles.headerIcon} />
        <Text style={titleStyle}>{title ? title : en.todays_routes}</Text>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.backgroundLight,
    paddingHorizontal: 16,
  },
  body: {
    justifyContent: 'center',
  },
  headerIcon: {
    alignSelf: 'flex-end',
  },
  leftView: {
    flex: 1,
    justifyContent: 'flex-start',
  },
});

export default ThemedHeader;
