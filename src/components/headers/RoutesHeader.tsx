import React from 'react';
import { StyleSheet, Text, SafeAreaView, Platform } from 'react-native';
import { RMBlueHeaderIcon } from '~/assets/icons';
import en from '~/localization/en';
import colors from '~/styles/colors';
import { routesHeaderTitle } from '~/styles/text';

const RoutesHeader = () => {
  return (
    <SafeAreaView style={styles.header}>
      <Text style={routesHeaderTitle}>{en.todays_routes}</Text>
      <RMBlueHeaderIcon style={styles.headerIcon} />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  header: {
    alignItems: 'center',
    height: 104,
    justifyContent: 'center',
    marginTop: Platform.OS === 'ios' ? 36 : 0,
    backgroundColor: colors.backgroundLight,
  },
  headerIcon: {
    width: '100%',
    alignItems: 'center',
    height: 100,
    justifyContent: 'center',
    position: 'absolute',
    right: 0,
  },
});

export default RoutesHeader;
