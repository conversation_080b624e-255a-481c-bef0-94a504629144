import React from 'react';
import {
  Platform,
  StatusBar,
  StyleSheet,
  Text,
  TextStyle,
  View,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import colors from '~/styles/colors';
import { paddingTop16 } from '~/styles/spacing';
import IconButton from '~/components/buttons/IconButton';
import BackArrow from '~/components/icons/BackArrow';
import { headerTitle } from '~/styles/text';

const TransparentHeader = ({
  onBackPress,
  title,
  rightButton,
}: {
  onBackPress: () => void;
  title: string;
  rightButton?: React.ReactNode;
}) => {
  const insets = useSafeAreaInsets();
  const paddingTop = Platform.select({
    ios: insets.top,
    android: insets.top + paddingTop16.paddingTop,
  });

  return (
    <>
      <StatusBar
        translucent
        backgroundColor="transparent"
        barStyle="light-content"
      />
      <LinearGradient
        colors={[
          colors.black,
          colors.transBlack70,
          colors.transBlack50,
          'transparent',
        ]}
        style={[styles.buttonContainer, { paddingTop }]}>
        <View style={styles.leftContainer}>
          <IconButton
            onPress={onBackPress}
            icon={<BackArrow color={colors.white} size={24} />}
          />
        </View>
        {Boolean(title) && (
          <Text numberOfLines={3} style={styles.headerTitle}>
            {title}
          </Text>
        )}
        <View style={styles.rightContainer}>{rightButton}</View>
      </LinearGradient>
    </>
  );
};

const styles = StyleSheet.create({
  buttonContainer: {
    zIndex: 999,
    paddingTop: 16,
    paddingHorizontal: 20,
    position: 'absolute',
    flexDirection: 'row',
    alignItems: 'center',
  },
  leftContainer: {
    flex: 1,
  },
  rightContainer: {
    flex: 1,
    alignItems: 'flex-end',
  },
  headerTitle: {
    ...headerTitle,
    fontSize: 18,
    color: colors.white,
    flex: 6,
  } as TextStyle,
});

export default React.memo(TransparentHeader);
