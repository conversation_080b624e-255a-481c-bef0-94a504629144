import React from 'react';
import { View, Text, StyleSheet, ViewStyle, TextStyle } from 'react-native';
import colors from '~/styles/colors';
import { ToastType } from '~/types/toast.types';
import {
  CheckCircle,
  CloudSlashOutlined,
  WarningFilled,
} from '~/components/icons';
import { iconView } from '~/styles/icons';

type BannerMessageProps = {
  message: string;
  type: ToastType;
  testID?: string;
};

const bannerStyles = {
  backgroundColor: {
    [ToastType.SUCCESS]: colors.success,
    [ToastType.ERROR]: colors.red50,
    [ToastType.WARNING]: colors.warning,
  },
  textColor: {
    [ToastType.SUCCESS]: colors.greenDark,
    [ToastType.ERROR]: colors.red600,
    [ToastType.WARNING]: colors.yellowDark,
  },
};

const getBannerIcon = (
  type: ToastType,
  color: string,
  testID: string,
  size = iconView.width,
): React.ReactNode => {
  const iconProps = {
    width: size,
    height: size,
    style: styles.icon,
    testID: `${testID}.Icon`,
  };

  switch (type) {
    case ToastType.SUCCESS:
      return <CheckCircle {...iconProps} fill={color} />;
    case ToastType.ERROR:
      return <WarningFilled {...iconProps} stroke={color} />;
    case ToastType.WARNING:
      return <CloudSlashOutlined {...iconProps} color={color} />;
    default:
      return null;
  }
};

const BannerMessage: React.FC<BannerMessageProps> = ({
  type,
  message,
  testID = 'BannerMessage',
}) => {
  const background = bannerStyles.backgroundColor[type];
  const color = bannerStyles.textColor[type];

  return (
    <View
      testID={`${testID}.Container`}
      style={[styles.container, { backgroundColor: background }]}>
      {getBannerIcon(type, color, testID)}
      <Text
        testID={`${testID}.Text`}
        style={[styles.text, { color }]}
        numberOfLines={2}
        ellipsizeMode="tail">
        {message}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingVertical: 8,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 4,
  } as ViewStyle,
  icon: {
    marginTop: 1,
  },
  text: {
    fontSize: 14,
    fontWeight: '500',
    flexShrink: 1,
    flexWrap: 'wrap',
  } as TextStyle,
});

export default BannerMessage;
