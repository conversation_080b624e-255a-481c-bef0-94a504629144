import React from 'react';
import { StyleSheet, View, Text, TextStyle } from 'react-native';
import HeaderIcon from '~/assets/header_icons/logotype_small_red.svg';
import en from '~/localization/en';
import colors from '~/styles/colors';
import { h3 } from '~/styles/text';

const RapidHeader = () => {
  return (
    <View style={styles.header}>
      <HeaderIcon />
      <Text style={styles.sectionHeaderText}>{en.todays_routes}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  header: {
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    height: 72,
  },
  sectionHeaderText: {
    ...h3,
    fontSize: 20,
    color: colors.textBlack,
    textAlign: 'center',
    flex: 1,
    marginLeft: -70,
  } as TextStyle,
});

export default RapidHeader;
