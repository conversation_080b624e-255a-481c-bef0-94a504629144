import React from 'react';
import { Text, StyleSheet, TextStyle } from 'react-native';
import colors from '~/styles/colors';
import { stepperChipText } from '~/styles/text';
import { RouteStatus } from '~/types/routes.types';
import { StopType } from '~/types/stops.types';
import {
  ROUTE_STATUS_TITLE,
  STOP_STATUS_TITLE,
  STOP_TYPE_TITLE,
} from '~/utils/status';

interface StepperChipProps {
  stepperChipColor: [string, string];
  status: RouteStatus | StopType;
  chipBorderColor?: string;
}

const StepperChip: React.FC<StepperChipProps> = ({
  stepperChipColor,
  status,
  chipBorderColor = stepperChipColor[0],
}) => {
  const statusMap = {
    ...ROUTE_STATUS_TITLE,
    ...STOP_TYPE_TITLE,
    ...STOP_STATUS_TITLE,
  };

  return (
    <Text
      style={[
        styles.stepperChip,
        {
          color: stepperChipColor[1],
          backgroundColor: stepperChipColor[0],
          borderColor: chipBorderColor,
        },
      ]}>
      {statusMap[status] ?? status.toUpperCase()}
    </Text>
  );
};

const styles = StyleSheet.create({
  stepperChip: {
    ...stepperChipText,
    backgroundColor: colors.greenLight,
    borderRadius: 16,
    borderWidth: 1,
    paddingVertical: 8,
    paddingHorizontal: 16,
    color: colors.greenDark,
    fontSize: 14,
    alignSelf: 'flex-start',
    overflow: 'hidden',
  } as TextStyle,
});

export default StepperChip;
