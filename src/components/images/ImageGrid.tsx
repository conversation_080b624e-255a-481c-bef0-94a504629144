import React from 'react';
import { View, StyleSheet, Image, TouchableOpacity } from 'react-native';
import { ImageAssetMap } from '~/components/protocol/AssetMap';

const NUMBER_OF_IMAGES_PER_ROW = 3;

interface ImageGridProps {
  images: string[];
  onImagePress?: (imagePath: string) => void;
  imagesPerRow?: number;
}

const GAP_PERCENTAGE = 8;

export const resolveImageSource = (path: string) => {
  if (path.startsWith('assets/images/')) {
    return ImageAssetMap({ name: path });
  }
  return { uri: path };
};

const ImageGrid: React.FC<ImageGridProps> = ({
  images,
  onImagePress,
  imagesPerRow = NUMBER_OF_IMAGES_PER_ROW,
}) => {
  const widthPercentage =
    100 / imagesPerRow - (1 - GAP_PERCENTAGE / 100) * (imagesPerRow - 1);

  return (
    <View style={[styles.grid, { gap: `${GAP_PERCENTAGE}%` }]}>
      {images.map((imagePath, index) => (
        <TouchableOpacity
          key={`${index.toString()}_${imagePath}`}
          style={[styles.imageContainer, { width: `${widthPercentage}%` }]}
          onPress={() => onImagePress?.(imagePath)}>
          <Image
            style={styles.image}
            source={resolveImageSource(imagePath)}
            resizeMode="cover"
          />
        </TouchableOpacity>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  grid: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    flexWrap: 'wrap',
  },
  imageContainer: {
    aspectRatio: 1,
    borderRadius: 10,
    overflow: 'hidden',
  },
  image: {
    width: '100%',
    height: '100%',
  },
});

export default ImageGrid;
