import React, { useState } from 'react';
import { View, Text, Pressable } from 'react-native';
import { Icon } from '@rneui/base';

const CheckboxList = ({ items, onAllChecked }) => {
  const [checkedItems, setCheckedItems] = useState(
    items.reduce((acc, item) => ({ ...acc, [item]: false }), {}),
  );

  const handleCheck = item => {
    const updatedCheckedItems = {
      ...checkedItems,
      [item]: !checkedItems[item],
    };
    setCheckedItems(updatedCheckedItems);

    // Check if all items are checked
    const allChecked = Object.values(updatedCheckedItems).every(Boolean);
    onAllChecked(allChecked);
  };

  return (
    <View>
      {items.map(item => (
        <Pressable
          key={item}
          onPress={() => handleCheck(item)}
          testID={`CheckboxList.${item.replace(/\s+/g, '_')}`}>
          <Icon
            name={checkedItems[item] ? 'check-box' : 'check-box-outline-blank'}
            type="material"
            color={checkedItems[item] ? 'blue' : 'gray'}
            size={24}
            testID={`CheckboxList.Icon.${item.replace(/\s+/g, '_')}`}
          />
          <Text>{item}</Text>
        </Pressable>
      ))}
    </View>
  );
};

export default CheckboxList;
