import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ViewStyle,
  StyleSheet,
} from 'react-native';
import colors from '~/styles/colors';
import {
  fontSize16,
  letterSpacing,
  fontFamilyBasedOnRunningOS,
  blackText,
} from '~/styles/text';
import { radioButtonSelected, radioButtonUnselected } from '~/styles/views';
import { BottomSheetItemType } from '~/components/modals/BottomSheetWithRadioButtonsList';

const RadioButtonItem = ({
  item,
  isSelected,
  onItemSelected,
}: {
  item: BottomSheetItemType;
  isSelected: boolean;
  onItemSelected: (id: string) => void;
}) => {
  const radioButtonStyle = isSelected
    ? radioButtonSelected
    : radioButtonUnselected;
  return (
    <TouchableOpacity
      id={`RadioButtonItem.TouchableOpacity.${item.title}`}
      style={styles.listItem}
      onPress={() => {
        onItemSelected(item.id);
      }}>
      <View style={styles.textContainer}>
        <Text
          style={{ ...fontSize16, ...letterSpacing, ...blackText }}
          numberOfLines={1}>
          {item.title}
        </Text>

        {item.subtitle !== '' && (
          <Text style={styles.subtitle} numberOfLines={1}>
            {item.subtitle}
          </Text>
        )}
      </View>

      {item.rightText !== '' && (
        <Text style={styles.subtitle}>{item.rightText}</Text>
      )}

      <View style={radioButtonStyle as ViewStyle} />
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  listItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    gap: 12,
    borderRadius: 8,
    elevation: 1,
    backgroundColor: colors.lightGrayishBlue,
    shadowColor: colors.lightGrayishBlue,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  textContainer: {
    flex: 1,
    gap: 4,
  },
  subtitle: {
    fontSize: 15,
    fontFamily: fontFamilyBasedOnRunningOS,
    letterSpacing: -0.1,
    color: colors.transBlack70,
  },
});

export default RadioButtonItem;
