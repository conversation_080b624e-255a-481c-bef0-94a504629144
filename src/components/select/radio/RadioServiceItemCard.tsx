import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ViewStyle,
  StyleSheet,
  TextStyle,
} from 'react-native';
import colors from '~/styles/colors';
import {
  letterSpacing,
  fontFamilyBasedOnRunningOS,
  blackText,
  heading,
  leftText,
  grayText,
  fontSize18,
  disabledText,
  pendingInfoText,
  h5,
} from '~/styles/text';
import {
  column,
  container,
  radioButtonSelected,
  radioButtonUnselected,
  row,
} from '~/styles/views';
import { StopType, TaskListItem } from '~/types/stops.types';
import { Checkmark, CircleBackground, Box } from '~/components/icons';
import {
  marginBottom10,
  marginRight12,
  marginTop18,
  marginTop16,
  marginTop8,
} from '~/styles/spacing';
import en from '~/localization/en';

const RadioServiceItemCard = ({
  item,
  isSelected,
  onItemSelected,
  isCompleted,
  allowSelectionWhenCompleted = true,
}: {
  item: TaskListItem;
  isSelected: boolean;
  onItemSelected: (item: TaskListItem) => void;
  isCompleted: boolean;
  allowSelectionWhenCompleted?: boolean;
}) => {
  const radioButtonStyle = isSelected
    ? radioButtonSelected
    : radioButtonUnselected;

  return (
    <TouchableOpacity
      id={`RadioButtonItem.TouchableOpacity.${item.type}`}
      onPress={() => {
        onItemSelected(item);
      }}
      disabled={allowSelectionWhenCompleted && isCompleted}>
      <View style={container}>
        <View style={styles.header}>
          <View style={[styles.columnView, marginRight12, marginTop8]}>
            <CircleBackground fillColor={colors.backgroundWhite}>
              {item.icon}
            </CircleBackground>
          </View>

          <View style={styles.contentContainer}>
            <Text style={styles.title} numberOfLines={1}>
              {item.type === StopType.Service &&
              !Array.isArray(item.data) &&
              item.data
                ? (item.data as any).Name
                : item.type === StopType.Pickup
                  ? en.pickup
                  : item.type === StopType.Delivery
                    ? en.delivery
                    : item.type}
            </Text>
          </View>

          {isCompleted ? (
            <View style={[styles.columnView, marginTop16]}>
              <Checkmark color={colors.greenDark} size={38} />
            </View>
          ) : (
            <View
              style={[
                radioButtonStyle as ViewStyle,
                styles.columnView,
                marginTop18,
              ]}
            />
          )}
        </View>

        {Array.isArray(item.data) &&
          item.data.length > 0 &&
          (() => {
            const validParcels = item.data.filter(data => {
              const quantity =
                item.type === StopType.Pickup
                  ? data.Quantity__c
                  : (data.Dropoff_Quantity__c ?? data.Quantity__c);
              return quantity && data.Parcel_Type_Name__c;
            });

            return validParcels.length > 0 ? (
              <>
                <View style={styles.divider} />

                <View style={styles.sectionContainer}>
                  <View style={styles.iconContainer}>
                    <Box size={20} color={colors.darkBlue600} />
                  </View>
                  <View style={styles.sectionContent}>
                    <Text style={h5 as TextStyle}>{en.expected_parcels}</Text>
                    <View style={styles.parcelItems}>
                      {validParcels.map(data => {
                        const quantity =
                          item.type === StopType.Pickup
                            ? data.Quantity__c
                            : (data.Dropoff_Quantity__c ?? data.Quantity__c);

                        return (
                          <Text
                            key={data.Id}
                            style={styles.comments}
                            numberOfLines={1}>
                            {`${quantity} ${data.Parcel_Type_Name__c}`}
                          </Text>
                        );
                      })}
                    </View>
                  </View>
                </View>
              </>
            ) : null;
          })()}
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  header: {
    ...row,
    ...marginBottom10,
    ...container,
    alignItems: 'center',
    gap: 8,
  } as ViewStyle,
  title: {
    ...container,
    ...heading,
    ...blackText,
    ...leftText,
    ...marginTop16,
    fontSize: 20,
  } as TextStyle,
  columnView: {
    ...column,
    justifyContent: 'center',
    alignSelf: 'flex-start',
  } as ViewStyle,
  subtitle: {
    ...letterSpacing,
    ...grayText,
    ...fontSize18,
    ...disabledText,
    fontFamily: fontFamilyBasedOnRunningOS,
  } as TextStyle,
  comments: {
    ...pendingInfoText,
    paddingVertical: 4,
  } as TextStyle,
  divider: {
    height: 1,
    backgroundColor: colors.grey200,
    marginTop: marginTop8.marginTop,
  },
  sectionLabel: {
    ...h5,
  } as TextStyle,
  sectionContent: {
    flex: 1,
    marginLeft: 8,
    justifyContent: 'center',
  } as ViewStyle,
  sectionContainer: {
    flexDirection: 'row',
    marginTop: marginTop16.marginTop,
    alignItems: 'flex-start',
  } as ViewStyle,
  contentContainer: {
    flex: 1,
  } as ViewStyle,
  parcelItems: {
    marginTop: 4,
  } as ViewStyle,
  iconContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 2,
  } as ViewStyle,
});

export default RadioServiceItemCard;
