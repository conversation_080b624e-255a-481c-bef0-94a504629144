import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { grayishText, h5 } from '~/styles/text';
import {
  cardView,
  radioButtonSelected,
  radioButtonUnselected,
} from '~/styles/views';
import { paddingVertical16 } from '~/styles/spacing';
import colors from '~/styles/colors';

type Option<T> = {
  label: string;
  value: T;
};

interface RadioSelectionCardProps<T> {
  label: string;
  value: T | undefined;
  onChange: (value: T) => void;
  options: Option<T>[];
}

const RadioSelectionCard = <T,>({
  label,
  value,
  onChange,
  options,
}: RadioSelectionCardProps<T>) => {
  return (
    <View style={[cardView, styles.container]}>
      <Text style={h5 as TextStyle}>{label}</Text>
      {options.map(option => (
        <TouchableOpacity
          id={`RadioSelectionCard.TouchableOpacity.${option.label}`}
          testID={`radio-option-${option.label}`}
          key={option.label}
          style={styles.optionContainer}
          onPress={() => onChange(option.value)}>
          <Text style={[grayishText, styles.optionLabel]}>{option.label}</Text>
          <View
            style={
              value === option.value
                ? (radioButtonSelected as ViewStyle)
                : (radioButtonUnselected as ViewStyle)
            }
          />
        </TouchableOpacity>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    gap: 16,
  },
  optionContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderRadius: 8,
    ...paddingVertical16,
    paddingLeft: 12,
    backgroundColor: colors.lightGrayishBlue,
  },
  optionLabel: {
    flex: 1,
    marginRight: 12,
  },
});

export default RadioSelectionCard;
