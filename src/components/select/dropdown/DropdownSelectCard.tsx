import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { Chevron } from '~/components/icons';
import colors from '~/styles/colors';
import { blackText, pendingInfoText } from '~/styles/text';
import Checkmark from '~/components/icons/Checkmark';
import CardWrapper from '~/components/cards/CardWrapper';

interface DropdownSelectCardProps {
  label: string;
  selectedValue: string | null | undefined;
  placeholderText: string;
  items: string[];
  onValueSelection: (value: string) => void;
}

const DropdownSelectCard: React.FC<DropdownSelectCardProps> = ({
  label,
  selectedValue,
  placeholderText,
  items,
  onValueSelection,
}) => {
  const [isPickerVisible, setIsPickerVisible] = useState(false);

  const textStyle = selectedValue
    ? styles.optionText
    : [blackText, styles.placeholderText];

  const chevronStyle = {
    transform: [{ rotate: isPickerVisible ? '180deg' : '0deg' }],
  };

  return (
    <CardWrapper title={label}>
      <TouchableOpacity
        style={styles.dropdownContainer}
        onPress={() => {
          setIsPickerVisible(!isPickerVisible);
        }}>
        <View style={chevronStyle}>
          <Chevron />
        </View>
        <Text style={textStyle}>{selectedValue || placeholderText}</Text>
      </TouchableOpacity>

      {isPickerVisible && (
        <View style={styles.listContainer}>
          {items.map(item => (
            <TouchableOpacity
              key={item}
              style={[
                styles.listItemContainer,
                selectedValue === item && styles.selectedListItemContainer,
              ]}
              onPress={() => {
                onValueSelection(item);
                setIsPickerVisible(false);
              }}>
              <Text style={styles.listItemText}>{item}</Text>
              {selectedValue === item && <Checkmark />}
            </TouchableOpacity>
          ))}
        </View>
      )}
    </CardWrapper>
  );
};

const styles = StyleSheet.create({
  dropdownContainer: {
    gap: 4,
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.lightGray,
    borderRadius: 8,
    paddingVertical: 12,
    marginVertical: 12,
    paddingHorizontal: 12,
  },
  listContainer: {
    borderWidth: 1,
    borderColor: colors.lightGray,
    borderRadius: 8,
    overflow: 'hidden',
  },
  listItemText: {
    ...pendingInfoText,
    fontSize: 16,
    fontWeight: '400',
  },
  listItemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  selectedListItemContainer: {
    backgroundColor: colors.blue50,
  },
  icon: {
    marginRight: 8,
  },
  optionText: {
    ...blackText,
    fontSize: 16,
    paddingLeft: 10,
    fontWeight: '300',
  },
  placeholderText: {
    color: colors.transBlack60,
  },
});

export default DropdownSelectCard;
