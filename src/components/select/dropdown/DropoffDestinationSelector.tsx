import React, { useEffect, useState } from 'react';
import { Text, TouchableOpacity, StyleSheet } from 'react-native';
import { Chevron } from '~/components/icons';
import colors from '~/styles/colors';
import { blackText, fontSize16, h4 } from '~/styles/text';
import en from '~/localization/en';
import CardWrapper from '~/components/cards/CardWrapper';

const DropoffDestinationSelector: React.FC<{
  destinations: any[];
  selectedDestination: string | undefined;
  onOpenModal: () => void;
}> = ({ destinations, selectedDestination, onOpenModal }) => {
  const [selectedDestinationName, setSelectedDestinationName] = useState(
    en.select_dropoff_destination,
  );

  useEffect(() => {
    if (selectedDestination) {
      const destinationData = destinations?.find(
        destination => destination.id === selectedDestination,
      );

      if (destinationData) {
        setSelectedDestinationName(destinationData.title);
      } else {
        setSelectedDestinationName(en.select_dropoff_destination);
      }
    }
  }, [destinations, selectedDestination]);

  return (
    <CardWrapper title={en.dropoff_destination}>
      <TouchableOpacity style={styles.dropdownContainer} onPress={onOpenModal}>
        <Chevron />
        <Text
          style={
            selectedDestination
              ? styles.optionText
              : [styles.optionText, styles.placeholderText]
          }>
          {selectedDestinationName}
        </Text>
      </TouchableOpacity>
    </CardWrapper>
  );
};

const styles = StyleSheet.create({
  dropdownContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.lightGray,
    borderRadius: 8,
    paddingVertical: 12,
    marginVertical: 12,
    paddingHorizontal: 16,
  },
  placeholderText: {
    color: colors.transBlack60,
  },
  optionText: {
    ...h4,
    ...blackText,
    ...fontSize16,
    paddingLeft: 10,
  },
});

export default DropoffDestinationSelector;
