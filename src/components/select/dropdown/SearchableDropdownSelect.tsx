import React, { useState } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  TextStyle,
  ViewStyle,
} from 'react-native';
import { Chevron, Cross } from '~/components/icons';
import colors from '~/styles/colors';
import { grayishText, pendingInfoText } from '~/styles/text';
import { deviceHeight, inputBoxContainer } from '~/styles/views';
import Checkmark from '~/components/icons/Checkmark';
import TextInput from '~/components/inputs/TextInput';
import CardWrapper from '~/components/cards/CardWrapper';
import { paddingBottom16 } from '~/styles/spacing';
import en from '~/localization/en';
import { Type } from '~/types/shared.types';

interface SearchableDropdownSelectProps {
  id: string;
  label: string;
  selectedValue: Type | null | undefined;
  placeholderText: string;
  searchPlaceHolderText: string;
  items: Type[];
  onValueSelection: (value: Type) => void;
}

const SearchableDropdownSelect: React.FC<SearchableDropdownSelectProps> = ({
  id,
  label,
  selectedValue,
  placeholderText,
  searchPlaceHolderText,
  items = [],
  onValueSelection,
}) => {
  const [isPickerVisible, setIsPickerVisible] = useState(false);
  const [searchQuery, setSearchQuery] = useState(selectedValue?.Name ?? '');

  const filteredItems = items.filter(item =>
    item?.Name?.toLowerCase().includes(searchQuery.toLowerCase()),
  );

  const handleDropdownPress = () => {
    setIsPickerVisible(true);
    setSearchQuery('');
  };

  return (
    <CardWrapper id={id} title={label}>
      {!isPickerVisible ? (
        <TouchableOpacity
          style={[inputBoxContainer as ViewStyle, styles.dropdownContainer]}
          onPress={handleDropdownPress}
          disabled={items.length === 0}
          testID={`${id}.DropdownButton`}>
          <Text style={selectedValue ? pendingInfoText : grayishText}>
            {selectedValue?.Name ?? placeholderText}
          </Text>
          <Chevron style={{ transform: [{ rotate: '0deg' }] }} />
        </TouchableOpacity>
      ) : (
        <View style={styles.inputContainer} testID={`${id}.SearchContainer`}>
          <TextInput
            id={`${id}.SearchInput`}
            onChangeText={value => setSearchQuery(value)}
            value={searchQuery}
            placeholder={searchPlaceHolderText}
            label={''}
            icon={
              <TouchableOpacity
                onPress={() => setSearchQuery('')}
                testID={`${id}.ClearSearchButton`}>
                <Cross color={colors.darkGray} />
              </TouchableOpacity>
            }
          />
        </View>
      )}

      {isPickerVisible && (
        <View style={styles.listContainer} testID={`${id}.OptionsContainer`}>
          <FlatList
            nestedScrollEnabled
            keyboardShouldPersistTaps="handled"
            data={filteredItems}
            keyExtractor={item => item.Id}
            renderItem={({ item }) => (
              <TouchableOpacity
                style={[
                  styles.listItemContainer,
                  selectedValue?.Id === item.Id &&
                    styles.selectedListItemContainer,
                ]}
                onPress={() => {
                  onValueSelection(item);
                  setIsPickerVisible(false);
                  setSearchQuery(item.Name);
                }}
                accessible={true}
                accessibilityRole="button"
                accessibilityState={{ selected: selectedValue?.Id === item.Id }}
                testID={`${id}.Option.${item.Name}`}>
                <Text style={pendingInfoText as TextStyle}>{item.Name}</Text>
                {selectedValue?.Id === item.Id && <Checkmark />}
              </TouchableOpacity>
            )}
            ListEmptyComponent={
              <View style={styles.noResultContainer}>
                <Text style={grayishText} testID={`${id}.NoResultText`}>
                  {en.no_result_found}
                </Text>
              </View>
            }
          />
        </View>
      )}
    </CardWrapper>
  );
};

const styles = StyleSheet.create({
  dropdownContainer: {
    gap: 4,
    justifyContent: 'space-between',
    marginTop: 8,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  listContainer: {
    borderWidth: 1,
    borderColor: colors.lightGray,
    borderRadius: 8,
    overflow: 'hidden',
    maxHeight: deviceHeight * 0.3,
  },
  listItemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  selectedListItemContainer: {
    backgroundColor: colors.blue50,
  },
  noResultContainer: {
    padding: paddingBottom16.paddingBottom,
  },
});

export default SearchableDropdownSelect;
