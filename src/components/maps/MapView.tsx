import React from 'react';
import { View, StyleSheet } from 'react-native';
import MapboxGL from '@rnmapbox/maps';
import MapPinStop from '~/components/icons/MapPinStop';
import { deviceWidth } from '~/styles/views';

type Props = {
  coordinate: [number, number]; // [longitude, latitude]
};

const MapView = ({ coordinate }: Props) => {
  const mapViewOptions = {
    style: styles.map,
    logoEnabled: false,
    attributionEnabled: false,
    compassEnabled: false,
    scaleBarEnabled: false,
    zoomEnabled: false,
    pitchEnabled: false,
    rotateEnabled: false,
    scrollEnabled: false,
  };

  return (
    <View style={styles.container} testID="MapView.Container">
      <MapboxGL.MapView testID="MapView.Map" {...mapViewOptions}>
        <MapboxGL.Camera
          centerCoordinate={coordinate}
          zoomLevel={15}
          animationMode="none"
          animationDuration={0}
        />
        {/* changing of id is not supported  */}
        <MapboxGL.PointAnnotation id="center" coordinate={coordinate}>
          <View style={styles.marker} testID="MapView.Marker.Pin">
            <MapPinStop />
          </View>
        </MapboxGL.PointAnnotation>
      </MapboxGL.MapView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    height: deviceWidth * 0.6,
    width: deviceWidth * 0.9,
    borderRadius: 16,
    overflow: 'hidden',
    alignSelf: 'center',
  },
  map: {
    flex: 1,
  },
  marker: {
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default MapView;
