import React from 'react';
import { View, ActivityIndicator, ColorValue } from 'react-native';
import colors from '~/styles/colors';
import { appLoader } from '~/styles/views';

interface AppLoaderProps {
  animating?: boolean;
  color?: ColorValue;
  hidesWhenStopped?: boolean;
  size?: number | 'small' | 'large';
  style?: object | null;
}

const AppLoader: React.FC<AppLoaderProps> = ({
  animating = true,
  color = 'primary',
  hidesWhenStopped = true,
  size = '20',
  style,
}) => {
  const getLoaderColor = () => {
    if (color === 'primary') {
      return colors.red600;
    }

    return 'gray';
  };

  return (
    <View style={[appLoader, style]}>
      <ActivityIndicator
        animating={animating}
        color={getLoaderColor()}
        hidesWhenStopped={hidesWhenStopped}
        size={size}
      />
    </View>
  );
};

export default AppLoader;
