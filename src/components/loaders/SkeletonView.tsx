import React, { useEffect } from 'react';
import { Animated, StyleSheet, View, ViewStyle } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import colors from '~/styles/colors';
import {
  column,
  container,
  deviceWidth,
  leftView,
  row,
  screenBase,
} from '~/styles/views';

/**
 * @View
 * SkeletonView
 *
 * @description
 * SkeletonView is a reusable View that displays a skeleton effect, simulating the loading state
 * of UI elements such as text, images, or other content. This is commonly used to improve the user
 * experience by providing a visual indicator of ongoing data fetching or processing.
 *
 * @param {number} [width=200] - The width of the skeleton placeholder.
 * @param {number} [height=15] - The height of the skeleton placeholder.
 * @param {string[]} [skeletonColors=['colors.gray', 'colors.red', 'colors.white']] - Array of colors to create the skeleton gradient.
 * @param {number} [duration=1000] - Duration of the skeleton animation in milliseconds.
 * @param {boolean} [isReversed=false] - Whether the skeleton animation direction is reversed.
 * @param {ViewStyle} [style] - Additional styles for the container view.
 *
 * @example
 * <SkeletonView width={300} height={20} skeletonColors={['#f0f0f0', '#e0e0e0', '#f0f0f0']} />
 *
 * @example
 * <SkeletonView width={250} height={20} isReversed={true} />
 */

interface SkeletonViewProps {
  width?: number; // Width of the skeleton view
  height?: number; // Height of the skeleton view
  skeletonColors?: string[]; // Colors for the skeleton effect
  duration?: number; // Duration of the skeleton animation in milliseconds
  isReversed?: boolean; // Whether the skeleton animation is reversed
  style?: ViewStyle; // Custom container styles
}

const SkeletonView: React.FC<SkeletonViewProps> = ({
  width = 200,
  height = 15,
  skeletonColors = ['#ebebeb', '#c5c5c5', '#ebebeb'],
  duration = 1000,
  isReversed = false,
}) => {
  const skeletonAnimatedValue = React.useRef(new Animated.Value(-1)).current;

  useEffect(() => {
    // Define skeleton animation
    const skeletonAnimation = Animated.loop(
      Animated.timing(skeletonAnimatedValue, {
        toValue: 1,
        duration,
        useNativeDriver: false,
      }),
    );

    skeletonAnimation.start();

    return () => {
      skeletonAnimation.stop();
    };
  }, [duration, skeletonAnimatedValue]);

  // Compute translation range
  const linearTranslate = skeletonAnimatedValue.interpolate({
    inputRange: [-1, 1],
    outputRange: isReversed ? [width, -width] : [-width, width],
  });

  const buildSkeletonViewFor = (style: ViewStyle) => {
    return (
      <View style={[styles.container, { width, height }, style]}>
        <Animated.View
          style={[
            StyleSheet.absoluteFill,
            { transform: [{ translateX: linearTranslate }] },
          ]}>
          <LinearGradient
            colors={skeletonColors}
            style={[StyleSheet.absoluteFill, { width: width * 1.5 }]}
            start={{ x: 0, y: 0.5 }}
            end={{ x: 1, y: 0.5 }}
          />
        </Animated.View>
      </View>
    );
  };

  return (
    <View style={styles.skeletonContainer}>
      {/* Repeat for each list item */}
      {Array(8)
        .fill(0)
        .map((_, index) => (
          <View key={`SkeletonView-item-${index}`} style={column as ViewStyle}>
            <View style={styles.skeletonItem}>
              {/* Skeleton for the text */}

              <View style={styles.skeletonItemTextContainer}>
                {buildSkeletonViewFor(styles.skeletonTitle)}
                {buildSkeletonViewFor(styles.skeletonSubtitle)}
              </View>
              {buildSkeletonViewFor(styles.skeletonButton)}

              {/* Skeleton for the button */}
            </View>
            {buildSkeletonViewFor(styles.skeletonItemDivider)}
          </View>
        ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    overflow: 'hidden',
    backgroundColor: '#ebebeb',
    borderRadius: 4,
  } as ViewStyle,
  skeletonContainer: {
    ...container,
    padding: 16,
    backgroundColor: colors.white,
  },
  skeletonItem: {
    ...(row as ViewStyle),
    height: 140,
    alignItems: 'center',
    borderRadius: 8,
    paddingVertical: 24,
  },
  skeletonItemDivider: {
    height: 1.5,
    ...(screenBase as ViewStyle),
  },
  skeletonItemTextContainer: {
    ...container,
    marginRight: 16,
  },
  skeletonTitle: {
    height: 40,
    width: deviceWidth * 0.35,
    borderRadius: 4,
    marginBottom: 8,
  },
  skeletonSubtitle: {
    height: 20,
    width: deviceWidth * 0.55,
    borderRadius: 4,
  },
  skeletonButton: {
    height: 40,
    width: deviceWidth * 0.3,
    ...(leftView as ViewStyle),
    marginTop: 16,
    borderRadius: 20,
  },
});

export default SkeletonView;
