import React from 'react';
import { View, Text, StyleSheet, TextStyle } from 'react-native';
import { h4 } from '~/styles/text';
import colors from '~/styles/colors';
import { rowViewFillSpace } from '~/styles/views';

interface ProfileRowProps {
  label: string;
  text: string;
}

const InfoRow: React.FC<ProfileRowProps> = ({ label, text }) => {
  return (
    <View style={rowViewFillSpace}>
      <Text style={[styles.label]}>{label}</Text>
      <Text numberOfLines={1} ellipsizeMode="tail" style={[styles.text]}>
        {text}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  label: {
    ...h4,
    fontSize: 16,
    marginRight: 16,
    color: colors.grey900,
  } as TextStyle,
  text: {
    ...h4,
    fontSize: 16,
    textAlign: 'right',
    color: colors.grey900,
    flex: 1,
  } as TextStyle,
});

export default InfoRow;
