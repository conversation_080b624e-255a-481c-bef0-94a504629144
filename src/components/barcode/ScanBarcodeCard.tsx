import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  TextStyle,
} from 'react-native';
import colors from '~/styles/colors';
import { h5, buttonText } from '~/styles/text';
import { cardView, deviceWidth } from '~/styles/views';
import Scan from '~/components/icons/Scan';
import en from '~/localization/en';
import { IMAGE_ASPECT_RATIO } from '~/utils/constants';
import TextInput from '~/components/inputs/TextInput';

interface ScanBarcodeCardProps {
  barcode: string;
  onScan: () => void;
  setBarcode: (barcode: string) => void;
}

const ScanBarcodeCard: React.FC<ScanBarcodeCardProps> = ({
  barcode,
  onScan,
  setBarcode,
}) => (
  <View style={styles.card}>
    <View style={styles.row}>
      <Text style={h5 as TextStyle}>{en.barcode}</Text>

      {Boolean(barcode) && (
        <TouchableOpacity
          id="ScanBarcodeCard.TouchableOpacity.re-scan"
          testID="ScanBarcodeCard.TouchableOpacity.re-scan"
          onPress={onScan}>
          <Text style={styles.redTextButton}>{en.re_scan}</Text>
        </TouchableOpacity>
      )}
    </View>

    {barcode ? (
      <TextInput
        id="ScanBarcodeCard.InputField.barcode"
        value={barcode}
        onChangeText={setBarcode}
        placeholder={en.scan_barcode_title}
      />
    ) : (
      <TouchableOpacity
        id="ScanBarcodeCard.TouchableOpacity.barcode"
        testID="ScanBarcodeCard.TouchableOpacity.barcode"
        style={styles.photoPlaceholder}
        onPress={onScan}>
        <Scan />
      </TouchableOpacity>
    )}
  </View>
);

const styles = StyleSheet.create({
  card: { ...cardView, gap: 16 },
  photoPlaceholder: {
    height: deviceWidth * IMAGE_ASPECT_RATIO,
    backgroundColor: colors.white,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.darkBlue500,
    borderStyle: 'dashed',
  },
  redTextButton: {
    ...(buttonText as TextStyle),
    color: colors.red400,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
});

export default ScanBarcodeCard;
