import React, { useCallback, useEffect, useRef } from 'react';
import { View, StyleSheet, Animated, ViewStyle } from 'react-native';
import { ScanCornerBottomLeft } from '~/components/icons';
import colors from '~/styles/colors';

import {
  absoluteLeft,
  absoluteRight,
  center,
  deviceHeight,
  deviceWidth,
} from '~/styles/views';

const SPACING = 20;
const BOX_HEIGHT = deviceHeight * 0.28;
const SCANNER_LINE_HEIGHT = 5;

const CORNER_ROTATIONS = {
  TOP_LEFT: '90deg',
  TOP_RIGHT: '180deg',
  BOTTOM_LEFT: '0deg',
  BOTTOM_RIGHT: '270deg',
};

const RotatedScanCorner = ({
  rotation,
  testID,
}: {
  rotation: string;
  testID: string;
}) => (
  <View style={{ transform: [{ rotate: rotation }] }} testID={testID}>
    <ScanCornerBottomLeft />
  </View>
);

const BarcodeScannerOverlay = () => {
  const animatedValue = useRef(new Animated.Value(0)).current;

  const animateLine = useCallback(() => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(animatedValue, {
          toValue: 1,
          duration: 2000,
          useNativeDriver: true,
        }),
        Animated.timing(animatedValue, {
          toValue: 0,
          duration: 2000,
          useNativeDriver: true,
        }),
      ]),
    ).start();
  }, [animatedValue]);

  useEffect(() => {
    animateLine();

    return () => {
      animatedValue.stopAnimation();
    };
  }, [animateLine, animatedValue]);

  const translateY = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [SPACING, BOX_HEIGHT - (SPACING + SCANNER_LINE_HEIGHT)],
  });

  return (
    <View style={styles.container}>
      <View style={styles.middleBox} testID="middle-box">
        {/* Animated Scanner Line */}
        <Animated.View
          style={[styles.scannerLine, { transform: [{ translateY }] }]}
          testID="scanner-line"
        />

        {/* Corner Icons */}
        <View style={styles.topLeft}>
          <RotatedScanCorner
            rotation={CORNER_ROTATIONS.TOP_LEFT}
            testID="top-left-corner"
          />
        </View>
        <View style={styles.topRight}>
          <RotatedScanCorner
            rotation={CORNER_ROTATIONS.TOP_RIGHT}
            testID="top-right-corner"
          />
        </View>
        <View style={styles.bottomLeft}>
          <RotatedScanCorner
            rotation={CORNER_ROTATIONS.BOTTOM_LEFT}
            testID="bottom-left-corner"
          />
        </View>
        <View style={styles.bottomRight}>
          <RotatedScanCorner
            rotation={CORNER_ROTATIONS.BOTTOM_RIGHT}
            testID="bottom-right-corner"
          />
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    ...center,
  } as ViewStyle,
  middleBox: {
    width: deviceWidth,
    height: BOX_HEIGHT,
    alignItems: 'center',
  },
  scannerLine: {
    height: SCANNER_LINE_HEIGHT,
    borderRadius: SCANNER_LINE_HEIGHT,
    width: '76%',
    backgroundColor: colors.darkBlue500,
  },
  // Corner Icons
  topLeft: {
    ...absoluteLeft,
    top: 0,
  } as ViewStyle,
  topRight: {
    ...absoluteRight,
    top: 0,
  } as ViewStyle,
  bottomLeft: {
    ...absoluteLeft,
    bottom: 0,
  } as ViewStyle,
  bottomRight: {
    ...absoluteRight,
    bottom: 0,
  } as ViewStyle,
});

export default BarcodeScannerOverlay;
