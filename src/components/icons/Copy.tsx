import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { SvgProps } from '~/types/svg.types';
import colors from '~/styles/colors';

const Copy = ({ color = colors.red600, size = 20, ...props }: SvgProps) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 18 21" fill="none" {...props}>
      <Path
        fill={color}
        d="M17.53 5.47L12.53 0.470001C12.389 0.329001 12.199 0.25 12 0.25H7.3999C5.3979 0.25 4.25 1.39799 4.25 3.39999V4.25H3.3999C1.3979 4.25 0.25 5.39799 0.25 7.39999V17.6C0.25 19.601 1.3979 20.75 3.3999 20.75H10.5991C12.6011 20.75 13.749 19.602 13.749 17.6V16.75H14.5991C16.6011 16.75 17.749 15.602 17.749 13.6V6C17.75 5.801 17.671 5.61 17.53 5.47ZM12.75 2.811L15.189 5.25H14.5C13.24 5.25 12.75 4.759 12.75 3.5V2.811ZM12.25 17.6C12.25 18.787 11.7871 19.25 10.6001 19.25H3.40088C2.21288 19.25 1.75098 18.787 1.75098 17.6V7.39999C1.75098 6.21299 2.21388 5.75 3.40088 5.75H4.25098V13.6C4.25098 15.601 5.39888 16.75 7.40088 16.75H12.251V17.6H12.25ZM14.6001 15.25H7.40088C6.21288 15.25 5.75098 14.787 5.75098 13.6V3.39999C5.75098 2.21299 6.21388 1.75 7.40088 1.75H11.251V3.5C11.251 5.596 12.405 6.75 14.501 6.75H16.251V13.6C16.25 14.787 15.7871 15.25 14.6001 15.25Z"
      />
    </Svg>
  );
};

export default Copy;
