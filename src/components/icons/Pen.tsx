import React from 'react';
import Svg, { Path } from 'react-native-svg';

import colors from '~/styles/colors';
import { SvgProps } from '~/types/svg.types';

const Pen = ({ color = colors.grey600, size = 20, ...props }: SvgProps) => (
  <Svg fill="none" viewBox="0 0 20 20" width={size} height={size} {...props}>
    <Path
      fill={color}
      d="m17.453 4.403-1.856-1.856a2.22 2.22 0 0 0-1.624-.672c-.613 0-1.189.24-1.62.675L2.057 12.893a.62.62 0 0 0-.183.44V17.5c0 .345.28.625.625.625h4.167a.63.63 0 0 0 .44-.182L17.45 7.648a2.28 2.28 0 0 0 .675-1.621c0-.614-.237-1.19-.672-1.624M6.408 16.875H3.125v-3.283l7.494-7.529 3.319 3.318zm10.16-10.113-1.745 1.737-3.322-3.321 1.737-1.746a1.038 1.038 0 0 1 1.476-.002l1.857 1.857a1.04 1.04 0 0 1-.003 1.475"
    />
  </Svg>
);

export default Pen;
