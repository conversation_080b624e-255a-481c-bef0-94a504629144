import React from 'react';
import Svg, { Path } from 'react-native-svg';
import colors from '~/styles/colors';
import { SvgProps } from '~/types/svg.types';

const Briefcase = ({
  color = colors.red600,
  size = 20,
  ...props
}: SvgProps) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill="none" {...props}>
    <Path
      fill={color}
      d="M18 5.25h-1.25V4c0-.965-.785-1.75-1.75-1.75H9c-.965 0-1.75.785-1.75 1.75v1.25H6C3.582 5.25 2.25 6.582 2.25 9v9c0 2.418 1.332 3.75 3.75 3.75h12c2.418 0 3.75-1.332 3.75-3.75V9c0-2.418-1.332-3.75-3.75-3.75ZM8.75 4A.25.25 0 0 1 9 3.75h6a.25.25 0 0 1 .25.25v1.25h-6.5V4Zm-5 5c0-1.577.673-2.25 2.25-2.25h12c1.577 0 2.25.673 2.25 2.25v2.253a.744.744 0 0 1-.446.687c-.865.386-2.339.936-4.446 1.366-.103.021-.214-.058-.274-.146-.725-1.083-1.877-1.729-3.084-1.729s-2.359.646-3.084 1.729c-.059.089-.166.169-.274.146-2.106-.43-3.581-.98-4.446-1.366a.744.744 0 0 1-.446-.687V9ZM18 20.25H6c-1.577 0-2.25-.673-2.25-2.25v-4.62c.95.411 2.473.962 4.591 1.395.69.14 1.408-.165 1.821-.782.446-.666 1.133-1.063 1.838-1.063.705 0 1.392.397 1.837 1.063.346.517.904.816 1.483.816.112 0 .225-.011.337-.034 2.119-.433 3.642-.984 4.592-1.395V18c.001 1.577-.672 2.25-2.249 2.25ZM13.02 15a1 1 0 0 1-1 1 1.005 1.005 0 0 1-1.005-1c0-.552.442-1 .995-1h.01a1 1 0 0 1 1 1Z"
    />
  </Svg>
);

export default Briefcase;
