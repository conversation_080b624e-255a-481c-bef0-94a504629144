import React from 'react';
import Svg, { Path } from 'react-native-svg';

import colors from '~/styles/colors';
import { SvgProps } from '~/types/svg.types';

const Exit = ({ color = colors.red600, size = 20, ...props }: SvgProps) => (
  <Svg fill="none" viewBox="0 0 20 20" width={size} height={size} {...props}>
    <Path
      fill={color}
      d="M13.75 15v1c0 2.418-1.332 3.75-3.75 3.75H4C1.582 19.75.25 18.418.25 16V4C.25 1.582 1.582.25 4 .25h6c2.418 0 3.75 1.332 3.75 3.75v1a.75.75 0 0 1-1.5 0V4c0-1.577-.673-2.25-2.25-2.25H4c-1.577 0-2.25.673-2.25 2.25v12c0 1.577.673 2.25 2.25 2.25h6c1.577 0 2.25-.673 2.25-2.25v-1a.75.75 0 0 1 1.5 0m5.942-4.713a.75.75 0 0 0-.162-.817l-3-3a.75.75 0 1 0-1.061 1.061l1.72 1.72H6a.75.75 0 0 0 0 1.5h11.189l-1.72 1.72a.75.75 0 0 0 1.06 1.061l3-3a.8.8 0 0 0 .163-.245"
    />
  </Svg>
);

export default Exit;
