import React from 'react';
import Svg, { Path } from 'react-native-svg';
import colors from '~/styles/colors';
import { SvgProps } from '~/types/svg.types';

const Notes = ({
  color = colors.darkBlue500,
  size = 20,
  ...props
}: SvgProps) => (
  <Svg width={size} height={size} viewBox="0 0 20 20" fill="none" {...props}>
    <Path
      fill={color}
      d="M14.1666 18.125H5.83331C3.81831 18.125 2.70831 17.015 2.70831 15V5C2.70831 2.985 3.81831 1.875 5.83331 1.875H14.1666C16.1816 1.875 17.2916 2.985 17.2916 5V15C17.2916 17.015 16.1816 18.125 14.1666 18.125ZM5.83331 3.125C4.51915 3.125 3.95831 3.68583 3.95831 5V15C3.95831 16.3142 4.51915 16.875 5.83331 16.875H14.1666C15.4808 16.875 16.0416 16.3142 16.0416 15V5C16.0416 3.68583 15.4808 3.125 14.1666 3.125H5.83331ZM13.9583 10C13.9583 9.655 13.6783 9.375 13.3333 9.375H6.66665C6.32165 9.375 6.04165 9.655 6.04165 10C6.04165 10.345 6.32165 10.625 6.66665 10.625H13.3333C13.6783 10.625 13.9583 10.345 13.9583 10ZM11.4583 13.3333C11.4583 12.9883 11.1783 12.7083 10.8333 12.7083H6.66665C6.32165 12.7083 6.04165 12.9883 6.04165 13.3333C6.04165 13.6783 6.32165 13.9583 6.66665 13.9583H10.8333C11.1783 13.9583 11.4583 13.6783 11.4583 13.3333ZM13.9583 6.66667C13.9583 6.32167 13.6783 6.04167 13.3333 6.04167H6.66665C6.32165 6.04167 6.04165 6.32167 6.04165 6.66667C6.04165 7.01167 6.32165 7.29167 6.66665 7.29167H13.3333C13.6783 7.29167 13.9583 7.01167 13.9583 6.66667Z"
    />
  </Svg>
);

export default Notes;
