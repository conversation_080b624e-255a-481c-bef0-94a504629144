import React from 'react';
import Svg, { Path, Defs, LinearGradient, Stop } from 'react-native-svg';
import colors from '~/styles/colors';

import { SvgProps } from '~/types/svg.types';

const ScanCornerBottomLeft = ({
  size = 44,
  color = colors.white,
  ...props
}: SvgProps) => (
  <Svg fill="none" viewBox="0 0 44 45" width={size} height={size} {...props}>
    <Path
      stroke="url(#barcode-bottom-left-corner_svg__a)"
      strokeLinecap="round"
      strokeWidth={4}
      d="M42 43H14C7.373 43 2 37.627 2 31V2"
    />
    <Defs>
      <LinearGradient
        id="barcode-bottom-left-corner_svg__a"
        x1={10.286}
        x2={43.785}
        y1={31.733}
        y2={28.433}
        gradientUnits="userSpaceOnUse">
        <Stop stopColor={color} />
        <Stop offset={1} stopColor={color} />
      </LinearGradient>
    </Defs>
  </Svg>
);

export default ScanCornerBottomLeft;
