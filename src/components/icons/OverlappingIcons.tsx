import React from 'react';
import { View, StyleSheet, ViewStyle } from 'react-native';
import { center } from '~/styles/views';

interface OverlappingIconsProps {
  mainIcon: React.ReactNode;
  overlayIcon: React.ReactNode;
  containerSize?: number;
  overlayPosition?: { top?: number; right?: number };
}

const OverlappingIcons: React.FC<OverlappingIconsProps> = ({
  mainIcon,
  overlayIcon,
  containerSize = 100,
  overlayPosition = { top: 0, right: 10 },
}) => {
  const updatedStyles = {
    container: {
      ...styles.container,
      width: containerSize,
      height: containerSize,
    },
    overlayContainer: {
      ...styles.overlayContainer,
      top: overlayPosition.top,
      right: overlayPosition.right,
    },
  };

  return (
    <View style={updatedStyles.container}>
      {mainIcon}
      <View style={updatedStyles.overlayContainer}>{overlayIcon}</View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    ...center,
  } as ViewStyle,
  overlayContainer: {
    position: 'absolute',
  },
});

export default OverlappingIcons;
