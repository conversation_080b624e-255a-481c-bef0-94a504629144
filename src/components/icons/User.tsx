import React from 'react';
import Svg, { Path } from 'react-native-svg';

import colors from '~/styles/colors';
import { SvgProps } from '~/types/svg.types';

type UserProps = SvgProps & {
  filled?: boolean;
};

const User = ({
  color = colors.red600,
  size = 25,
  filled = false,
  ...props
}: UserProps) => (
  <Svg fill="none" viewBox="0 0 25 24" width={size} height={size} {...props}>
    <Path
      fill={filled ? color : 'none'}
      stroke={filled ? 'none' : color}
      strokeWidth={filled ? 0 : 1.5}
      d="M8.842 6.5c0-2.206 1.794-4 4-4s4 1.794 4 4-1.794 4-4 4-4-1.794-4-4m5.991 6h-4c-4.06 0-5.5 2.973-5.5 5.519 0 2.277 1.211 3.481 3.503 3.481h7.994c2.292 0 3.503-1.204 3.503-3.481 0-2.546-1.44-5.519-5.5-5.519"
    />
  </Svg>
);

export default User;
