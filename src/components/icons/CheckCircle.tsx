import React from 'react';
import Svg, { Path } from 'react-native-svg';

import colors from '~/styles/colors';
import { SvgProps } from '~/types/svg.types';

const CheckCircle = ({
  color = colors.greenDark,
  size = 33,
  ...props
}: SvgProps) => (
  <Svg fill="none" viewBox="0 0 33 33" width={size} height={size} {...props}>
    <Path
      fill={color}
      d="M16.331.403C7.591.403.498 7.496.498 16.236s7.094 15.833 15.833 15.833 15.834-7.093 15.834-15.833S25.072.403 16.332.403m6.381 12.983-7.394 7.378a1.12 1.12 0 0 1-.839.349c-.3 0-.602-.111-.84-.349l-3.688-3.689c-.46-.459-.46-1.22 0-1.678.459-.46 1.219-.46 1.678 0l2.85 2.85 6.555-6.54a1.166 1.166 0 0 1 1.678 0c.46.46.46 1.204 0 1.679"
    />
  </Svg>
);

export default CheckCircle;
