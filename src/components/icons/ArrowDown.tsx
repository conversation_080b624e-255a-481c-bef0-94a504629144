import React from 'react';
import Svg, { Path } from 'react-native-svg';

import colors from '~/styles/colors';
import { SvgProps } from '~/types/svg.types';

const ArrowDown = ({
  color = colors.darkBlue500,
  width = 24,
  height = 24,
  ...props
}: SvgProps) => (
  <Svg width={width} height={height} viewBox="0 0 24 24" {...props}>
    <Path
      fill={color}
      d={
        'M19.7073 14.707L12.7083 21.7061C12.6153 21.7991 12.5054 21.8721 12.3824 21.9231C12.2604 21.9741 12.1303 22 12.0003 22C11.8703 22 11.7402 21.9731 11.6182 21.9231C11.4952 21.8721 11.3853 21.7991 11.2923 21.7061L4.29325 14.707C3.90225 14.316 3.90225 13.684 4.29325 13.293C4.68425 12.902 5.31631 12.902 5.70731 13.293L11.0003 18.5859V3C11.0003 2.448 11.4473 2 12.0003 2C12.5533 2 13.0003 2.448 13.0003 3V18.5859L18.2933 13.293C18.6842 12.902 19.3163 12.902 19.7073 13.293C20.0983 13.684 20.0983 14.316 19.7073 14.707Z'
      }
    />
  </Svg>
);

export default ArrowDown;
