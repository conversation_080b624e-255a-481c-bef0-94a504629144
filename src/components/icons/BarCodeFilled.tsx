import React from 'react';
import Svg, { Rect, Path, Defs, LinearGradient, Stop } from 'react-native-svg';

import { SvgProps } from '~/types/svg.types';

const BarCodeFilled = ({ size = 56, ...props }: SvgProps) => (
  <Svg width={size} height={size} viewBox="0 0 57 56" fill="none" {...props}>
    <Rect x="1" y="0.5" width="55" height="55" rx="27.5" fill="#F5F7FE" />
    <Rect
      x="1"
      y="0.5"
      width="55"
      height="55"
      rx="27.5"
      stroke="url(#paint0_linear)"
    />
    <Path
      fill="#011973"
      d="M38.25 20.5V24C38.25 24.414 37.914 24.75 37.5 24.75C37.086 24.75 36.75 24.414 36.75 24V20.5C36.75 19.911 36.589 19.75 36 19.75H32.5C32.086 19.75 31.75 19.414 31.75 19C31.75 18.586 32.086 18.25 32.5 18.25H36C37.409 18.25 38.25 19.091 38.25 20.5ZM19.5 24.75C19.914 24.75 20.25 24.414 20.25 24V20.5C20.25 19.911 20.411 19.75 21 19.75H24.5C24.914 19.75 25.25 19.414 25.25 19C25.25 18.586 24.914 18.25 24.5 18.25H21C19.591 18.25 18.75 19.091 18.75 20.5V24C18.75 24.414 19.086 24.75 19.5 24.75ZM24.5 36.25H21C20.411 36.25 20.25 36.089 20.25 35.5V32C20.25 31.586 19.914 31.25 19.5 31.25C19.086 31.25 18.75 31.586 18.75 32V35.5C18.75 36.909 19.591 37.75 21 37.75H24.5C24.914 37.75 25.25 37.414 25.25 37C25.25 36.586 24.914 36.25 24.5 36.25ZM37.5 31.25C37.086 31.25 36.75 31.586 36.75 32V35.5C36.75 36.089 36.589 36.25 36 36.25H32.5C32.086 36.25 31.75 36.586 31.75 37C31.75 37.414 32.086 37.75 32.5 37.75H36C37.409 37.75 38.25 36.909 38.25 35.5V32C38.25 31.586 37.914 31.25 37.5 31.25ZM22.75 24.88V25C22.75 25.414 23.086 25.75 23.5 25.75C23.914 25.75 24.25 25.414 24.25 25V24.88C24.25 24.035 24.535 23.75 25.38 23.75H31.62C32.465 23.75 32.75 24.035 32.75 24.88V25C32.75 25.414 33.086 25.75 33.5 25.75C33.914 25.75 34.25 25.414 34.25 25V24.88C34.25 23.209 33.291 22.25 31.62 22.25H25.38C23.709 22.25 22.75 23.208 22.75 24.88ZM31.62 33.75H25.38C23.709 33.75 22.75 32.791 22.75 31.12V28.75H21.5C21.086 28.75 20.75 28.414 20.75 28C20.75 27.586 21.086 27.25 21.5 27.25H35.5C35.914 27.25 36.25 27.586 36.25 28C36.25 28.414 35.914 28.75 35.5 28.75H34.25V31.12C34.25 32.791 33.291 33.75 31.62 33.75ZM32.75 31.12V28.75H24.25V31.12C24.25 31.965 24.535 32.25 25.38 32.25H31.62C32.465 32.25 32.75 31.965 32.75 31.12Z"
    />
    <Defs>
      <LinearGradient
        id="paint0_linear"
        x1="127.577"
        y1="28"
        x2="-37.7308"
        y2="28"
        gradientUnits="userSpaceOnUse">
        <Stop stopColor="#011973" />
        <Stop offset="0.599149" stopColor="#011973" stopOpacity="0" />
      </LinearGradient>
    </Defs>
  </Svg>
);

export default BarCodeFilled;
