import React from 'react';
import Svg, { Path } from 'react-native-svg';

import colors from '~/styles/colors';
import { SvgProps } from '~/types/svg.types';

const BarcodeOutlined = ({
  color = colors.red600,
  size = 24,
  ...props
}: SvgProps) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill="none" {...props}>
    <Path
      fill={color}
      d="M21.75 4.5V8C21.75 8.414 21.414 8.75 21 8.75C20.586 8.75 20.25 8.414 20.25 8V4.5C20.25 3.911 20.089 3.75 19.5 3.75H16C15.586 3.75 15.25 3.414 15.25 3C15.25 2.586 15.586 2.25 16 2.25H19.5C20.909 2.25 21.75 3.091 21.75 4.5ZM3 8.75C3.414 8.75 3.75 8.414 3.75 8V4.5C3.75 3.911 3.911 3.75 4.5 3.75H8C8.414 3.75 8.75 3.414 8.75 3C8.75 2.586 8.414 2.25 8 2.25H4.5C3.091 2.25 2.25 3.091 2.25 4.5V8C2.25 8.414 2.586 8.75 3 8.75ZM8 20.25H4.5C3.911 20.25 3.75 20.089 3.75 19.5V16C3.75 15.586 3.414 15.25 3 15.25C2.586 15.25 2.25 15.586 2.25 16V19.5C2.25 20.909 3.091 21.75 4.5 21.75H8C8.414 21.75 8.75 21.414 8.75 21C8.75 20.586 8.414 20.25 8 20.25ZM21 15.25C20.586 15.25 20.25 15.586 20.25 16V19.5C20.25 20.089 20.089 20.25 19.5 20.25H16C15.586 20.25 15.25 20.586 15.25 21C15.25 21.414 15.586 21.75 16 21.75H19.5C20.909 21.75 21.75 20.909 21.75 19.5V16C21.75 15.586 21.414 15.25 21 15.25ZM6.25 8.88V9C6.25 9.414 6.586 9.75 7 9.75C7.414 9.75 7.75 9.414 7.75 9V8.88C7.75 8.035 8.035 7.75 8.88 7.75H15.12C15.965 7.75 16.25 8.035 16.25 8.88V9C16.25 9.414 16.586 9.75 17 9.75C17.414 9.75 17.75 9.414 17.75 9V8.88C17.75 7.209 16.791 6.25 15.12 6.25H8.88C7.209 6.25 6.25 7.208 6.25 8.88ZM15.12 17.75H8.88C7.209 17.75 6.25 16.791 6.25 15.12V12.75H5C4.586 12.75 4.25 12.414 4.25 12C4.25 11.586 4.586 11.25 5 11.25H19C19.414 11.25 19.75 11.586 19.75 12C19.75 12.414 19.414 12.75 19 12.75H17.75V15.12C17.75 16.791 16.791 17.75 15.12 17.75ZM16.25 15.12V12.75H7.75V15.12C7.75 15.965 8.035 16.25 8.88 16.25H15.12C15.965 16.25 16.25 15.965 16.25 15.12Z"
    />
  </Svg>
);

export default BarcodeOutlined;
