import React from 'react';
import Svg, { Path } from 'react-native-svg';

import colors from '~/styles/colors';
import { SvgProps } from '~/types/svg.types';

const AutoFlash = ({ color = colors.white, size = 20, ...props }: SvgProps) => (
  <Svg fill="none" viewBox="0 0 20 20" width={size} height={size} {...props}>
    <Path
      fill={color}
      d="M5.75 19.5a.75.75 0 0 1-.75-.75V12a.5.5 0 0 0-.5-.5H.75a.75.75 0 0 1-.558-1.252l9-10a.748.748 0 0 1 1.307.502V7.5a.5.5 0 0 0 .5.5h3.751a.75.75 0 0 1 .558 1.252l-9 10a.75.75 0 0 1-.558.248M3.185 9.166a.5.5 0 0 0 .372.834H5.75a.75.75 0 0 1 .75.75v4.743a.5.5 0 0 0 .872.335l4.943-5.493a.5.5 0 0 0-.372-.835H9.75A.75.75 0 0 1 9 8.75V4.007a.5.5 0 0 0-.872-.334zM13.55 19.287a.655.655 0 1 1-1.235-.436l2.588-7.19a1 1 0 0 1 .94-.661h.116a1 1 0 0 1 .94.661l2.588 7.19a.655.655 0 1 1-1.234.435l-2.315-6.7h-.069zm.082-2.977h4.534v1.108h-4.534z"
    />
  </Svg>
);

export default AutoFlash;
