import React from 'react';
import Svg, { Path } from 'react-native-svg';

import colors from '~/styles/colors';
import { SvgProps } from '~/types/svg.types';

const WarningFilled: React.FC<SvgProps> = ({
  color = colors.yellowDark,
  size = 24,
  ...props
}) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 24 24" {...props}>
      <Path
        d="M21.6069 17.1522L14.9999 4.79599C13.7199 2.40199 10.2809 2.40199 8.99993 4.79599L2.393 17.1522C1.219 19.3482 2.81304 22.0001 5.30804 22.0001H18.6918C21.1858 22.0001 22.7809 19.3472 21.6069 17.1522ZM11.2499 10.0001C11.2499 9.58609 11.5859 9.25009 11.9999 9.25009C12.4139 9.25009 12.7499 9.58609 12.7499 10.0001V14.0001C12.7499 14.4141 12.4139 14.7501 11.9999 14.7501C11.5859 14.7501 11.2499 14.4141 11.2499 14.0001V10.0001ZM12.02 18.0001C11.468 18.0001 11.0148 17.5521 11.0148 17.0001C11.0148 16.4481 11.4579 16.0001 12.0099 16.0001H12.02C12.573 16.0001 13.02 16.4481 13.02 17.0001C13.02 17.5521 12.572 18.0001 12.02 18.0001Z"
        fill={color}
      />
    </Svg>
  );
};

export default WarningFilled;
