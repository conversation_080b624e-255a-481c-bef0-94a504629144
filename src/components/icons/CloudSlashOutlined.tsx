import React from 'react';
import Svg, { Path } from 'react-native-svg';
import colors from '~/styles/colors';
import { SvgProps } from '~/types/svg.types';

const CloudSlashOutlined = ({
  color = colors.yellowDark,
  size = 24,
  ...props
}: SvgProps) => (
  <Svg width={size} height={size} viewBox="0 0 21 21" fill="none" {...props}>
    <Path
      fill={color}
      d="M14.2751 7.60838L18.4417 3.44171C18.6859 3.19754 18.6859 2.80168 18.4417 2.55751C18.1976 2.31335 17.8017 2.31335 17.5575 2.55751L13.5601 6.55501C13.4818 6.55918 13.4025 6.55921 13.325 6.56671C13.0633 6.20338 12.7549 5.86251 12.4049 5.54834C11.3091 4.57584 9.89173 4.04087 8.41589 4.04087C5.08423 4.04087 2.37423 6.75087 2.37423 10.0825C2.37423 12.0984 3.39422 13.9792 5.03088 15.0834L2.55672 17.5575C2.31255 17.8017 2.31255 18.1975 2.55672 18.4417C2.67839 18.5634 2.83841 18.625 2.99841 18.625C3.15841 18.625 3.31844 18.5642 3.4401 18.4417L14.2751 7.60838ZM3.62504 10.0834C3.62504 7.44085 5.77421 5.29169 8.41671 5.29169C9.58671 5.29169 10.7093 5.715 11.5743 6.48167C11.9409 6.81083 12.2509 7.17669 12.4943 7.57003C12.5009 7.58086 12.511 7.58752 12.5185 7.59836L5.94499 14.1717C4.52665 13.3209 3.62504 11.7659 3.62504 10.0834ZM18.625 11.3334C18.625 13.9759 16.4759 16.125 13.8334 16.125H8.83337C8.48837 16.125 8.20837 15.845 8.20837 15.5C8.20837 15.155 8.48837 14.875 8.83337 14.875H13.8334C15.7867 14.875 17.375 13.2867 17.375 11.3334C17.375 10.2267 16.8451 9.16669 15.9576 8.49919C15.6818 8.29169 15.6266 7.90003 15.8341 7.6242C16.0408 7.3492 16.4308 7.29171 16.71 7.50004C17.9091 8.40254 18.625 9.83585 18.625 11.3334Z"
    />
  </Svg>
);

export default CloudSlashOutlined;
