import React from 'react';
import Svg, { Path } from 'react-native-svg';

import colors from '~/styles/colors';
import { SvgProps } from '~/types/svg.types';

const Box = ({ color = colors.white, size = 24, ...props }: SvgProps) => (
  <Svg fill="none" viewBox="0 0 24 24" width={size} height={size} {...props}>
    <Path
      fill={color}
      d="m21.287 6.882-.001-.006q-.005-.007-.009-.013a3.74 3.74 0 0 0-1.752-1.622l-6-2.667a3.76 3.76 0 0 0-3.047 0l-6 2.667a3.73 3.73 0 0 0-1.752 1.622q-.005.006-.009.013l-.001.006a3.73 3.73 0 0 0-.463 1.785v6.667c0 1.48.874 2.825 2.227 3.427l6 2.667c.482.214.999.32 1.516.322l.008.001.008-.001a3.8 3.8 0 0 0 1.516-.322l6-2.667a3.75 3.75 0 0 0 2.227-3.427V8.667a3.8 3.8 0 0 0-.468-1.785m-10.2-2.938a2.25 2.25 0 0 1 1.827-.001l6 2.667c.17.076.319.181.462.292l-2.733 1.215L9.384 4.7zM7.572 5.505l7.259 3.417-1.912.85a2.3 2.3 0 0 1-1.835.002L4.624 6.902c.143-.111.292-.217.462-.292zM5.086 17.389a2.25 2.25 0 0 1-1.336-2.056V8.666c0-.165.036-.323.07-.479l6.66 2.96q.375.161.77.239v8.722c-.054-.019-.11-.03-.164-.053zm15.164-2.056c0 .888-.524 1.695-1.336 2.056l-6 2.667c-.053.024-.11.034-.164.053v-8.723q.398-.079.774-.241l2.355-1.047v1.792a.75.75 0 0 0 1.5 0V9.432l2.8-1.244c.035.157.07.314.07.479v6.666z"
    />
  </Svg>
);

export default Box;
