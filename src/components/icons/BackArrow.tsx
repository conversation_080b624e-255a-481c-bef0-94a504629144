import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { SvgProps } from '~/types/svg.types';

const BackArrow = ({ color, size = 24, ...props }: SvgProps) => (
  <Svg width={size} height={size} fill="none" {...props}>
    <Path
      fill={color}
      d="M20.75 12a.75.75 0 0 1-.75.75H5.81l2.72 2.72a.75.75 0 0 1-1.06 1.06l-4-4a.748.748 0 0 1-.161-.816.75.75 0 0 1 .162-.244l4-4a.75.75 0 1 1 1.06 1.06l-2.72 2.72H20a.749.749 0 0 1 .75.75Z"
    />
  </Svg>
);

export default BackArrow;
