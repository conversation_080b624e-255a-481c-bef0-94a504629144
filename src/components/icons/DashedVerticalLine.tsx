import React from 'react';
import Svg, { Path } from 'react-native-svg';
import colors from '~/styles/colors';
import { SvgProps } from '~/types/svg.types';

type DashedVerticalLineProps = SvgProps & {
  height: number;
  width?: number;
};

const DashedVerticalLine = ({
  height,
  color = colors.transBlack40,
  width = 1,
  ...props
}: DashedVerticalLineProps) => (
  <Svg width={width} height={height} fill="none" {...props}>
    <Path
      stroke={color}
      strokeWidth={width}
      strokeDasharray="1 4"
      strokeLinecap="round"
      d={`M.5.5v${height - 1}`}
    />
  </Svg>
);

export default DashedVerticalLine;
