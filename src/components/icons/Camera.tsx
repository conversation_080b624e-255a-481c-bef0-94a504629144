import React from 'react';
import Svg, { Path } from 'react-native-svg';

import colors from '~/styles/colors';
import { SvgProps } from '~/types/svg.types';

const Camera = ({
  color = colors.darkBlue500,
  size = 32,
  ...props
}: SvgProps) => (
  <Svg fill="none" viewBox="0 0 32 32" width={size} height={size} {...props}>
    <Path
      fill={color}
      d="M24 8.333h-1.947l-.648-1.948a3 3 0 0 0-2.846-2.052H13.44a3 3 0 0 0-2.846 2.052l-.648 1.948H8c-3.224 0-5 1.776-5 5V24c0 3.224 1.776 5 5 5h16c3.224 0 5-1.776 5-5V13.333c0-3.224-1.776-5-5-5M27 24c0 2.103-.897 3-3 3H8c-2.103 0-3-.897-3-3V13.333c0-2.102.897-3 3-3h2.667c.43 0 .812-.276.949-.684l.877-2.633a1 1 0 0 1 .948-.684h5.118c.43 0 .811.275.947.684l.878 2.632c.137.408.519.684.95.684H24c2.103 0 3 .897 3 3zM16 13.667c-2.756 0-5 2.242-5 5s2.244 5 5 5 5-2.243 5-5-2.244-5-5-5m0 8c-1.653 0-3-1.346-3-3 0-1.655 1.347-3 3-3s3 1.345 3 3c0 1.654-1.347 3-3 3M24.667 14a1.334 1.334 0 1 1-2.668-.001 1.334 1.334 0 0 1 2.668 0"
    />
  </Svg>
);

export default Camera;
