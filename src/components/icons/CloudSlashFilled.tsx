import React from 'react';
import Svg, { Rect, Path, Defs, LinearGradient, Stop } from 'react-native-svg';
import { SvgProps } from '~/types/svg.types';
import colors from '~/styles/colors';

const CloudSlashFilled = ({
  size = 56,
  color = colors.darkBlue900,
  ...props
}: SvgProps) => (
  <Svg width={size} height={size} viewBox="0 0 57 56" fill="none" {...props}>
    <Rect x="1" y="0.5" width="55" height="55" rx="27.5" fill="#F5F7FE" />
    <Rect
      x="1"
      y="0.5"
      width="55"
      height="55"
      rx="27.5"
      stroke="url(#paint0_linear)"
    />
    <Path
      d="M33.0301 24.5301L38.0301 19.5301C38.3231 19.2371 38.3231 18.762 38.0301 18.469C37.7371 18.176 37.262 18.176 36.969 18.469L32.1721 23.266C32.0781 23.271 31.9829 23.2711 31.8899 23.2801C31.5759 22.8441 31.2059 22.435 30.7859 22.058C29.4709 20.891 27.77 20.249 25.999 20.249C22.001 20.249 18.749 23.501 18.749 27.499C18.749 29.918 19.973 32.175 21.937 33.5L18.968 36.469C18.675 36.762 18.675 37.2371 18.968 37.5301C19.114 37.6761 19.3061 37.75 19.4981 37.75C19.6901 37.75 19.8821 37.6771 20.0281 37.5301L33.0301 24.5301ZM20.25 27.5C20.25 24.329 22.829 21.75 26 21.75C27.404 21.75 28.7511 22.258 29.7891 23.178C30.2291 23.573 30.6011 24.012 30.8931 24.484C30.9011 24.497 30.9131 24.505 30.9221 24.518L23.034 32.406C21.332 31.385 20.25 29.519 20.25 27.5ZM38.25 29C38.25 32.171 35.671 34.75 32.5 34.75H26.5C26.086 34.75 25.75 34.414 25.75 34C25.75 33.586 26.086 33.25 26.5 33.25H32.5C34.844 33.25 36.75 31.344 36.75 29C36.75 27.672 36.1141 26.4 35.0491 25.599C34.7181 25.35 34.6519 24.88 34.9009 24.549C35.1489 24.219 35.6169 24.15 35.9519 24.4C37.3909 25.483 38.25 27.203 38.25 29Z"
      fill={color}
    />
    <Defs>
      <LinearGradient
        id="paint0_linear"
        x1="127.577"
        y1="28"
        x2="-37.7308"
        y2="28"
        gradientUnits="userSpaceOnUse">
        <Stop stopColor="#011973" />
        <Stop offset="0.599149" stopColor="#011973" stopOpacity="0" />
      </LinearGradient>
    </Defs>
  </Svg>
);

export default CloudSlashFilled;
