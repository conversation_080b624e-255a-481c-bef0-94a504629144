import * as React from 'react';
import { ReactNode } from 'react';
import { StyleSheet, View, ViewStyle } from 'react-native';
import Svg, {
  G,
  Path,
  Defs,
  ClipPath,
  Stop,
  LinearGradient,
} from 'react-native-svg';
import colors from '~/styles/colors';
import { center } from '~/styles/views';
import { SvgProps } from '~/types/svg.types';

type MeshBackgroundProps = SvgProps & {
  children?: ReactNode;
};

const MeshBackground: React.FC<MeshBackgroundProps> = ({
  color = colors.darkBlue500,
  children,
}) => {
  return (
    <View>
      <Svg width={393} height={221} viewBox="0 0 393 221" fill="none">
        <G
          clipPath="url(#paint0_diamond_4769_18830_clip_path)"
          data-figma-skip-parse="true">
          <G
            transform="matrix(0 -.10834 .2784 -.00027 197 183.144)"
            fill="url(#paint0_diamond_4769_18830)"
            shapeRendering="crispEdges">
            <Path d="M0 0H1687.63V752.505H0z" />
            <Path transform="scale(1 -1)" d="M0 0H1687.63V752.505H0z" />
            <Path transform="scale(-1 1)" d="M0 0H1687.63V752.505H0z" />
            <Path transform="scale(-1)" d="M0 0H1687.63V752.505H0z" />
          </G>
        </G>
        <Path
          d="M71.719 66.477a.25.25 0 10-.232.443l.232-.443zM280.48 176.423a.25.25 0 00.338-.106.25.25 0 00-.106-.337l-.232.443zM50.815 77.429a.25.25 0 10-.232.443l.232-.443zm208.768 109.94a.25.25 0 00.232-.442l-.232.442zM29.917 88.382a.25.25 0 10-.232.443l.232-.443zm208.768 109.94a.25.25 0 10.232-.443l-.232.443zM9.02 99.329a.25.25 0 00-.232.443l.232-.443zm208.761 109.94a.25.25 0 00.233-.443l-.233.443zM92.616 55.53a.25.25 0 10-.232.443l.232-.443zm208.768 109.94a.25.25 0 10.232-.443l-.232.443zM113.514 44.577a.25.25 0 00-.233.443l.233-.443zm208.768 109.94a.25.25 0 00.232-.442l-.232.442zM134.417 33.631a.25.25 0 00-.232.442l.232-.443zm208.768 109.94a.25.25 0 00.338-.106.25.25 0 00-.106-.337l-.232.443zM176.219 11.731a.25.25 0 10-.232.443l.232-.443zm208.761 109.94a.25.25 0 10.232-.443l-.232.443zM155.315 22.678a.25.25 0 10-.232.443l.232-.443zm208.768 109.946a.25.25 0 00.232-.443l-.232.443zM197 1l.114-.223a.25.25 0 00-.23.002L197 1zm.1.051l.116-.221-.002-.001-.114.222zM406 110.497l.116.222a.25.25 0 000-.443l-.116.221zM197 220l-.116.221a.245.245 0 00.232 0L197 220zM-12 110.497l-.116-.221a.25.25 0 000 .443l.116-.222zm397.212-10.725a.25.25 0 10-.232-.443l.232.443zM175.987 208.826a.25.25 0 10.232.443l-.232-.443zM280.719 45.02a.25.25 0 00-.233-.443l.233.443zM71.487 154.08a.25.25 0 00.232.443l-.232-.443zm167.43-130.96a.25.25 0 10-.232-.442l.232.443zM29.685 132.182a.25.25 0 10.232.443l-.232-.443zm230.13-98.108a.25.25 0 00-.232-.443l.232.443zM50.583 143.128a.25.25 0 10.232.443l-.232-.443zm251.033-87.16a.25.25 0 10-.232-.443l.232.442zM92.384 165.026a.25.25 0 00.232.443l-.232-.443zm251.033-87.155a.25.25 0 10-.232-.443l.232.443zM134.185 186.927a.25.25 0 00.232.442l-.232-.442zM322.514 66.92a.25.25 0 00-.232-.443l.232.443zM113.281 175.98a.25.25 0 10.233.443l-.233-.443zm251.034-87.16a.25.25 0 00-.232-.444l.232.443zM155.083 197.878a.25.25 0 10.232.443l-.232-.443zm62.937-185.71a.25.25 0 00-.232-.444l.232.443zM8.781 121.227a.25.25 0 00.233.443l-.233-.443zM71.487 66.92L280.48 176.423l.232-.443L71.719 66.477l-.232.443zM50.583 77.872l209 109.497.232-.442-209-109.498-.232.443zM29.685 88.825l209 109.497.232-.443-209-109.497-.232.443zM8.788 99.772L217.78 209.269l.233-.443L9.02 99.329l-.232.443zm83.596-43.8l209 109.498.232-.443-209-109.497-.232.443zm20.897-10.952l209.001 109.497.232-.442-209-109.498-.233.443zm20.904-10.947l209 109.498.232-.443-209-109.497-.232.442zm41.802-21.899L384.98 121.671l.232-.443L176.219 11.731l-.232.443zm-20.904 10.947l209 109.503.232-.443-209-109.503-.232.443zm41.803-21.898l.101.05.227-.444-.1-.052-.228.446zm.098.05l208.9 109.446.232-.443L197.216.83l-.232.443zm208.9 109.003l-209 109.503.232.442 209-109.502-.232-.443zM197.116 219.779l-209-109.503-.232.443 209 109.502.232-.442zm-209-109.06l209-109.498-.232-.442-209 109.497.232.443zm396.864-11.39L175.987 208.826l.232.443L385.212 99.772l-.232-.443zM280.486 44.577l-209 109.503.233.443 209-109.503-.233-.443zm-41.801-21.899l-209 109.503.232.443 209-109.503-.232-.443zm20.898 10.952l-209 109.498.232.443 209-109.498-.232-.443zm41.801 21.894l-209 109.503.232.443 209-109.503-.232-.443zm41.801 21.905l-209 109.498.232.442 209-109.497-.232-.443zm-20.903-10.952L113.281 175.98l.233.443 209-109.503-.232-.443zm41.801 21.9l-209 109.502.232.443 209-109.503-.232-.443zM217.788 11.724L8.781 121.228l.233.443L218.02 12.168l-.232-.443z"
          data-figma-gradient-fill='{"type":"GRADIENT_DIAMOND","stops":[{"color":{"r":0.0039215688593685627,"g":0.098039217293262482,"b":0.45098039507865906,"a":0.20000000298023224},"position":0.0},{"color":{"r":0.0039215688593685627,"g":0.098039217293262482,"b":0.45098039507865906,"a":0.0},"position":1.0}],"stopsVar":[{"color":{"r":0.0039215688593685627,"g":0.098039217293262482,"b":0.45098039507865906,"a":0.20000000298023224},"position":0.0},{"color":{"r":0.0039215688593685627,"g":0.098039217293262482,"b":0.45098039507865906,"a":0.0},"position":1.0}],"transform":{"m00":3.8826163421476489e-13,"m01":556.80718994140625,"m02":-81.4035644531250,"m10":-216.69062805175781,"m11":-0.53929191827774048,"m12":291.75885009765625},"opacity":1.0,"blendMode":"NORMAL","visible":true}'
        />
        <Defs>
          <ClipPath id="paint0_diamond_4769_18830_clip_path">
            <Path d="M71.719 66.477a.25.25 0 10-.232.443l.232-.443zM280.48 176.423a.25.25 0 00.338-.106.25.25 0 00-.106-.337l-.232.443zM50.815 77.429a.25.25 0 10-.232.443l.232-.443zm208.768 109.94a.25.25 0 00.232-.442l-.232.442zM29.917 88.382a.25.25 0 10-.232.443l.232-.443zm208.768 109.94a.25.25 0 10.232-.443l-.232.443zM9.02 99.329a.25.25 0 00-.232.443l.232-.443zm208.761 109.94a.25.25 0 00.233-.443l-.233.443zM92.616 55.53a.25.25 0 10-.232.443l.232-.443zm208.768 109.94a.25.25 0 10.232-.443l-.232.443zM113.514 44.577a.25.25 0 00-.233.443l.233-.443zm208.768 109.94a.25.25 0 00.232-.442l-.232.442zM134.417 33.631a.25.25 0 00-.232.442l.232-.443zm208.768 109.94a.25.25 0 00.338-.106.25.25 0 00-.106-.337l-.232.443zM176.219 11.731a.25.25 0 10-.232.443l.232-.443zm208.761 109.94a.25.25 0 10.232-.443l-.232.443zM155.315 22.678a.25.25 0 10-.232.443l.232-.443zm208.768 109.946a.25.25 0 00.232-.443l-.232.443zM197 1l.114-.223a.25.25 0 00-.23.002L197 1zm.1.051l.116-.221-.002-.001-.114.222zM406 110.497l.116.222a.25.25 0 000-.443l-.116.221zM197 220l-.116.221a.245.245 0 00.232 0L197 220zM-12 110.497l-.116-.221a.25.25 0 000 .443l.116-.222zm397.212-10.725a.25.25 0 10-.232-.443l.232.443zM175.987 208.826a.25.25 0 10.232.443l-.232-.443zM280.719 45.02a.25.25 0 00-.233-.443l.233.443zM71.487 154.08a.25.25 0 00.232.443l-.232-.443zm167.43-130.96a.25.25 0 10-.232-.442l.232.443zM29.685 132.182a.25.25 0 10.232.443l-.232-.443zm230.13-98.108a.25.25 0 00-.232-.443l.232.443zM50.583 143.128a.25.25 0 10.232.443l-.232-.443zm251.033-87.16a.25.25 0 10-.232-.443l.232.442zM92.384 165.026a.25.25 0 00.232.443l-.232-.443zm251.033-87.155a.25.25 0 10-.232-.443l.232.443zM134.185 186.927a.25.25 0 00.232.442l-.232-.442zM322.514 66.92a.25.25 0 00-.232-.443l.232.443zM113.281 175.98a.25.25 0 10.233.443l-.233-.443zm251.034-87.16a.25.25 0 00-.232-.444l.232.443zM155.083 197.878a.25.25 0 10.232.443l-.232-.443zm62.937-185.71a.25.25 0 00-.232-.444l.232.443zM8.781 121.227a.25.25 0 00.233.443l-.233-.443zM71.487 66.92L280.48 176.423l.232-.443L71.719 66.477l-.232.443zM50.583 77.872l209 109.497.232-.442-209-109.498-.232.443zM29.685 88.825l209 109.497.232-.443-209-109.497-.232.443zM8.788 99.772L217.78 209.269l.233-.443L9.02 99.329l-.232.443zm83.596-43.8l209 109.498.232-.443-209-109.497-.232.443zm20.897-10.952l209.001 109.497.232-.442-209-109.498-.233.443zm20.904-10.947l209 109.498.232-.443-209-109.497-.232.442zm41.802-21.899L384.98 121.671l.232-.443L176.219 11.731l-.232.443zm-20.904 10.947l209 109.503.232-.443-209-109.503-.232.443zm41.803-21.898l.101.05.227-.444-.1-.052-.228.446zm.098.05l208.9 109.446.232-.443L197.216.83l-.232.443zm208.9 109.003l-209 109.503.232.442 209-109.502-.232-.443zM197.116 219.779l-209-109.503-.232.443 209 109.502.232-.442zm-209-109.06l209-109.498-.232-.442-209 109.497.232.443zm396.864-11.39L175.987 208.826l.232.443L385.212 99.772l-.232-.443zM280.486 44.577l-209 109.503.233.443 209-109.503-.233-.443zm-41.801-21.899l-209 109.503.232.443 209-109.503-.232-.443zm20.898 10.952l-209 109.498.232.443 209-109.498-.232-.443zm41.801 21.894l-209 109.503.232.443 209-109.503-.232-.443zm41.801 21.905l-209 109.498.232.442 209-109.497-.232-.443zm-20.903-10.952L113.281 175.98l.233.443 209-109.503-.232-.443zm41.801 21.9l-209 109.502.232.443 209-109.503-.232-.443zM217.788 11.724L8.781 121.228l.233.443L218.02 12.168l-.232-.443z" />
          </ClipPath>
          <LinearGradient
            id="paint0_diamond_4769_18830"
            x1={0}
            y1={0}
            x2={500}
            y2={500}
            gradientUnits="userSpaceOnUse">
            <Stop stopColor={color} stopOpacity={0.2} />
            <Stop offset={1} stopColor="#011973" stopOpacity={0} />
          </LinearGradient>
        </Defs>
      </Svg>
      <View style={[center as ViewStyle, styles.content]}>{children}</View>
    </View>
  );
};

const styles = StyleSheet.create({
  content: {
    position: 'absolute',
    alignSelf: 'center',
    bottom: '5%',
  },
});

export default MeshBackground;
