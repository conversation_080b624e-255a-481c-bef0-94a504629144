import React from 'react';
import Svg, { Path } from 'react-native-svg';

import colors from '~/styles/colors';
import { SvgProps } from '~/types/svg.types';

const ArrowUp = ({
  color = colors.darkBlue500,
  width = 24,
  height = 24,
  ...props
}: SvgProps) => (
  <Svg width={width} height={height} viewBox="0 0 24 24" {...props}>
    <Path
      fill={color}
      d={
        'M19.7073 10.7059C19.5123 10.9009 19.2563 10.9988 19.0003 10.9988C18.7443 10.9988 18.4882 10.9009 18.2933 10.7059L13.0003 5.41291V20.9988C13.0003 21.5508 12.5533 21.9988 12.0003 21.9988C11.4473 21.9988 11.0003 21.5508 11.0003 20.9988V5.41291L5.70731 10.7059C5.31631 11.0969 4.68425 11.0969 4.29325 10.7059C3.90225 10.3149 3.90225 9.68281 4.29325 9.29181L11.2923 2.29279C11.3853 2.19979 11.4952 2.12675 11.6182 2.07575C11.8622 1.97475 12.1384 1.97475 12.3824 2.07575C12.5054 2.12675 12.6153 2.19979 12.7083 2.29279L19.7073 9.29181C20.0983 9.68281 20.0983 10.3149 19.7073 10.7059Z'
      }
    />
  </Svg>
);

export default ArrowUp;
