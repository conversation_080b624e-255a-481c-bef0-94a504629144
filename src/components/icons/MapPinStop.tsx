import React from 'react';
import Svg, { Path } from 'react-native-svg';
import colors from '~/styles/colors';
import { SvgProps } from '~/types/svg.types';

const MapPinStop = ({ color = colors.darkBlue500, ...props }: SvgProps) => (
  <Svg width={74} height={83} viewBox="0 0 74 83" fill="none" {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M37.0002 12C48.5982 12 58.0002 21.5901 58.0002 33.42C58.0002 42.4904 52.473 50.244 44.6666 53.3678L38.1963 62.3887C38.0615 62.5775 37.8824 62.7316 37.6742 62.838C37.466 62.9444 37.2348 63 37.0002 63C36.7656 63 36.5345 62.9444 36.3263 62.838C36.1181 62.7316 35.939 62.5775 35.8041 62.3887L29.3339 53.3678C21.5275 50.244 16.0002 42.4904 16.0002 33.42C16.0002 21.5901 25.4023 12 37.0002 12Z"
      fill="white"
    />
    <Path
      d="M19 33C19 23.0589 27.0589 15 37 15C46.9411 15 55 23.0589 55 33C55 42.9411 46.9411 51 37 51C27.0589 51 19 42.9411 19 33Z"
      fill={color}
    />
    <Path
      d="M46.4769 30.085L39.0648 23.9891C37.8678 23.0071 36.1329 23.0061 34.9359 23.9891L27.5239 30.084C27.2049 30.347 27.1578 30.8191 27.4208 31.1401C27.6838 31.4611 28.1569 31.5051 28.4769 31.2431L29.4998 30.4011V39.0001C29.4998 41.2901 30.7098 42.5001 32.9998 42.5001H40.9998C43.2898 42.5001 44.4998 41.2901 44.4998 39.0001V30.402L45.5228 31.2441C45.6628 31.3591 45.8318 31.4151 45.9998 31.4151C46.2158 31.4151 46.4308 31.322 46.5788 31.142C46.8428 30.82 46.7959 30.348 46.4769 30.085ZM39.4998 41.0001H34.4998V37.5001C34.4998 36.1191 35.6188 35.0001 36.9998 35.0001C38.3808 35.0001 39.4998 36.1191 39.4998 37.5001V41.0001Z"
      fill="white"
    />
  </Svg>
);

export default MapPinStop;
