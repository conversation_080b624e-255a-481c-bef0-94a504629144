import React from 'react';
import Svg, { Path } from 'react-native-svg';
import { SvgProps } from '~/types/svg.types';

const LocationPinOutlined = ({ color, size = 20, ...props }: SvgProps) => (
  <Svg width={size} height={size} viewBox="0 0 20 20" fill="none" {...props}>
    <Path
      fill={color}
      d="M10 1.875C5.97921 1.875 2.70837 5.14583 2.70837 9.16667C2.70837 13.4317 6.62253 16.0166 9.21253 17.7275L9.65337 18.02C9.75837 18.09 9.87921 18.125 10 18.125C10.1209 18.125 10.2417 18.09 10.3467 18.02L10.7875 17.7275C13.3775 16.0166 17.2917 13.4317 17.2917 9.16667C17.2917 5.14583 14.0209 1.875 10 1.875ZM10.0992 16.6842L10 16.7501L9.90087 16.6842C7.39254 15.0275 3.95837 12.7592 3.95837 9.16667C3.95837 5.835 6.66837 3.125 10 3.125C13.3317 3.125 16.0417 5.835 16.0417 9.16667C16.0417 12.7592 12.6067 15.0283 10.0992 16.6842ZM10 6.45833C8.50671 6.45833 7.29171 7.67333 7.29171 9.16667C7.29171 10.66 8.50671 11.875 10 11.875C11.4934 11.875 12.7084 10.66 12.7084 9.16667C12.7084 7.67333 11.4934 6.45833 10 6.45833ZM10 10.625C9.19587 10.625 8.54171 9.97083 8.54171 9.16667C8.54171 8.3625 9.19587 7.70833 10 7.70833C10.8042 7.70833 11.4584 8.3625 11.4584 9.16667C11.4584 9.97083 10.8042 10.625 10 10.625Z"
    />
  </Svg>
);

export default LocationPinOutlined;
