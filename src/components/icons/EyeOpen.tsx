import React from 'react';
import Svg, { Path } from 'react-native-svg';

import colors from '~/styles/colors';
import { SvgProps } from '~/types/svg.types';

const EyeOpen = ({
  color = colors.darkBlue500,
  size = 24,
  ...props
}: SvgProps) => (
  <Svg fill="none" viewBox="0 0 24 25" width={size} height={size} {...props}>
    <Path
      fill={color}
      d="M21.2352 10.6378C19.9222 8.43882 16.9751 4.74988 12.0001 4.74988C7.02511 4.74988 4.07801 8.43882 2.76501 10.6378C2.07801 11.7858 2.07801 13.2129 2.76501 14.3619C4.07801 16.5609 7.02511 20.2499 12.0001 20.2499C16.9751 20.2499 19.9222 16.5609 21.2352 14.3619C21.9222 13.2129 21.9222 11.7868 21.2352 10.6378ZM19.9481 13.5919C18.7981 15.5179 16.2351 18.7499 12.0001 18.7499C7.76511 18.7499 5.20212 15.5189 4.05212 13.5919C3.65012 12.9179 3.65012 12.0809 4.05212 11.4069C5.20212 9.48086 7.76511 6.2489 12.0001 6.2489C16.2351 6.2489 18.7981 9.47986 19.9481 11.4069C20.3511 12.0819 20.3511 12.9179 19.9481 13.5919ZM12.0001 8.24988C9.65611 8.24988 7.75011 10.1569 7.75011 12.4999C7.75011 14.8429 9.65611 16.7499 12.0001 16.7499C14.3441 16.7499 16.2501 14.8429 16.2501 12.4999C16.2501 10.1569 14.3441 8.24988 12.0001 8.24988ZM12.0001 15.2499C10.4831 15.2499 9.25011 14.0169 9.25011 12.4999C9.25011 10.9829 10.4831 9.74988 12.0001 9.74988C13.5171 9.74988 14.7501 10.9829 14.7501 12.4999C14.7501 14.0169 13.5171 15.2499 12.0001 15.2499Z"
    />
  </Svg>
);

export default EyeOpen;
