import React from 'react';
import Svg, { Path } from 'react-native-svg';

import colors from '~/styles/colors';
import { SvgProps } from '~/types/svg.types';

const Photo = ({ color = colors.grey600, size = 25, ...props }: SvgProps) => (
  <Svg fill="none" viewBox="0 0 25 24" width={size} height={size} {...props}>
    <Path
      fill={color}
      d="M18.667 3.25h-12c-2.418 0-3.75 1.332-3.75 3.75v10c0 2.418 1.332 3.75 3.75 3.75h12c2.418 0 3.75-1.332 3.75-3.75V7c0-2.418-1.332-3.75-3.75-3.75m-12 1.5h12c1.577 0 2.25.673 2.25 2.25v6.189l-4.01-4.01c-.66-.66-1.82-.66-2.48 0l-4.76 4.76-.76-.76c-.66-.66-1.82-.66-2.48 0l-2.01 2.01V7c0-1.577.673-2.25 2.25-2.25m12 14.5h-12c-1.472 0-2.148-.596-2.233-1.957l3.053-3.053c.127-.126.232-.126.359 0l.939.939c.467.468 1.296.467 1.761 0l4.94-4.94c.127-.126.232-.126.359 0l5.07 5.07V17c.002 1.577-.671 2.25-2.248 2.25M7.417 9a1.25 1.25 0 1 1 2.5 0 1.25 1.25 0 0 1-2.5 0"
    />
  </Svg>
);

export default Photo;
