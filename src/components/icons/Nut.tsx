import React from 'react';
import Svg, { Path } from 'react-native-svg';
import colors from '~/styles/colors';

import { SvgProps } from '~/types/svg.types';

const Nut = ({ color = colors.darkBlue500, size = 24, ...props }: SvgProps) => (
  <Svg width={size} height={size} fill="none" {...props}>
    <Path
      fill={color}
      d="M12 8.24997C9.93205 8.24997 8.25005 9.93197 8.25005 12C8.25005 14.068 9.93205 15.75 12 15.75C14.068 15.75 15.75 14.068 15.75 12C15.75 9.93197 14.068 8.24997 12 8.24997ZM12 14.25C10.759 14.25 9.75005 13.241 9.75005 12C9.75005 10.759 10.759 9.74997 12 9.74997C13.241 9.74997 14.25 10.759 14.25 12C14.25 13.241 13.241 14.25 12 14.25ZM21.2081 13.953C20.5141 13.551 20.082 12.803 20.081 12C20.08 11.199 20.5091 10.452 21.2121 10.045C21.7271 9.74598 21.9031 9.08296 21.6051 8.56696L19.9331 5.68097C19.6351 5.16597 18.972 4.98898 18.456 5.28598C17.757 5.68898 16.8881 5.68898 16.1871 5.28198C15.4961 4.88098 15.0661 4.13598 15.0661 3.33698C15.0661 2.73798 14.578 2.25098 13.979 2.25098H10.024C9.42403 2.25098 8.93706 2.73798 8.93706 3.33698C8.93706 4.13598 8.50704 4.88097 7.81404 5.28397C7.11504 5.68897 6.24705 5.68996 5.54805 5.28696C5.03105 4.98896 4.36906 5.16698 4.07106 5.68198L2.39705 8.57098C2.09905 9.08598 2.27604 9.74796 2.79604 10.05C3.48904 10.451 3.92105 11.198 3.92305 11.999C3.92505 12.801 3.49504 13.55 2.79304 13.957C2.54304 14.102 2.36305 14.335 2.28905 14.615C2.21505 14.894 2.25306 15.185 2.39806 15.436L4.06905 18.32C4.36705 18.836 5.03005 19.015 5.54805 18.716C6.24705 18.313 7.11405 18.314 7.80305 18.713L7.80504 18.714C7.80804 18.716 7.81105 18.718 7.81505 18.72C8.50605 19.121 8.93504 19.866 8.93404 20.666C8.93404 21.265 9.42103 21.752 10.02 21.752H13.979C14.578 21.752 15.065 21.265 15.065 20.667C15.065 19.867 15.495 19.122 16.189 18.719C16.887 18.314 17.755 18.312 18.455 18.716C18.971 19.014 19.6331 18.837 19.9321 18.322L21.606 15.433C21.903 14.916 21.7261 14.253 21.2081 13.953ZM18.831 17.227C17.741 16.752 16.476 16.817 15.434 17.42C14.401 18.019 13.7191 19.078 13.5871 20.25H10.41C10.28 19.078 9.59603 18.017 8.56303 17.419C7.52303 16.816 6.25605 16.752 5.16905 17.227L3.89305 15.024C4.84805 14.321 5.42504 13.193 5.42104 11.993C5.41804 10.801 4.84204 9.68097 3.89204 8.97797L5.16905 6.77396C6.25705 7.24796 7.52405 7.18396 8.56605 6.57996C9.59805 5.98196 10.28 4.92198 10.412 3.75098H13.5871C13.7181 4.92298 14.4011 5.98197 15.4361 6.58197C16.475 7.18497 17.742 7.24896 18.831 6.77496L20.108 8.97797C19.155 9.67997 18.579 10.806 18.581 12.004C18.582 13.198 19.1581 14.32 20.1091 15.025L18.831 17.227Z"
    />
  </Svg>
);

export default Nut;
