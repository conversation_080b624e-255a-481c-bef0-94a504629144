import React from 'react';
import Svg, { Path } from 'react-native-svg';

import colors from '~/styles/colors';
import { SvgProps } from '~/types/svg.types';

const Trash = ({ color = colors.red600, size = 20, ...props }: SvgProps) => (
  <Svg fill="none" viewBox="0 0 20 20" width={size} height={size} {...props}>
    <Path
      fill={color}
      d="M17.5 4.375h-2.966c-.75 0-.782-.095-.988-.712l-.168-.505a1.87 1.87 0 0 0-1.779-1.283H8.401c-.809 0-1.523.515-1.779 1.283l-.168.505c-.206.618-.237.712-.988.712H2.5a.625.625 0 0 0 0 1.25h1.082l.639 9.583c.123 1.854 1.26 2.917 3.118 2.917h5.323c1.857 0 2.994-1.063 3.118-2.918l.64-9.582h1.08a.625.625 0 0 0 0-1.25m-9.692-.822a.62.62 0 0 1 .593-.428h3.198c.27 0 .508.172.593.428l.168.505q.054.165.113.317H7.525q.06-.154.114-.317zm6.724 11.571c-.08 1.194-.675 1.751-1.871 1.751H7.338c-1.196 0-1.79-.556-1.87-1.75l-.634-9.5h.631c.104 0 .19-.01.284-.018.029.005.054.018.083.018h8.334c.03 0 .055-.013.083-.018.093.007.18.018.284.018h.631zm-2.24-5.957v4.166a.625.625 0 0 1-1.25 0V9.167a.625.625 0 0 1 1.25 0m-3.334 0v4.166a.625.625 0 0 1-1.25 0V9.167a.625.625 0 0 1 1.25 0"
    />
  </Svg>
);

export default Trash;
