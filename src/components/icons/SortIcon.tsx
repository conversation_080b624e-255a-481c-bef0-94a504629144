import React from 'react';
import Svg, { Path } from 'react-native-svg';
import { SvgProps } from '~/types/svg.types';

const SortIcon = ({ color, size = 24, ...props }: SvgProps) => (
  <Svg width={size} height={size} fill="none" {...props}>
    <Path
      fill={color}
      d="M20 6.75H4a.75.75 0 0 1 0-1.5h16a.75.75 0 0 1 0 1.5ZM17.75 12a.75.75 0 0 0-.75-.75H7a.75.75 0 0 0 0 1.5h10a.75.75 0 0 0 .75-.75Zm-3 6a.75.75 0 0 0-.75-.75h-4a.75.75 0 0 0 0 1.5h4a.75.75 0 0 0 .75-.75Z"
    />
  </Svg>
);

export default SortIcon;
