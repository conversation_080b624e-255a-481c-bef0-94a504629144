import React from 'react';
import Svg, { Path } from 'react-native-svg';
import colors from '~/styles/colors';

import { SvgProps } from '~/types/svg.types';

const Arrow = ({
  color = colors.darkBlue500,
  size = 24,
  ...props
}: SvgProps) => (
  <Svg width={size} height={size} fill="none" {...props}>
    <Path
      fill={color}
      d="M20.692 12.287a.748.748 0 0 1-.162.244l-4 4a.748.748 0 0 1-1.06 0 .75.75 0 0 1 0-1.061l2.72-2.72H4a.75.75 0 0 1 0-1.5h14.189l-2.72-2.72a.75.75 0 0 1 1.061-1.061l4 4a.748.748 0 0 1 .162.818Z"
    />
  </Svg>
);

export default Arrow;
