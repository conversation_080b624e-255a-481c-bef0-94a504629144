import React from 'react';
import Svg, { Path } from 'react-native-svg';

import colors from '~/styles/colors';
import { SvgProps } from '~/types/svg.types';

const Service = ({
  color = colors.darkBlue500,
  width = 24,
  height = 24,
  ...props
}: SvgProps) => (
  <Svg width={width} height={height} viewBox="0 0 24 24" {...props}>
    <Path
      fill={color}
      d={
        'M21.271 6.28384C21.177 6.05084 20.972 5.88083 20.726 5.82983C20.479 5.77883 20.224 5.85584 20.047 6.03284L17.865 8.20386C17.578 8.48986 17.111 8.48985 16.823 8.20285L15.798 7.17786C15.511 6.89086 15.51 6.42286 15.797 6.13486L17.968 3.95386C18.145 3.77586 18.221 3.52084 18.171 3.27484C18.121 3.02884 17.95 2.82386 17.717 2.72986C15.795 1.95286 13.654 2.13085 11.841 3.21585C11.019 3.70885 10.323 4.38585 9.83 5.17185C9.83 5.17185 9.82899 5.17286 9.82899 5.17386L9.74699 5.30884C8.93499 6.65184 8.621 8.15886 8.84 9.66986C8.871 9.88386 8.792 10.1098 8.629 10.2738L3.306 15.5968C2.625 16.2778 2.25 17.1829 2.25 18.1469C2.25 19.1109 2.625 20.0149 3.306 20.6959C3.987 21.3769 4.892 21.7519 5.856 21.7519C6.819 21.7519 7.724 21.3769 8.405 20.6959L13.728 15.3728C13.891 15.2098 14.118 15.1308 14.332 15.1618C15.84 15.3808 17.344 15.0708 18.681 14.2658C19.544 13.7468 20.269 13.0219 20.774 12.1729C21.869 10.3589 22.051 8.21284 21.271 6.28384ZM19.487 11.4008C19.106 12.0418 18.56 12.5879 17.907 12.9789C16.598 13.7679 15.357 13.7928 14.548 13.6758C13.864 13.5758 13.164 13.8139 12.668 14.3109L7.345 19.6339C6.548 20.4298 5.162 20.4289 4.367 19.6339C3.97 19.2358 3.75 18.7068 3.75 18.1448C3.75 17.5828 3.969 17.0539 4.367 16.6559L9.69099 11.3329C10.187 10.8359 10.424 10.1339 10.325 9.45386C10.157 8.29186 10.401 7.12685 11.035 6.07785L11.102 5.96784C11.47 5.38184 11.993 4.87484 12.612 4.50284C13.886 3.73984 15.082 3.66484 16 3.80484L14.733 5.07684C13.865 5.94984 13.866 7.36786 14.737 8.23886L15.762 9.26285C16.633 10.1348 18.051 10.1358 18.924 9.26685L20.195 8.00186C20.364 9.15686 20.123 10.3478 19.487 11.4008ZM6.02 16.9998H6.00999C5.45799 16.9998 5.015 17.4478 5.015 17.9998C5.015 18.5518 5.468 18.9998 6.02 18.9998C6.572 18.9998 7.02 18.5518 7.02 17.9998C7.02 17.4478 6.572 16.9998 6.02 16.9998Z'
      }
    />
  </Svg>
);

export default Service;
