import React from 'react';
import Svg, { Path } from 'react-native-svg';

import colors from '~/styles/colors';
import { SvgProps } from '~/types/svg.types';

const RouteOutlined = ({
  color = colors.blue500,
  size = 24,
  ...props
}: SvgProps) => (
  <Svg fill="none" viewBox="0 0 25 24" width={size} height={size} {...props}>
    <Path
      fill={color}
      d="M7.75 15C7.75 16.241 8.759 17.25 10 17.25H12C12.414 17.25 12.75 17.586 12.75 18C12.75 18.414 12.414 18.75 12 18.75H10C7.932 18.75 6.25 17.068 6.25 15C6.25 12.932 7.932 11.25 10 11.25H14C15.241 11.25 16.25 10.241 16.25 9C16.25 7.759 15.241 6.75 14 6.75H12C11.586 6.75 11.25 6.414 11.25 6C11.25 5.586 11.586 5.25 12 5.25H14C16.068 5.25 17.75 6.932 17.75 9C17.75 11.068 16.068 12.75 14 12.75H10C8.759 12.75 7.75 13.759 7.75 15ZM6.41602 10.374C6.29002 10.458 6.145 10.5 6 10.5C5.855 10.5 5.70998 10.458 5.58398 10.374L5.38702 10.2439C4.21402 9.4689 2.25 8.172 2.25 6C2.25 3.932 3.932 2.25 6 2.25C8.068 2.25 9.75 3.932 9.75 6C9.75 8.171 7.78598 9.4689 6.61298 10.2439L6.41602 10.374ZM6 8.85107C7.011 8.17807 8.25 7.273 8.25 6C8.25 4.759 7.241 3.75 6 3.75C4.759 3.75 3.75 4.759 3.75 6C3.75 7.273 4.989 8.17707 6 8.85107ZM6.02002 5H6.01001C5.45801 5 5.01501 5.448 5.01501 6C5.01501 6.552 5.46802 7 6.02002 7C6.57202 7 7.02002 6.552 7.02002 6C7.02002 5.448 6.57202 5 6.02002 5ZM21.75 17.25C21.75 19.421 19.786 20.7189 18.613 21.4939L18.416 21.624C18.29 21.708 18.145 21.75 18 21.75C17.855 21.75 17.71 21.708 17.584 21.624L17.387 21.4939C16.214 20.7189 14.25 19.422 14.25 17.25C14.25 15.182 15.932 13.5 18 13.5C20.068 13.5 21.75 15.182 21.75 17.25ZM20.25 17.25C20.25 16.009 19.241 15 18 15C16.759 15 15.75 16.009 15.75 17.25C15.75 18.523 16.989 19.4271 18 20.1011C19.011 19.4271 20.25 18.523 20.25 17.25ZM18.02 16.25H18.01C17.458 16.25 17.015 16.698 17.015 17.25C17.015 17.802 17.468 18.25 18.02 18.25C18.572 18.25 19.02 17.802 19.02 17.25C19.02 16.698 18.572 16.25 18.02 16.25Z"
    />
  </Svg>
);

export default RouteOutlined;
