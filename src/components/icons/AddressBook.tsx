import React from 'react';
import Svg, { Path } from 'react-native-svg';

import colors from '~/styles/colors';
import { SvgProps } from '~/types/svg.types';

type AddressBookProps = SvgProps & {
  filled?: boolean;
};

const AddressBook = ({
  color = colors.darkBlue300,
  size = 20,
  filled = true,
  ...props
}: AddressBookProps) => (
  <Svg fill="none" viewBox="0 -2 24 24" width={size} height={size} {...props}>
    <Path
      d="M19 12.25H17.75V7.75H19C19.414 7.75 19.75 7.414 19.75 7C19.75 6.586 19.414 6.25 19 6.25H17.75V4C17.75 1.582 16.418 0.25 14 0.25H4C1.582 0.25 0.25 1.582 0.25 4V16C0.25 18.418 1.582 19.75 4 19.75H14C16.418 19.75 17.75 18.418 17.75 16V13.75H19C19.414 13.75 19.75 13.414 19.75 13C19.75 12.586 19.414 12.25 19 12.25ZM16.25 16C16.25 17.577 15.577 18.25 14 18.25H4C2.423 18.25 1.75 17.577 1.75 16V4C1.75 2.423 2.423 1.75 4 1.75H14C15.577 1.75 16.25 2.423 16.25 4V16ZM9 9.75C10.517 9.75 11.75 8.516 11.75 7C11.75 5.484 10.517 4.25 9 4.25C7.483 4.25 6.25 5.484 6.25 7C6.25 8.516 7.483 9.75 9 9.75ZM9 5.75C9.689 5.75 10.25 6.311 10.25 7C10.25 7.689 9.689 8.25 9 8.25C8.311 8.25 7.75 7.689 7.75 7C7.75 6.311 8.311 5.75 9 5.75ZM13.75 14.36V15C13.75 15.414 13.414 15.75 13 15.75C12.586 15.75 12.25 15.414 12.25 15V14.36C12.25 13.31 11.5111 12.25 9.86011 12.25H8.13892C6.48892 12.25 5.74902 13.31 5.74902 14.36V15C5.74902 15.414 5.41302 15.75 4.99902 15.75C4.58502 15.75 4.24902 15.414 4.24902 15V14.36C4.24902 12.566 5.58492 10.75 8.13892 10.75H9.86011C12.4141 10.75 13.75 12.566 13.75 14.36Z"
      fill={filled ? color : 'none'}
      stroke={filled ? 'none' : color}
      strokeWidth={filled ? 0 : 1.5}
    />
  </Svg>
);

export default AddressBook;
