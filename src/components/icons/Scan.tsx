import React from 'react';
import Svg, { Path } from 'react-native-svg';
import colors from '~/styles/colors';
import { SvgProps } from '~/types/svg.types';

const Scan = ({
  color = colors.darkBlue500,
  size = 32,
  ...props
}: SvgProps) => (
  <Svg width={size} height={size} viewBox="0 0 32 32" fill="none" {...props}>
    <Path
      fill={color}
      d="M28 15H4C3.448 15 3 15.448 3 16C3 16.552 3.448 17 4 17H5.66667V24C5.66667 27.224 7.44267 29 10.6667 29H21.3333C24.5573 29 26.3333 27.224 26.3333 24V17H28C28.552 17 29 16.552 29 16C29 15.448 28.552 15 28 15ZM24.3333 24C24.3333 26.1027 23.436 27 21.3333 27H10.6667C8.564 27 7.66667 26.1027 7.66667 24V17H24.3333V24ZM5.66667 12V8C5.66667 4.776 7.44267 3 10.6667 3H21.3333C24.5573 3 26.3333 4.776 26.3333 8V12C26.3333 12.552 25.8853 13 25.3333 13C24.7813 13 24.3333 12.552 24.3333 12V8C24.3333 5.89733 23.436 5 21.3333 5H10.6667C8.564 5 7.66667 5.89733 7.66667 8V12C7.66667 12.552 7.21867 13 6.66667 13C6.11467 13 5.66667 12.552 5.66667 12Z"
    />
  </Svg>
);

export default Scan;
