import React from 'react';
import Svg, { Path } from 'react-native-svg';

import colors from '~/styles/colors';
import { SvgProps } from '~/types/svg.types';

const Route = ({ color = colors.red600, size = 25, ...props }: SvgProps) => (
  <Svg fill="none" viewBox="0 0 25 24" width={size} height={size} {...props}>
    <Path
      fill={color}
      d="M12.5 18.75h-2A3.754 3.754 0 0 1 6.75 15a3.754 3.754 0 0 1 3.75-3.75h4A2.25 2.25 0 0 0 16.75 9a2.25 2.25 0 0 0-2.25-2.25h-2a.75.75 0 0 1 0-1.5h2A3.754 3.754 0 0 1 18.25 9a3.754 3.754 0 0 1-3.75 3.75h-4A2.25 2.25 0 0 0 8.25 15a2.25 2.25 0 0 0 2.25 2.25h2a.75.75 0 0 1 0 1.5M10 6c0 2.037-1.82 3.239-3.025 4.035l-.198.131a.5.5 0 0 1-.554 0l-.198-.131C4.82 9.239 3 8.037 3 6c0-1.93 1.57-3.5 3.5-3.5S10 4.07 10 6M7.52 6a1 1 0 0 0-1-1h-.01a.996.996 0 0 0-.995 1c0 .552.453 1 1.005 1a1 1 0 0 0 1-1M22 17.25c0 2.037-1.82 3.239-3.025 4.035l-.198.131a.5.5 0 0 1-.554 0l-.198-.131C16.82 20.489 15 19.287 15 17.25c0-1.93 1.57-3.5 3.5-3.5s3.5 1.57 3.5 3.5m-2.48 0a1 1 0 0 0-1-1h-.01a.996.996 0 0 0-.995 1c0 .552.453 1 1.005 1a1 1 0 0 0 1-1"
    />
  </Svg>
);

export default Route;
