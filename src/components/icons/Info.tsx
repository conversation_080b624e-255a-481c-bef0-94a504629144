import React from 'react';
import Svg, { Path } from 'react-native-svg';

import colors from '~/styles/colors';
import { SvgProps } from '~/types/svg.types';

const Info = ({ color = colors.grey600, size = 24, ...props }: SvgProps) => (
  <Svg fill="none" viewBox="0 0 24 24" width={size} height={size} {...props}>
    <Path
      fill={color}
      d="M12 22.75C6.072 22.75 1.25 17.928 1.25 12S6.072 1.25 12 1.25 22.75 6.072 22.75 12 17.928 22.75 12 22.75m0-20c-5.101 0-9.25 4.149-9.25 9.25s4.149 9.25 9.25 9.25 9.25-4.149 9.25-9.25S17.101 2.75 12 2.75m.71 10.614c.025-.072.161-.345.893-.835 1.18-.79 1.708-1.921 1.489-3.183-.222-1.275-1.261-2.322-2.527-2.545a3.14 3.14 0 0 0-2.574.68 3.16 3.16 0 0 0-1.13 2.43.75.75 0 0 0 1.5 0c0-.496.216-.963.594-1.28a1.62 1.62 0 0 1 1.349-.354c.646.114 1.197.671 1.311 1.325.043.247.174.997-.847 1.68-.827.555-1.297 1.064-1.478 1.604a.75.75 0 0 0 1.42.478m.31 3.136a1 1 0 0 0-1-1h-.01a.996.996 0 0 0-.995 1c0 .552.453 1 1.005 1a1 1 0 0 0 1-1"
    />
  </Svg>
);

export default Info;
