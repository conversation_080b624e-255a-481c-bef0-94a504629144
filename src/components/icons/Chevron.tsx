import React from 'react';
import Svg, { Path } from 'react-native-svg';
import colors from '~/styles/colors';

import { SvgProps } from '~/types/svg.types';

const Chevron = ({
  color = colors.darkBlue500,
  size = 24,
  ...props
}: SvgProps) => (
  <Svg fill="none" viewBox="0 0 24 24" width={size} height={size} {...props}>
    <Path
      fill={color}
      d="M12 15a1 1 0 0 1-.707-.293l-4-4a.999.999 0 1 1 1.414-1.414L12 12.586l3.293-3.293a.999.999 0 1 1 1.414 1.414l-4 4A1 1 0 0 1 12 15"
    />
  </Svg>
);

export default Chevron;
