import React from 'react';
import Svg, { Path } from 'react-native-svg';

import colors from '~/styles/colors';
import { SvgProps } from '~/types/svg.types';

const Mobile = ({ color = colors.white, size = 20, ...props }: SvgProps) => (
  <Svg fill="none" viewBox="0 0 16 20" width={size} height={size} {...props}>
    <Path
      fill={color}
      d="M12 19.75H4C1.582 19.75.25 18.418.25 16V4C.25 1.582 1.582.25 4 .25h8c2.418 0 3.75 1.332 3.75 3.75v12c0 2.418-1.332 3.75-3.75 3.75m-8-18c-1.577 0-2.25.673-2.25 2.25v12c0 1.577.673 2.25 2.25 2.25h8c1.577 0 2.25-.673 2.25-2.25V4c0-1.577-.673-2.25-2.25-2.25zM10.25 4a.75.75 0 0 0-.75-.75h-3a.75.75 0 0 0 0 1.5h3a.75.75 0 0 0 .75-.75M8 15a1 1 0 1 0 0 2 1 1 0 0 0 0-2"
    />
  </Svg>
);

export default Mobile;
