import React from 'react';
import Svg, { Circle } from 'react-native-svg';

import colors from '~/styles/colors';
import { SvgProps } from '~/types/svg.types';

const CaptureButton = ({
  color = colors.white,
  size = 76,
  ...props
}: SvgProps) => (
  <Svg fill="none" viewBox="0 0 75 76" width={size} height={size} {...props}>
    <Circle cx={37.5} cy={38.46} r={29.5} fill={color} />
    <Circle cx={37.5} cy={38.46} r={36} stroke={color} strokeWidth={3} />
  </Svg>
);

export default CaptureButton;
