import React from 'react';
import Svg, { Path } from 'react-native-svg';

import { SvgProps } from '~/types/svg.types';
import colors from '~/styles/colors';

const Cross = ({ color = colors.white, size = 20, ...props }: SvgProps) => {
  return (
    <Svg fill="none" viewBox="0 0 20 20" width={size} height={size} {...props}>
      <Path
        fill={color}
        d="M15.442 14.558a.625.625 0 0 1-.884.885L10 10.884l-4.558 4.559a.623.623 0 0 1-.884 0 .625.625 0 0 1 0-.885L9.117 10 4.558 5.442a.625.625 0 0 1 .885-.884L10 9.116l4.558-4.558a.625.625 0 0 1 .884.884L10.885 10z"
      />
    </Svg>
  );
};

export default Cross;
