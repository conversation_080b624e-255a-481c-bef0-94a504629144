import React from 'react';
import { StyleSheet, View, ViewStyle } from 'react-native';
import Svg, { Defs, LinearGradient, Rect, Stop } from 'react-native-svg';
import colors from '~/styles/colors';
import { center } from '~/styles/views';

type CircleBackgroundProps = {
  outlineColor?: string;
  fillColor?: string;
  children: React.ReactNode;
};

const CircleBackground: React.FC<CircleBackgroundProps> = ({
  outlineColor = colors.darkBlue500,
  fillColor = colors.backgroundLight,
  children,
}) => {
  return (
    <View style={styles.container}>
      <Svg width={40} height={40} viewBox="0 0 40 40" fill="none">
        <Rect
          x="0.5"
          y="0.5"
          width="39"
          height="39"
          rx="19.5"
          fill={fillColor}
        />
        <Rect
          x="0.5"
          y="0.5"
          width="39"
          height="39"
          rx="19.5"
          stroke="url(#paint0_linear_8405_8238)"
        />
        <Defs>
          <LinearGradient
            id="paint0_linear_8405_8238"
            x1="90.7692"
            y1="20"
            x2="-27.3077"
            y2="20"
            gradientUnits="userSpaceOnUse">
            <Stop stopColor={outlineColor} />
            <Stop offset="0.599149" stopColor={outlineColor} stopOpacity="0" />
          </LinearGradient>
        </Defs>
      </Svg>
      <View style={styles.content}>{children}</View>
    </View>
  );
};

const styles = StyleSheet.create({
  content: {
    position: 'absolute',
    alignSelf: 'center',
  },
  container: {
    ...center,
    justifyContent: 'center',
  } as ViewStyle,
});

export default CircleBackground;
