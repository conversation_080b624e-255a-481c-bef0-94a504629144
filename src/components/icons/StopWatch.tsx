import React from 'react';
import { Svg, Path } from 'react-native-svg';
import { SvgProps } from '~/types/svg.types';

const StopWatch = ({ color, size = 20, ...props }: SvgProps) => (
  <Svg width={size} height={size} fill="none" {...props}>
    <Path
      fill={color}
      d="m14.665 6.636.778-.778a.625.625 0 0 0-.884-.884l-.816.815a6.83 6.83 0 0 0-4.16-1.414A6.883 6.883 0 0 0 2.71 11.25a6.883 6.883 0 0 0 6.875 6.875 6.883 6.883 0 0 0 6.875-6.875 6.84 6.84 0 0 0-1.794-4.614M9.584 16.875a5.63 5.63 0 0 1-5.625-5.625 5.63 5.63 0 0 1 5.625-5.625 5.63 5.63 0 0 1 5.625 5.625 5.63 5.63 0 0 1-5.625 5.625M11.625 12a.625.625 0 0 1-.75 1l-1.667-1.25a.63.63 0 0 1-.25-.5V8.333a.625.625 0 0 1 1.25 0v2.605zm-4.75-9.5c0-.345.28-.625.625-.625h4.167a.625.625 0 0 1 0 1.25H7.5a.625.625 0 0 1-.625-.625"
    />
  </Svg>
);

export default StopWatch;
