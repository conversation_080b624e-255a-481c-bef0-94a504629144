import React from 'react';
import Svg, { Path } from 'react-native-svg';

import colors from '~/styles/colors';
import { SvgProps } from '~/types/svg.types';

const Flash = ({ color = colors.white, size = 25, ...props }: SvgProps) => (
  <Svg fill="none" viewBox="0 0 24 25" width={size} height={size} {...props}>
    <Path
      fill={color}
      d="M10 22.21a.75.75 0 0 1-.75-.75v-7.25H5a.75.75 0 0 1-.558-1.252l9-10a.748.748 0 0 1 1.307.502v7.25H19a.75.75 0 0 1 .558 1.252l-9 10a.75.75 0 0 1-.558.248m-3.316-9.5H10a.75.75 0 0 1 .75.75v6.046l6.566-7.296H14a.75.75 0 0 1-.75-.75V5.414z"
    />
  </Svg>
);

export default Flash;
