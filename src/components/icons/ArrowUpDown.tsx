import React from 'react';
import Svg, { Path } from 'react-native-svg';

import colors from '~/styles/colors';
import { SvgProps } from '~/types/svg.types';

const ArrowUpDown = ({
  color = colors.darkBlue500,
  width = 24,
  height = 24,
  ...props
}: SvgProps) => (
  <Svg width={width} height={height} viewBox="0 0 24 24" {...props}>
    <Path
      fill={color}
      d={
        'M21.7073 7.70588C21.5123 7.90088 21.2563 7.99885 21.0003 7.99885C20.7443 7.99885 20.4882 7.90088 20.2932 7.70588L18.0003 5.41291V16.9988C18.0003 17.5508 17.5533 17.9988 17.0003 17.9988C16.4473 17.9988 16.0003 17.5508 16.0003 16.9988V5.41291L13.7073 7.70588C13.3163 8.09688 12.6842 8.09688 12.2933 7.70588C11.9023 7.31488 11.9023 6.68281 12.2933 6.29181L16.2923 2.29279C16.3853 2.19979 16.4952 2.12675 16.6182 2.07575C16.8622 1.97475 17.1384 1.97475 17.3824 2.07575C17.5054 2.12675 17.6153 2.19979 17.7083 2.29279L21.7073 6.29181C22.0983 6.68281 22.0983 7.31488 21.7073 7.70588ZM10.2933 16.2918L8.00028 18.5848V6.99885C8.00028 6.44685 7.55328 5.99885 7.00028 5.99885C6.44728 5.99885 6.00028 6.44685 6.00028 6.99885V18.5848L3.70731 16.2918C3.31631 15.9008 2.68425 15.9008 2.29325 16.2918C1.90225 16.6828 1.90225 17.3149 2.29325 17.7059L6.29227 21.7049C6.38527 21.7979 6.4952 21.8709 6.6182 21.9219C6.7402 21.9729 6.87028 21.9988 7.00028 21.9988C7.13028 21.9988 7.26036 21.9719 7.38236 21.9219C7.50536 21.8709 7.61529 21.7979 7.70829 21.7049L11.7073 17.7059C12.0983 17.3149 12.0983 16.6828 11.7073 16.2918C11.3163 15.9008 10.6842 15.9008 10.2933 16.2918Z'
      }
    />
  </Svg>
);

export default ArrowUpDown;
