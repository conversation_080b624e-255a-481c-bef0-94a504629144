import React from 'react';
import Svg, { Path } from 'react-native-svg';

import colors from '~/styles/colors';
import { SvgProps } from '~/types/svg.types';

const EyeClosed = ({
  color = colors.darkBlue500,
  size = 24,
  ...props
}: SvgProps) => (
  <Svg fill="none" viewBox="0 0 24 25" width={size} height={size} {...props}>
    <Path
      fill={color}
      d="M21.229 14.3678C19.913 16.5638 16.96 20.2499 12 20.2499C11.037 20.2499 10.0861 20.1038 9.17408 19.8148C8.77908 19.6898 8.56105 19.2689 8.68605 18.8739C8.81005 18.4779 9.23496 18.2619 9.62696 18.3849C10.392 18.6269 11.19 18.7499 12 18.7499C16.222 18.7499 18.7901 15.5199 19.9441 13.5939C20.3521 12.9179 20.3521 12.0809 19.9461 11.4069C19.6001 10.8239 19.177 10.2279 18.72 9.67886C18.455 9.35986 18.4991 8.88695 18.8181 8.62295C19.1381 8.35795 19.61 8.40187 19.875 8.71987C20.381 9.32887 20.8509 9.99186 21.2329 10.6369C21.9259 11.7839 21.926 13.2158 21.229 14.3678ZM10.063 15.4979L3.53004 22.0309C3.38404 22.1769 3.19201 22.2509 3.00001 22.2509C2.80801 22.2509 2.61598 22.1779 2.46998 22.0309C2.17698 21.7379 2.17698 21.2629 2.46998 20.9699L5.64503 17.7948C4.31703 16.6498 3.35205 15.3369 2.76905 14.3659C2.07505 13.2159 2.07501 11.784 2.77101 10.633C4.08701 8.43696 7.04001 4.75088 12 4.75088C13.835 4.75088 15.565 5.26981 17.155 6.28481L20.469 2.97085C20.762 2.67785 21.237 2.67785 21.53 2.97085C21.823 3.26385 21.823 3.73888 21.53 4.03188L10.065 15.497C10.065 15.497 10.065 15.4979 10.064 15.4979C10.063 15.4979 10.063 15.4969 10.063 15.4979ZM9.6089 13.831L13.3311 10.1088C12.9291 9.88179 12.478 9.75088 12 9.75088C10.484 9.75088 9.25099 10.9839 9.25099 12.5009C9.25099 12.9779 9.3829 13.429 9.6089 13.831ZM6.70704 16.7318L8.51905 14.9198C8.02405 14.2168 7.75099 13.3819 7.75099 12.4989C7.75099 10.1559 9.65701 8.24892 12 8.24892C12.884 8.24892 13.7179 8.52199 14.4209 9.01699L16.052 7.38589C14.787 6.64489 13.432 6.24795 12 6.24795C7.77801 6.24795 5.20992 9.47795 4.05592 11.404C3.64792 12.08 3.64797 12.917 4.05397 13.591C4.59097 14.488 5.48304 15.7008 6.70704 16.7318ZM14.708 12.935C14.528 14.095 13.594 15.0299 12.436 15.2089C12.027 15.2719 11.7461 15.6549 11.8091 16.0639C11.8671 16.4349 12.1861 16.6999 12.5491 16.6999C12.5871 16.6999 12.6261 16.6968 12.6641 16.6908C14.4911 16.4088 15.908 14.992 16.19 13.164C16.253 12.754 15.973 12.372 15.563 12.308C15.163 12.247 14.771 12.525 14.708 12.935Z"
    />
  </Svg>
);

export default EyeClosed;
