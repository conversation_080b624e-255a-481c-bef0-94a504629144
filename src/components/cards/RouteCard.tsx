import React, { memo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextStyle,
  Pressable,
  ImageBackground,
  ViewStyle,
} from 'react-native';
import colors from '~/styles/colors';
import { h4 } from '~/styles/text';
import CardHeader from '~/components/cards/CardHeader';
import en from '~/localization/en';
import ErrorComponent from '~/components/error/ErrorComponent';
import { horizontalLine, shadowSmall } from '~/styles/views';
import Arrow from '~/components/icons/Arrow';
import LocationPinOutlined from '~/components/icons/LocationPinOutlined';
import { RouteSummary } from '~/types/routes.types';

const currentRouteBg = require('~/assets/images/current-route-default.webp');

interface RouteCardProps {
  current?: boolean;
  route: RouteSummary;
  stepperChipColor: [string, string];
  onItemPress?: (route: RouteSummary) => void;
}

const RouteCard: React.FC<RouteCardProps> = ({
  current = false,
  route,
  stepperChipColor,
  onItemPress,
}) => {
  const handlePress = () => {
    onItemPress?.(route);
  };

  if (!route) {
    return <ErrorComponent errorText={en.invalidData} />;
  }

  const CardContainer = ({ children }: { children: React.ReactNode }) => {
    return (
      <>
        {current ? (
          <View style={shadowSmall} testID="RouteCard.Container">
            <View style={styles.backgroundImgContainer}>
              <ImageBackground source={currentRouteBg}>
                {children}
              </ImageBackground>
            </View>
          </View>
        ) : (
          <View
            style={[shadowSmall, styles.container]}
            testID="RouteCard.Container">
            {children}
          </View>
        )}
      </>
    );
  };

  const bodyTextStyle = {
    ...h4,
    color: current ? colors.white : colors.grey900,
    fontSize: 16,
  } as TextStyle;

  const headerContainerStyle = {
    ...styles.headerContainer,
    paddingBottom: current ? 32 : 0,
    backgroundColor: current ? colors.transBlack60 : colors.white,
  } as ViewStyle;

  const detailContainerStyle = {
    ...styles.detailContainer,
    backgroundColor: current ? colors.transBlack80 : colors.white,
  } as ViewStyle;

  const textColor = () => {
    if (current) {
      return colors.white;
    }

    return colors.grey900;
  };

  const detailTextStyle = {
    ...h4,
    color: textColor(),
    fontSize: 16,
  } as TextStyle;

  return (
    <Pressable onPress={handlePress}>
      <CardContainer>
        <View style={headerContainerStyle}>
          <CardHeader
            isActive={current}
            textColor={current ? colors.white : colors.textBlack}
            name={route.Name}
            status={route.Status__c}
            stepperChipColor={stepperChipColor}
          />
        </View>

        <View style={detailContainerStyle}>
          <View style={styles.infoView}>
            <LocationPinOutlined
              color={current ? colors.white : colors.darkBlue500}
            />

            <Text style={bodyTextStyle}>{`${en.route}`}</Text>
          </View>

          <View style={horizontalLine} />

          <View style={styles.footerView}>
            <Text style={detailTextStyle}>{en.see_route_details}</Text>

            <Arrow color={current ? colors.white : colors.darkBlue500} />
          </View>
        </View>
      </CardContainer>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  backgroundImgContainer: {
    overflow: 'hidden',
    borderRadius: 16,
  },
  infoView: {
    gap: 4,
    paddingVertical: 4,
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerContainer: {
    padding: 16,
    backgroundColor: colors.transBlack50,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
  },
  detailContainer: {
    padding: 16,
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 16,
  },
  infoText: {
    ...h4,
    color: colors.darkGray,
    fontSize: 16,
  } as TextStyle,
  verticalLine: {
    backgroundColor: colors.transGray70,
    width: 1.5,
    height: 20,
    marginHorizontal: 8,
  },
  detailText: {
    ...h4,
    color: colors.grey900,
    fontSize: 16,
  } as TextStyle,
  container: {
    borderRadius: 16,
  },
  footerView: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 4,
  },
});

const MemoisedRouteCard = memo(RouteCard, (prevProps, nextProps) => {
  const isSameRoute =
    prevProps.route.Id === nextProps.route.Id &&
    prevProps.current === nextProps.current &&
    prevProps.route.Name === nextProps.route.Name &&
    prevProps.route.Status__c === nextProps.route.Status__c &&
    prevProps.route.Number_of_Stops__c === nextProps.route.Number_of_Stops__c &&
    prevProps.route.Planned_Start__c === nextProps.route.Planned_Start__c;
  return isSameRoute;
});

export default MemoisedRouteCard;
