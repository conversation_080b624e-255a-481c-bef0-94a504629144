import React from 'react';
import { View, Text, TextStyle, StyleSheet } from 'react-native';
import { h5 } from '~/styles/text';
import { cardView, shadowSmall } from '~/styles/views';

interface CardWrapperProps {
  id?: string;
  children: React.ReactNode;
  title?: string;
  withShadow?: boolean;
}

const CardWrapper: React.FC<CardWrapperProps> = ({
  children,
  title,
  id,
  withShadow,
}) => {
  return (
    <View
      id={`${id}.Container`}
      style={[styles.container, withShadow && shadowSmall]}
      testID={`${id}.Container`}>
      {title && (
        <Text style={h5 as TextStyle} testID={`${id}.Label`}>
          {title}
        </Text>
      )}
      {children}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    gap: 10,
    ...cardView,
  },
});

export default CardWrapper;
