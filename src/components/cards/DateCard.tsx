import React, { memo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextStyle,
  Pressable,
  ViewStyle,
} from 'react-native';
import colors from '~/styles/colors';
import { h4 } from '~/styles/text';
import CardHeader from '~/components/cards/CardHeader';
import en from '~/localization/en';
import { horizontalLine, shadowSmall } from '~/styles/views';
import { Arrow, Calendar } from '~/components/icons';

interface DateCardProps {
  date: string;
  onItemPress?: (date: string) => void;
}

const DateCard: React.FC<DateCardProps> = ({ date, onItemPress }) => {
  const handlePress = () => {
    onItemPress?.(date);
  };

  const headerContainerStyle = {
    ...styles.headerContainer,
    backgroundColor: colors.white,
  } as ViewStyle;

  const detailContainerStyle = {
    ...styles.detailContainer,
    backgroundColor: colors.white,
  } as ViewStyle;

  const detailTextStyle = {
    ...h4,
    color: colors.grey900,
    fontSize: 16,
  } as TextStyle;

  return (
    <Pressable onPress={handlePress}>
      <View style={[shadowSmall, styles.container]} testID="DateCard.Container">
        <View style={headerContainerStyle}>
          <CardHeader
            leftIcon={<Calendar color={colors.darkBlue500} />}
            textColor={colors.textBlack}
            name={date}
          />
        </View>

        <View style={detailContainerStyle}>
          <View style={horizontalLine} />

          <View style={styles.footerView}>
            <Text style={detailTextStyle}>{en.see_schedule}</Text>
            <Arrow color={colors.darkBlue500} />
          </View>
        </View>
      </View>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  headerContainer: {
    paddingHorizontal: 16,
    paddingTop: 16,
    backgroundColor: colors.transBlack50,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
  },
  detailContainer: {
    padding: 16,
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 16,
  },
  detailText: {
    ...h4,
    color: colors.grey900,
    fontSize: 16,
  } as TextStyle,
  container: {
    borderRadius: 16,
  },
  footerView: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 4,
  },
});

const MemoisedDateCard = memo(DateCard, (prevProps, nextProps) => {
  const isSameDate = prevProps.date === nextProps.date;
  return isSameDate;
});

export default MemoisedDateCard;
