import React from 'react';
import { View, Text, StyleSheet, TextStyle } from 'react-native';
import { h4, h5 } from '~/styles/text';
import colors from '~/styles/colors';
import { marginTop8, container } from '~/styles/views';
import CardWrapper from '~/components/cards/CardWrapper';

interface StopInfoItemCardProps {
  icon?: React.ReactNode;
  title: string;
  value: string;
}

const StopInfoItemCard: React.FC<StopInfoItemCardProps> = ({
  icon = null,
  title,
  value,
}) => {
  return (
    <CardWrapper>
      <Text style={styles.infoLabel}>{title}</Text>

      <View style={styles.infoView}>
        <View style={marginTop8}>{icon}</View>
        <Text style={[styles.infoText, container]}>{value}</Text>
      </View>
    </CardWrapper>
  );
};

const styles = StyleSheet.create({
  infoView: {
    flexDirection: 'row',
    flex: 1,
    gap: 4,
  },
  infoLabel: {
    ...h5,
    color: colors.grey900,
  } as TextStyle,
  infoText: {
    ...h4,
    ...marginTop8,
    color: colors.grey800,
    fontSize: 16,
  } as TextStyle,
});

export default StopInfoItemCard;
