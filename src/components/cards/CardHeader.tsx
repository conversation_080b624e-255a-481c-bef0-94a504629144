import React from 'react';
import { View, Text, StyleSheet, TextStyle, ViewStyle } from 'react-native';
import { h3, h4 } from '~/styles/text';
import StepperChip from '~/components/chip/StepperChip';
import colors from '~/styles/colors';
import { row } from '~/styles/views';
import { StopType } from '~/types/stops.types';

interface CardHeaderProps {
  leftIcon?: React.ReactNode;
  stepperChipColor?: [string, string];
  status?: string;
  name: string;
  textColor?: string;
  isActive?: boolean;
}

const CardHeader: React.FC<CardHeaderProps> = ({
  leftIcon = null,
  stepperChipColor = null,
  status = null,
  name,
  textColor = colors.grey900,
  isActive = true,
}) => {
  const getTextStyle = () => ({
    ...(isActive ? h3 : h4),
    color: textColor,
    fontWeight: '500',
  });

  return (
    <View style={styles.container}>
      {leftIcon && <View>{leftIcon}</View>}

      <Text
        numberOfLines={2}
        ellipsizeMode="tail"
        style={[styles.cardTitle, getTextStyle() as TextStyle]}>
        {name}
      </Text>

      {Boolean(stepperChipColor) && Boolean(status) && (
        <StepperChip
          stepperChipColor={stepperChipColor}
          status={status as StopType}
          chipBorderColor={stepperChipColor?.[0]}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    ...row,
    gap: 8,
  } as ViewStyle,
  cardTitle: {
    flex: 1,
    fontSize: 20,
  } as TextStyle,
});

export default CardHeader;
