import React, { memo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextStyle,
  Pressable,
  ImageBackground,
  ViewStyle,
} from 'react-native';
import colors from '~/styles/colors';
import { h4 } from '~/styles/text';
import CardHeader from '~/components/cards/CardHeader';
import en from '~/localization/en';
import ErrorComponent from '~/components/error/ErrorComponent';
import { getFormattedTime } from '~/utils/dateAndTime';
import { container, horizontalLine, shadowSmall } from '~/styles/views';
import Arrow from '~/components/icons/Arrow';
import Stopwatch from '~/components/icons/StopWatch';
import { StopWithRelations } from '~/types/stops.types';

const currentRouteBg = require('~/assets/images/current-route-default.webp');

interface StopCardProps {
  current?: boolean;
  stop: StopWithRelations;
  stepperChipColor: [string, string];
  onItemPress?: (stop: StopWithRelations) => void;
}

const StopCard: React.FC<StopCardProps> = ({
  current = false,
  stop,
  stepperChipColor,
  onItemPress,
}) => {
  const handlePress = () => {
    onItemPress?.(stop);
  };

  if (!stop) {
    return <ErrorComponent errorText={en.invalidData} />;
  }

  const CardContainer = ({ children }: { children: React.ReactNode }) => {
    return (
      <>
        {current ? (
          <View style={shadowSmall} testID="StopCard.Container">
            <View style={styles.backgroundImgContainer}>
              <ImageBackground source={currentRouteBg}>
                {children}
              </ImageBackground>
            </View>
          </View>
        ) : (
          <View
            style={[shadowSmall, styles.container]}
            testID="StopCard.Container">
            {children}
          </View>
        )}
      </>
    );
  };

  const bodyTextStyle = {
    ...h4,
    color: current ? colors.white : colors.grey900,
    fontSize: 16,
  } as TextStyle;

  const headerContainerStyle = {
    ...styles.headerContainer,
    paddingBottom: current ? 32 : 0,
    backgroundColor: current ? colors.transBlack60 : colors.white,
  } as ViewStyle;

  const detailContainerStyle = {
    ...styles.detailContainer,
    backgroundColor: current ? colors.transBlack80 : colors.white,
  } as ViewStyle;

  const textColor = () => {
    if (current) {
      return colors.white;
    }

    return colors.grey900;
  };

  const detailTextStyle = {
    ...h4,
    color: textColor(),
    fontSize: 16,
  } as TextStyle;

  return (
    <Pressable onPress={handlePress}>
      <CardContainer>
        <View style={headerContainerStyle}>
          <CardHeader
            isActive={current}
            textColor={current ? colors.white : colors.textBlack}
            name={stop.Name}
            status={stop.Status__c}
            stepperChipColor={stepperChipColor}
          />
        </View>

        <View style={detailContainerStyle}>
          <View style={styles.infoView}>
            <Stopwatch color={current ? colors.white : colors.darkBlue500} />

            <View style={container}>
              {stop.Stop_Time_Preferred__c !== null ? (
                <Text numberOfLines={1} style={bodyTextStyle}>
                  {getFormattedTime(stop.Stop_Time_Preferred__c)}
                </Text>
              ) : (
                <Text numberOfLines={1} style={bodyTextStyle}>
                  {en.no_start_time_set}
                </Text>
              )}
            </View>
          </View>

          <View style={horizontalLine} />

          <View style={styles.footerView}>
            <Text style={detailTextStyle}>{en.see_stop_details}</Text>
            <Arrow color={current ? colors.white : colors.darkBlue500} />
          </View>
        </View>
      </CardContainer>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  backgroundImgContainer: {
    overflow: 'hidden',
    borderRadius: 16,
  },
  infoView: {
    gap: 4,
    paddingVertical: 4,
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerContainer: {
    padding: 16,
    backgroundColor: colors.transBlack50,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
  },
  detailContainer: {
    padding: 16,
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 16,
  },
  infoText: {
    ...h4,
    color: colors.darkGray,
    fontSize: 16,
  } as TextStyle,
  verticalLine: {
    backgroundColor: colors.transGray70,
    width: 1.5,
    height: 20,
    marginHorizontal: 8,
  },
  detailText: {
    ...h4,
    color: colors.grey900,
    fontSize: 16,
  } as TextStyle,
  container: {
    borderRadius: 16,
  },
  footerView: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 4,
  },
});

const MemoisedStopCard = memo(StopCard, (prevProps, nextProps) => {
  const isSameRoute =
    prevProps.stop.Id === nextProps.stop.Id &&
    prevProps.current === nextProps.current &&
    prevProps.stop.Name === nextProps.stop.Name &&
    prevProps.stop.Status__c === nextProps.stop.Status__c &&
    prevProps.stop.Stop_Time_Preferred__c ===
      nextProps.stop.Stop_Time_Preferred__c;
  return isSameRoute;
});

export default MemoisedStopCard;
