import React, { useEffect } from 'react';
import { View } from 'react-native';
import Checkbox from '~/components/checkbox/Checkbox';
import { container } from '~/styles/views';
import colors from '~/styles/colors';

interface CheckboxGroupProps {
  options: { label: string; value: string }[];
  selectedOptionValues?: string[];
  optionBackgroundColor?: string;
  onChecked?: (selectedOptionValues: string[]) => void;
  onAllChecked?: (allChecked: boolean) => void;
}

const CheckboxGroup: React.FC<CheckboxGroupProps> = ({
  options,
  selectedOptionValues = [],
  onChecked,
  onAllChecked,
  optionBackgroundColor = colors.lightGrayishBlue,
}) => {
  useEffect(() => {
    const allChecked = selectedOptionValues.length === options.length;
    onAllChecked?.(allChecked);
  }, [selectedOptionValues, options, onAllChecked]);

  const handleChange = (value: string, checked: boolean) => {
    if (checked) {
      onChecked?.([...selectedOptionValues, value]);
    } else {
      onChecked?.(selectedOptionValues.filter(v => v !== value));
    }
  };

  return (
    <View testID="CheckboxGroup.Container" style={container}>
      {options.map(option => {
        const isChecked = selectedOptionValues.includes(option.value);

        return (
          <Checkbox
            key={option.value}
            label={option.label}
            backgroundColor={optionBackgroundColor}
            checked={isChecked}
            onChange={checked => handleChange(option.value, checked)}
            testID={`CheckboxGroup.Checkbox.${option.value}`}
          />
        );
      })}
    </View>
  );
};

export default CheckboxGroup;
