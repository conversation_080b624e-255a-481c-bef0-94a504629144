import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ViewStyle,
  TextStyle,
} from 'react-native';
import Checkmark from '~/components/icons/Checkmark';
import colors from '~/styles/colors';
import { cardView, center, rowViewFillSpace } from '~/styles/views';
import { iconView } from '~/styles/icons';
import { h5 } from '~/styles/text';

interface CheckboxProps {
  label: string;
  checked: boolean;
  backgroundColor?: string;
  onChange: (checked: boolean) => void;
  testID: string;
}

const Checkbox: React.FC<CheckboxProps> = ({
  label,
  checked,
  backgroundColor = colors.white,
  onChange,
  testID,
}) => {
  return (
    <TouchableOpacity
      style={[styles.container, { backgroundColor }]}
      onPress={() => onChange(!checked)}
      testID={`${testID}.Container`}>
      <Text
        style={[h5 as TextStyle, styles.labelStyle]}
        testID={`${testID}.Label`}>
        {label}
      </Text>
      <View
        style={[
          styles.checkbox,
          center as ViewStyle,
          checked && styles.checked,
        ]}
        testID={`${testID}.Checkbox`}>
        {checked && (
          <Checkmark color="white" testID={`${testID}.Icon.checkmark`} />
        )}
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    ...cardView,
    ...rowViewFillSpace,
    gap: 10,
  } as ViewStyle,
  checkbox: {
    width: iconView.width,
    height: iconView.width,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: colors.lightGray,
    backgroundColor: colors.white,
  },
  labelStyle: {
    flex: 1,
  },
  checked: {
    backgroundColor: colors.darkBlue500,
    borderColor: colors.darkBlue500,
  },
});

export default Checkbox;
