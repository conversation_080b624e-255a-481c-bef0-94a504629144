import React, { useCallback, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextStyle,
  LayoutChangeEvent,
} from 'react-native';
import { h4 } from '~/styles/text';
import { CircleCheck } from '~/assets/icons';
import CardHeader from '~/components/cards/CardHeader';
import { Stop } from '~/types/stops.types';
import { RouteStatus } from '~/types/routes.types';
import colors from '~/styles/colors';
import en from '~/localization/en';
import ErrorComponent from '~/components/error/ErrorComponent';
import { getFormattedTime } from '~/utils/dateAndTime';
import { horizontalLine, marginTop8 } from '~/styles/views';
import { STOP_STATUS } from '~/utils/constants';

import {
  StopWatch,
  Notes,
  LocationPinOutlined,
  DashedVerticalLine,
} from '~/components/icons';

interface StopCardProps {
  stop: Stop;
  lastCompletedStopIndex: number;
  itemIndex: number;
  routeStatus: RouteStatus;
}

interface StyleConfig {
  titleTextColor: string;
  infoTextColor: string;
  locationIcon: any;
  timeIcon: any;
  stepperChipColor: [string, string];
  status: string;
  stepperLine: any;
  isActive: boolean;
}

const StopCard: React.FC<StopCardProps> = ({
  stop,
  lastCompletedStopIndex,
  itemIndex,
  routeStatus,
}) => {
  const [cardHeight, setCardHeight] = useState(0);
  const defaultCircleStyle = styles.circle;
  const notesArePresent = stop.Notes__c !== '' && stop.Notes__c !== null;
  const routeIsScheduled = routeStatus === 'Scheduled';
  const scheduledCircleStyle = [styles.circle, styles.hollowCircle];
  const stopIsNextToVisit = itemIndex === lastCompletedStopIndex + 1;
  const stopType = stop.Type__c ?? 'Pickup';
  const verticalLineHeight =
    cardHeight - (2 * styles.circle.height + styles.stepperLineView.gap / 2);

  const handleDetailContainerLayout = useCallback(
    (event: LayoutChangeEvent) => {
      const { height } = event.nativeEvent.layout;
      if (cardHeight === 0) {
        setCardHeight(height);
      }
    },
    [cardHeight],
  );

  const stepperHeader = () => {
    const stopIsCompletedForStepper =
      !routeIsScheduled && itemIndex <= lastCompletedStopIndex;

    if (stopIsCompletedForStepper) {
      return <CircleCheck />;
    }

    if (stopIsNextToVisit) {
      return (
        <View
          style={routeIsScheduled ? scheduledCircleStyle : defaultCircleStyle}
        />
      );
    }

    return (
      <View
        style={
          routeIsScheduled
            ? scheduledCircleStyle
            : [defaultCircleStyle, styles.hollowCircle]
        }
      />
    );
  };

  const getCardStyle = (): StyleConfig => {
    const stopIsCompletedForCardStyle = itemIndex <= lastCompletedStopIndex;

    if (!routeIsScheduled && stopIsCompletedForCardStyle) {
      return {
        titleTextColor: colors.buttonGray,
        infoTextColor: colors.buttonGray,
        locationIcon: <LocationPinOutlined color={colors.buttonGray} />,
        timeIcon: <StopWatch color={colors.buttonGray} />,
        stepperChipColor: [colors.greenLight, colors.greenDark],
        status: STOP_STATUS.COMPLETE,
        stepperLine: <View style={styles.stepperSolidLineGreen} />,
        isActive: false,
      };
    }

    if (!routeIsScheduled && stopIsNextToVisit) {
      return {
        titleTextColor: colors.grey900,
        infoTextColor: colors.grey900,
        locationIcon: <LocationPinOutlined color={colors.darkBlue500} />,
        timeIcon: <StopWatch color={colors.darkBlue500} />,
        stepperChipColor: [colors.blue50, colors.darkBlue500],
        status: stopType,
        stepperLine: <View style={styles.stepperSolidLine} />,
        isActive: true,
      };
    }

    return {
      titleTextColor: colors.grey900,
      infoTextColor: colors.buttonGray,
      locationIcon: <LocationPinOutlined color={colors.buttonGray} />,
      timeIcon: <StopWatch color={colors.buttonGray} />,
      stepperChipColor: [colors.backgroundLight, colors.darkGray],
      status: stopType,
      stepperLine: (
        <DashedVerticalLine
          color={colors.buttonGray}
          width={1.5}
          height={verticalLineHeight}
        />
      ),
      isActive: false,
    };
  };

  if (!stop) {
    return <ErrorComponent errorText={en.invalidData} />;
  }

  const {
    titleTextColor,
    infoTextColor,
    locationIcon,
    timeIcon,
    stepperChipColor,
    status,
    stepperLine,
    isActive,
  } = getCardStyle();

  const renderStepperLineView = () => {
    return (
      <View style={styles.stepperLineView}>
        {stepperHeader()}
        {stepperLine}
      </View>
    );
  };

  const shouldShowNotes = () => {
    return isActive && notesArePresent;
  };

  return (
    <View style={styles.container}>
      <View style={styles.rowView}>
        {renderStepperLineView()}

        <View
          style={styles.detailContainer}
          onLayout={handleDetailContainerLayout}>
          <CardHeader
            stepperChipColor={stepperChipColor}
            status={status}
            name={stop.Name}
            textColor={titleTextColor}
            isActive={isActive}
          />
          <View style={horizontalLine} />
          <View style={styles.infoView}>
            <View style={[styles.rowView, marginTop8]}>
              {locationIcon}

              <Text style={[styles.infoText, { color: infoTextColor }]}>
                {stop.Address_1__c}
              </Text>
            </View>

            <View style={[styles.rowView, marginTop8]}>
              {timeIcon}

              <Text style={[styles.infoText, { color: infoTextColor }]}>
                {getFormattedTime(stop.Stop_Time_Preferred__c)}
              </Text>
            </View>

            {shouldShowNotes() && (
              <View style={[styles.rowView, marginTop8]}>
                <Notes />

                <Text style={[styles.infoText, { color: infoTextColor }]}>
                  {stop.Notes__c}
                </Text>
              </View>
            )}
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingVertical: 16,
    justifyContent: 'center',
    flex: 1,
  },
  infoView: {
    flexDirection: 'column',
    gap: 8,
  },
  infoText: {
    ...h4,
    color: colors.grey900,
    fontSize: 16,
    flex: 1,
  } as TextStyle,
  detailContainer: {
    flex: 1,
  },
  rowView: {
    flexDirection: 'row',
    gap: 8,
  },
  stepperLineView: {
    width: 24,
    alignItems: 'center',
    gap: 8,
    marginTop: 8,
  },
  stepperSolidLine: {
    backgroundColor: colors.darkBlue500,
    width: 1.5,
    flex: 1,
  },
  stepperSolidLineGreen: {
    backgroundColor: colors.greenDark,
    width: 1.5,
    flex: 1,
  },
  circle: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: colors.darkBlue500,
  },
  hollowCircle: {
    borderWidth: 1,
    backgroundColor: 'transparent',
    borderColor: colors.buttonGray,
  },
});

export default StopCard;
