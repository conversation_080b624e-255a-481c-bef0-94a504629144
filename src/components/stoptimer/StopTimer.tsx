import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, TextStyle } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import StopWatch from '~/components/icons/StopWatch';
import colors from '~/styles/colors';
import { useAppState } from '~/hooks/useAppState';
import { useTimer } from '~/hooks/useTimer';
import { deviceWidth } from '~/styles/views';
import { iconView } from '~/styles/icons';
import { grayishText, h3 } from '~/styles/text';
import { formatTimeRemaining } from '~/utils/dateAndTime';
import CircularDashes from '~/components/stoptimer/CircularDashes';

interface StopTimerProps {
  durationInMinutes?: number;
  strokeColor?: string;
  id: string;
  onComplete?: () => void;
}

const SIZE = deviceWidth * 0.8;
const DASH_WIDTH = 2;
const DASH_HEIGHT = 3;
const TOTAL_DASHES = 150;

const StopTimer: React.FC<StopTimerProps> = ({
  durationInMinutes = 10,
  strokeColor = colors.darkBlue600,
  onComplete,
  id,
}) => {
  const TIMER_START_KEY = `timerStart_${id}`;
  const TIMER_COMPLETED_KEY = `timerCompleted_${id}`;
  const totalSeconds = durationInMinutes * 60;
  const [remainingSeconds, setRemainingSeconds] =
    useState<number>(totalSeconds);
  const [timerActive, setTimerActive] = useState<boolean>(true);

  useEffect(() => {
    let isMounted = true;
    const initializeTimer = async () => {
      const completed = await AsyncStorage.getItem(TIMER_COMPLETED_KEY);
      if (completed === 'true') {
        setRemainingSeconds(0);
        setTimerActive(false);
        return;
      }

      const storedStartTime = await AsyncStorage.getItem(TIMER_START_KEY);
      if (storedStartTime) {
        const elapsed = Math.floor(
          (Date.now() - Number(storedStartTime)) / 1000,
        );
        if (elapsed >= totalSeconds) {
          await AsyncStorage.removeItem(TIMER_START_KEY);
          await AsyncStorage.setItem(TIMER_COMPLETED_KEY, 'true');
          if (isMounted) {
            setRemainingSeconds(0);
            setTimerActive(false);
          }
        } else if (isMounted) {
          setRemainingSeconds(Math.max(0, totalSeconds - elapsed));
        }
      } else {
        await AsyncStorage.setItem(TIMER_START_KEY, Date.now().toString());
        if (isMounted) setRemainingSeconds(totalSeconds);
      }
    };
    initializeTimer();

    return () => {
      isMounted = false;
    };

    //eslint-disable-next-line react-hooks/exhaustive-deps
  }, [durationInMinutes, totalSeconds]);

  // Handle app state changes (foreground/background)
  useAppState({
    onForeground: async () => {
      const storedStartTime = await AsyncStorage.getItem(TIMER_START_KEY);
      if (storedStartTime) {
        const elapsed = Math.floor(
          (Date.now() - Number(storedStartTime)) / 1000,
        );
        setRemainingSeconds(Math.max(0, totalSeconds - elapsed));
      }
    },
  });

  useEffect(() => {
    if (remainingSeconds === 0) {
      AsyncStorage.removeItem(TIMER_START_KEY);
      AsyncStorage.setItem(TIMER_COMPLETED_KEY, 'true');

      if (onComplete) {
        onComplete();
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [remainingSeconds]);

  // Use useTimer hook
  useTimer({
    isRunning: timerActive && remainingSeconds > 0,
    onTick: () => {
      setRemainingSeconds(prev => (prev <= 1 ? 0 : prev - 1));
    },
  });

  const filledDashes = Math.round(
    ((totalSeconds - remainingSeconds) / totalSeconds) * TOTAL_DASHES,
  );

  return (
    <View style={styles.container} id={`${id}.View.container`}>
      <View style={styles.outerBackground} id={`${id}.View.outerBackground`} />
      <View style={styles.whiteBackground} id={`${id}.View.whiteBackground`} />
      <CircularDashes
        filledDashes={filledDashes}
        componentId={id}
        progressColor={strokeColor}
      />
      <View style={styles.stopIconContainer} id={`${id}.View.stopIcon`}>
        <StopWatch color={colors.darkBlue900} />
      </View>
      <Text
        style={[h3 as TextStyle, styles.timerText]}
        id={`${id}.Text.timerText`}
        testID={`${id}.Text.timerText`}>
        {formatTimeRemaining(remainingSeconds)}
      </Text>
      <Text
        style={[grayishText as TextStyle, styles.remainingText]}
        id={`${id}.Text.remainingText`}
        testID={`${id}.Text.remainingText`}>
        Remaining
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: SIZE,
    height: SIZE,
    justifyContent: 'center',
    alignItems: 'center',
  },
  outerBackground: {
    width: SIZE,
    height: SIZE,
    backgroundColor: colors.transGray20,
    borderRadius: SIZE / 2,
    position: 'absolute',
  },
  whiteBackground: {
    width: SIZE * 0.95,
    height: SIZE * 0.95,
    backgroundColor: 'white',
    borderRadius: SIZE / 2,
    position: 'absolute',
  },
  circle: {
    width: SIZE * 0.9,
    height: SIZE * 0.9,
    position: 'absolute',
  },
  dash: {
    position: 'absolute',
    width: DASH_WIDTH,
    height: DASH_HEIGHT,
  },
  stopIconContainer: {
    position: 'absolute',
    top: SIZE * 0.3,
    backgroundColor: colors.lightBlue50,
    borderRadius: 20,
    width: iconView.height * 1.8,
    height: iconView.height * 1.8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  timerText: {
    paddingTop: SIZE * 0.1,
    fontSize: SIZE * 0.16,
    color: colors.black,
  },
  remainingText: {
    position: 'absolute',
    top: SIZE / 1.5,
  },
});

export default StopTimer;
