import React from 'react';
import { View, StyleSheet } from 'react-native';
import colors from '~/styles/colors';
import { deviceWidth } from '~/styles/views';

interface CircularDashesProps {
  filledDashes: number;
  componentId: string;
  progressColor: string;
}

const SIZE = 0.8 * deviceWidth;
const DASH_WIDTH = 2;
const DASH_HEIGHT = 3;
const TOTAL_DASHES = 150;

const calculateDashPosition = (index: number) => {
  const angle = (index / TOTAL_DASHES) * 2 * Math.PI - Math.PI / 2;
  const x = Math.cos(angle) * ((SIZE * 0.9) / 2);
  const y = Math.sin(angle) * ((SIZE * 0.9) / 2);
  const rotation = (angle * 180) / Math.PI;

  return {
    left: (SIZE * 0.9) / 2 + x - DASH_WIDTH / 2,
    top: (SIZE * 0.9) / 2 + y - DASH_HEIGHT / 2,
    transform: [{ rotate: `${rotation}deg` }],
    uniqueKey: `dash-${angle.toFixed(4)}`,
  };
};

const CircularDashes: React.FC<CircularDashesProps> = ({
  filledDashes,
  componentId,
  progressColor,
}) => (
  <View
    style={styles.circle}
    id={`${componentId}.View.circle`}
    testID={`${componentId}.View.circle`}>
    {Array.from({ length: TOTAL_DASHES }, (_, index) => {
      const { left, top, transform, uniqueKey } = calculateDashPosition(index);

      return (
        <View
          key={uniqueKey}
          id={`${componentId}.View.${uniqueKey}`}
          style={[
            styles.dash,
            {
              left,
              top,
              transform,
              backgroundColor:
                index < filledDashes ? progressColor : colors.lightGray,
            },
          ]}
        />
      );
    })}
  </View>
);

const styles = StyleSheet.create({
  circle: {
    width: SIZE * 0.9,
    height: SIZE * 0.9,
    position: 'absolute',
  },
  dash: {
    position: 'absolute',
    width: DASH_WIDTH,
    height: DASH_HEIGHT,
  },
});

export default CircularDashes;
