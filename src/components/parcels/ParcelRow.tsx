import React, { useState } from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { Pen, Trash, BarCodeOutlined, Checkmark } from '~/components/icons';
import { Parcel } from '~/types/parcel.types';
import colors from '~/styles/colors';
import { blackText } from '~/styles/text';
import en from '~/localization/en';
import CustomModal from '~/components/modals/CustomModal';
import { DustbinWithBackground, ScanCamera } from '~/assets/icons';
import { StopType } from '~/types/stops.types';

interface ParcelRowProps {
  parcel: Parcel;
  isEditable: boolean;
  onEditClick: () => void;
  onDelete: (parcelId: string) => void;
  canBeDeleted: boolean;
  shouldBeScanned: boolean;
  shouldShowCheckmark?: boolean;
  onScanPress?: () => void;
  stopType: StopType;
}

const ParcelRow = ({
  parcel,
  isEditable,
  onEditClick,
  onDelete,
  canBeDeleted,
  shouldBeScanned = false,
  shouldShowCheckmark = false,
  onScanPress,
  stopType,
}: ParcelRowProps) => {
  const [isDeleteModalVisible, setIsDeleteModalVisible] = useState(false);

  const handleDelete = async () => {
    onDelete(parcel.Id);
    setIsDeleteModalVisible(false);
  };

  const getDisplayQuantity = (): number => {
    const displayQuantity =
      stopType === StopType.Delivery
        ? parcel.Dropoff_Quantity__c != null && parcel.Dropoff_Quantity__c !== 0
          ? parcel.Dropoff_Quantity__c
          : parcel.Quantity__c != null && parcel.Quantity__c !== 0
            ? parcel.Quantity__c
            : parcel.Quantity_Expected__c
        : (parcel.Quantity__c ?? parcel.Quantity_Expected__c);
    return displayQuantity;
  };

  const renderScanButton = () => {
    if (!onScanPress) return null;

    const IconComponent = parcel.Barcode_Required__c
      ? BarCodeOutlined
      : ScanCamera;

    return (
      <TouchableOpacity onPress={onScanPress} style={styles.iconButton}>
        <IconComponent />
      </TouchableOpacity>
    );
  };

  const renderCheckmark = () => (
    <View style={styles.iconButton}>
      <Checkmark />
    </View>
  );

  const renderActionButtons = () => {
    const isFromWorkOrder = parcel.Work_Order__c !== null;

    // Show scan button if parcel should be scanned or is from work order
    if (shouldBeScanned || isFromWorkOrder) {
      return onScanPress ? renderScanButton() : renderCheckmark();
    }

    // Show edit/delete buttons for manual parcels
    return (
      <>
        {shouldShowCheckmark && renderCheckmark()}
        {isEditable && (
          <TouchableOpacity onPress={onEditClick} style={styles.iconButton}>
            <Pen />
          </TouchableOpacity>
        )}
        {canBeDeleted && (
          <TouchableOpacity
            onPress={() => setIsDeleteModalVisible(true)}
            style={styles.iconButton}>
            <Trash />
          </TouchableOpacity>
        )}
      </>
    );
  };

  const displayQuantity = getDisplayQuantity();
  const parcelTypeName = parcel.Parcel_Type_Name__c || '';

  return (
    <View style={styles.container}>
      <View style={styles.row}>
        <Text style={styles.text}>
          <Text style={styles.quantity}>{displayQuantity}</Text>{' '}
          {parcelTypeName}
        </Text>
        {renderActionButtons()}
      </View>

      <CustomModal
        testID="delete-parcel-modal"
        isVisible={isDeleteModalVisible}
        headerIcon={<DustbinWithBackground />}
        onClose={() => setIsDeleteModalVisible(false)}
        headerText={en.delete_parcel
          .replace('{quantity}', String(displayQuantity))
          .replace('{type}', parcelTypeName)}
        descriptionText={en.delete_parcel_description}
        okButtonText={en.delete}
        cancelButtonText={en.cancel}
        onOkPress={handleDelete}
        onCancelPress={() => setIsDeleteModalVisible(false)}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 10,
    backgroundColor: colors.lightGrayishBlue,
    borderRadius: 10,
    marginVertical: 4,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  text: {
    fontSize: 16,
    ...blackText,
    flex: 1,
  },
  quantity: {
    fontWeight: '500',
  },
  iconButton: {
    marginLeft: 12,
  },
});

export default ParcelRow;
