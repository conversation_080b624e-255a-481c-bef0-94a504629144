import React, {
  createContext,
  useContext,
  useState,
  useRef,
  ReactNode,
} from 'react';
import ToastMessage from '~/components/toast/ToastMessage';

type ToastType = 'success' | 'error' | 'warning';

interface ToastMessageProps {
  type: ToastType;
  message: string;
  linkText?: string;
  variant?: 'toast' | 'banner';
  onPress?: () => void;
  duration?: number;
}

interface ToastContextType {
  showToast: (props: ToastMessageProps) => void;
  hideToast: () => void;
}

const ToastContext = createContext<ToastContextType | undefined>(undefined);

interface ToastProviderProps {
  children: ReactNode;
}

export const ToastProvider: React.FC<ToastProviderProps> = ({ children }) => {
  const [toast, setToast] = useState<ToastMessageProps | null>(null);
  const toastTimeout = useRef<NodeJS.Timeout | null>(null);

  const hideToast = () => {
    if (toastTimeout.current) {
      clearTimeout(toastTimeout.current);
      toastTimeout.current = null;
    }
    setToast(null);
  };

  const showToast = (props: ToastMessageProps) => {
    hideToast();
    setToast(props);
    toastTimeout.current = setTimeout(
      () => setToast(null),
      props.duration ?? 3000,
    );
  };

  return (
    <ToastContext.Provider value={{ showToast, hideToast }}>
      {children}
      {toast && (
        <ToastMessage
          {...toast}
          variant={toast.variant}
          onPress={toast.onPress}
        />
      )}
    </ToastContext.Provider>
  );
};

export const useToast = (): ToastContextType => {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
};
