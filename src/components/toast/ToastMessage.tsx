import React, { useEffect, useRef } from 'react';
import {
  Animated,
  Text,
  StyleSheet,
  ViewStyle,
  TouchableOpacity,
  View,
  TextStyle,
} from 'react-native';
import colors from '~/styles/colors';
import { fontSize16, small } from '~/styles/text';
import { useToast } from '~/components/toast/ToastProvider';
import { CheckCircle, Cross, WarningFilled } from '~/components/icons';
import { iconView } from '~/styles/icons';
import { ToastType } from '~/types/toast.types';
import { shadowView } from '~/styles/views';

export interface ToastMessageProps {
  type: ToastType;
  title?: string;
  message: string;
  variant?: 'toast' | 'banner';
  linkText?: string;
  visible?: boolean;
  onPress?: () => void;
  duration?: number;
  testID?: string;
}

const toastStyles = {
  backgroundColor: {
    [ToastType.SUCCESS]: colors.success,
    [ToastType.ERROR]: colors.red50,
    [ToastType.WARNING]: colors.warning,
  },
  textColor: {
    [ToastType.SUCCESS]: colors.greenDark,
    [ToastType.ERROR]: colors.error,
    [ToastType.WARNING]: colors.yellowDark,
  },
};

const getToastIcon = (
  type: ToastType,
  color: string,
  testID: string,
  size = iconView.width,
): React.ReactNode => {
  const iconProps = {
    width: size,
    height: size,
    style: { marginRight: 8 },
    testID: `${testID}.Icon`,
  };

  switch (type) {
    case ToastType.SUCCESS:
      return <CheckCircle {...iconProps} fill={color} />;
    case ToastType.ERROR:
      return <Cross {...iconProps} stroke={color} />;
    case ToastType.WARNING:
      return <WarningFilled {...iconProps} color={color} />;
    default:
      return null;
  }
};

const ToastMessage: React.FC<ToastMessageProps> = ({
  type,
  title,
  message,
  variant = 'toast',
  linkText,
  visible = true,
  onPress,
  testID = 'ToastMessage',
}) => {
  const opacity = useRef(new Animated.Value(0)).current;
  const { hideToast } = useToast();

  const background = toastStyles.backgroundColor[type];
  const color = toastStyles.textColor[type];
  const isToast = variant === 'toast';

  useEffect(() => {
    const anim = Animated.timing(opacity, {
      toValue: visible ? 1 : 0,
      duration: 300,
      useNativeDriver: true,
    });

    anim.start();
    return () => anim.stop();
  }, [visible, opacity]);

  if (!visible) return null;

  return (
    <Animated.View
      testID={`${testID}.Container`}
      style={[
        isToast
          ? [styles.toast, { backgroundColor: background, opacity }]
          : [styles.banner, { borderColor: color, opacity }],
      ]}>
      <TouchableOpacity
        onPress={isToast ? hideToast : onPress}
        activeOpacity={0.9}
        style={styles.content}
        accessibilityLabel={[
          `${type} message:`,
          message,
          linkText ? `. ${linkText}` : '',
        ].join(' ')}
        accessibilityRole={isToast ? 'alert' : 'button'}>
        {getToastIcon(type, color, testID)}
        <View style={styles.textContainer}>
          {title && <Text style={styles.title}>{title}</Text>}
          <Text
            testID={`${testID}.Text.message`}
            style={[styles.text, { color: isToast ? color : colors.black }]}>
            {message}{' '}
            {!!linkText && (
              <Text testID={`${testID}.Text.linkText`} style={styles.linkText}>
                {linkText}
              </Text>
            )}
          </Text>
        </View>
      </TouchableOpacity>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  toast: {
    alignSelf: 'center',
    position: 'absolute',
    top: 50,
    width: '90%',
    padding: 12,
    borderRadius: 8,
    zIndex: 10,
    ...shadowView,
  },
  banner: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: colors.white,
    padding: 12,
    borderLeftWidth: 4,
    borderRadius: 8,
    ...shadowView,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  textContainer: {
    flex: 1,
    flexDirection: 'column',
  },
  title: {
    fontSize: fontSize16.fontSize,
    color: colors.black,
    marginBottom: 4,
  } as TextStyle,
  text: {
    fontSize: small.fontSize,
    flexShrink: 1,
    flexWrap: 'wrap',
    color: colors.red300,
  } as TextStyle,
  linkText: {
    fontSize: small.fontSize,
    textDecorationLine: 'underline',
    color: colors.red600,
  } as ViewStyle,
});

export default ToastMessage;
