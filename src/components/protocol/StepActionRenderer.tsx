import React, { useMemo } from 'react';
import { StepAction } from '~/types/protocolActions.types';
import Button<PERSON>rapper from '~/components/buttons/ButtonWrapper';
import { useProtocolContext } from '~/services/protocol/context/ProtocolContext';
import { StyleSheet } from 'react-native';
import { Component } from '~/types/component.types';
import { evaluateFieldValidations } from '~/services/protocol/ProtocolValidationService';
import { useProtocolDataStore } from '~/services/protocol/context/ProtocolDataStore';
import { doesConditionHold } from '~/services/protocol/ProtocolConditionEvaluatorService';
import { IconMap } from '~/components/protocol/AssetMap';
import { parseBoolean } from '~/utils/strings';
import { handleSaveProtocolAction } from '~/services/protocol/ProtocolDataSyncService';
import { StepType } from '~/types/step.types';

export default function StepActionRenderer({
  action,
  components,
  isRowAction = false,
  parentComponentType = StepType.PAGE,
}: {
  action: StepAction;
  isRowAction?: boolean;
  components: Component[];
  parentComponentType: StepType;
}) {
  const { userData, setUserData } = useProtocolDataStore();
  const { handleAction, scenario } = useProtocolContext();

  const containerStyle = isRowAction ? styles.rowAction : null;

  const requiresValidation = parseBoolean(action?.options?.requiresValidation);

  const getImageData = (imageData: any, key: string): any => {
    const imagesToUpload = imageData?.imagesToUpload ?? {};
    return imagesToUpload[key] ?? null;
  };

  const shouldDisplay = action.displayIf
    ? doesConditionHold(action.displayIf, userData)
    : true;

  const isValidStep = useMemo(() => {
    if (!requiresValidation) return true;

    return components.every(component => {
      const { displayIf, validations = [] } = component;

      // Check if the component is visible based on the displayIf condition
      // and the current user data.
      const isComponentVisible = displayIf
        ? doesConditionHold(displayIf, userData)
        : true;

      // Skip validation if the component is not visible or has no validations.
      if (!isComponentVisible || validations.length === 0) return true;

      let value = null;

      if (component.type === 'PhotoCapture') {
        value = getImageData(userData, component.key);
      } else {
        value = userData[component.key];
      }

      const errors = evaluateFieldValidations(value, validations);

      return errors.length === 0;
    });
  }, [components, requiresValidation, userData]);

  if (!shouldDisplay) return null;

  const { icon } = action.options ?? {};

  const buttonIcon = icon ? <IconMap name={icon} /> : undefined;

  const onPress = async (stepAction: StepAction) => {
    const { onTapAction } = stepAction;

    if (onTapAction) {
      for (const _onTapAction of onTapAction) {
        if (_onTapAction.actionType === 'setValue') {
          const { keyValuePairs = [] } = _onTapAction;

          for (const { key, value, setCurrentDateTime } of keyValuePairs) {
            const finalValue = setCurrentDateTime
              ? new Date().toISOString()
              : value;
            setUserData(key, finalValue);
          }
        }

        if (_onTapAction.actionType === 'immediateUpdate') {
          const { payload, updateCriteria } = _onTapAction;
          const shouldUpdate = updateCriteria
            ? doesConditionHold(updateCriteria, userData)
            : true;

          if (shouldUpdate) {
            await handleSaveProtocolAction(payload, scenario);
          }
        }
      }
    }

    handleAction({ ...stepAction, stepType: parentComponentType });
  };

  const getButtonLabel = (isEnabled: boolean) => {
    if (typeof action.label === 'string') {
      return action.label;
    }
    return isEnabled ? action.label?.enabled : action.label?.disabled;
  };

  const getButtonId = (): string => {
    if (action.actionId) return action.actionId;
    if (typeof action.label === 'string') return action.label;
    return action.label?.enabled || '';
  };

  return (
    <ButtonWrapper
      id={getButtonId()}
      title={getButtonLabel(isValidStep)}
      containerStyle={containerStyle}
      variant={action.options?.variant}
      color={action.options?.priority}
      icon={buttonIcon}
      iconPosition={action.options?.iconPosition}
      onPress={() => onPress(action)}
      disabled={!isValidStep}
    />
  );
}

const styles = StyleSheet.create({
  rowAction: {
    flex: 1,
  },
});
