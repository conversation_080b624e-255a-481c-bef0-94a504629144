import React from 'react';
import { View, Text, StyleSheet, TextStyle } from 'react-native';
import { grayishText, h5 } from '~/styles/text';
import { IconMap } from '~/components/protocol/AssetMap';

interface ProtocolHeaderProps {
  title?: string;
  subtitle?: string;
  leftIcon?: string;
}

const ProtocolHeader = ({ title, subtitle, leftIcon }: ProtocolHeaderProps) => {
  if (!title && !subtitle) return null;

  return (
    <View style={styles.container}>
      <View style={styles.leftIconContainer}>
        {leftIcon && <IconMap name={leftIcon} />}
        {title && <Text style={styles.title}>{title}</Text>}
      </View>
      {subtitle && <Text style={styles.subtitle}>{subtitle}</Text>}
    </View>
  );
};

export default ProtocolHeader;

const styles = StyleSheet.create({
  leftIconContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  container: {
    gap: 6,
  },
  title: {
    ...h5,
  } as TextStyle,
  subtitle: {
    ...grayishText,
  } as TextStyle,
});
