import React from 'react';
import { Text } from 'react-native';
import { Component, ComponentType } from '~/types/component.types';
import { useComponentDisplay } from '~/services/protocol/hooks/useComponentDisplay';
import { useProtocolContext } from '~/services/protocol/context/ProtocolContext';
import { grayishText } from '~/styles/text';
import ComponentRegistry, {
  ComponentProps,
} from '~/components/protocol/ComponentRegistry';
import { useProtocolDataStore } from '~/services/protocol/context/ProtocolDataStore';

interface StepComponentProps {
  readonly component: Component;
}

type RegisteredComponentType =
  | React.FC<ComponentProps<ComponentType>>
  | undefined;

const DefaultComponent: React.FC<StepComponentProps> = ({ component }) => (
  <Text style={grayishText}>{`${component.type} yet to be implemented`}</Text>
);

const StepComponent = ({ component }: StepComponentProps) => {
  const { scenario } = useProtocolContext();
  const { userData } = useProtocolDataStore();

  const shouldDisplay = useComponentDisplay({
    condition: component.displayIf,
    userData,
  });

  if (!shouldDisplay) return null;

  const RegisteredComponent = ComponentRegistry[
    component.type
  ] as RegisteredComponentType;

  if (RegisteredComponent) {
    return (
      <RegisteredComponent
        key={component.key}
        component={component}
        scenario={scenario}
      />
    );
  }

  return <DefaultComponent component={component} />;
};

export default StepComponent;
