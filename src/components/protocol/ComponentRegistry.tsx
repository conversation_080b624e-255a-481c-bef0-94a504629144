import React, { useEffect } from 'react';
import { Text, TextStyle, StyleSheet, View } from 'react-native';
import { Component, ComponentType, TextOptions } from '~/types/component.types';
import TextInput from '~/components/inputs/TextInput';
import { grayishText, h5 } from '~/styles/text';
import RadioSelectionInput from '~/components/select/radio/RadioSelectionCard';
import StopTimer from '~/components/stoptimer/StopTimer';
import Slider from '~/components/inputs/Slider';
import CheckboxGroup from '~/components/checkbox/CheckboxGroup';
import CardWrapper from '~/components/cards/CardWrapper';
import ButtonWrapper from '~/components/buttons/ButtonWrapper';
import PhotoCaptureWrapper from '~/components/camera/PhotoCaptureWrapper';
import TemperatureInfo from '~/components/temperatureInfo/TemperatureInfo';
import { useProtocolData } from '~/services/protocol/context/ProtocolDataStore';
import { ImageTitleType } from '~/services/ImageStoreService';
import { useProtocolContext } from '~/services/protocol/context/ProtocolContext';
import { IconMap } from '~/components/protocol/AssetMap';
import { extractLinkedEntityIds } from '~/services/protocol/ProtocolScenarioDataService';
import { parseBoolean } from '~/utils/strings';

export type ComponentProps<T extends ComponentType> = {
  component: Extract<Component, { type: T }>;
  scenario: Record<string, any>;
};

const getTextStyle = (options: TextOptions): TextStyle => ({
  ...(styles[options.type] || {}),
  textAlign: options.align ?? 'left',
});

const ComponentRegistry: {
  [K in ComponentType]: React.FC<ComponentProps<K>> | undefined;
} = {
  [ComponentType.TEXT_INPUT]: ({ component }) => {
    const [value, setValue] = useProtocolData(component.key);

    return (
      <CardWrapper>
        <TextInput
          id={component.key}
          label={component.options.label ?? ''}
          onChangeText={setValue}
          validations={component.validations}
          placeholder={component.options.placeholder ?? ''}
          value={value}
        />
      </CardWrapper>
    );
  },

  [ComponentType.DISPLAY_TEXT]: ({ component, scenario }) => {
    const interpolateTemplate = (
      template: string,
      context: Record<string, any>,
    ): string => {
      return template.replace(/{{(.*?)}}/g, (_, rawKey: string): string => {
        const key = rawKey.trim();

        const nestedValue = key
          .split('.')
          .reduce<any>((obj, part) => obj?.[part], context);

        const flatValue = context[key];

        const finalValue = nestedValue !== undefined ? nestedValue : flatValue;

        return finalValue != null ? String(finalValue) : '';
      });
    };

    const content = interpolateTemplate(
      component.options.content || 'No content',
      scenario,
    );

    const text = (
      <Text style={getTextStyle(component.options)}>
        {content || 'No content'}
      </Text>
    );

    return component.options.wrapInCard ? (
      <CardWrapper>{text}</CardWrapper>
    ) : (
      text
    );
  },

  [ComponentType.ICON]: ({ component }) => (
    <View style={[styles.iconContainer, component.options.style]}>
      <IconMap name={component.options.name} />
    </View>
  ),

  [ComponentType.IMAGE]: ({ component }) => {
    const { multiple, readonly } = component.options;
    const isReadonly = parseBoolean(readonly);
    const isMultiple = parseBoolean(multiple);

    return (
      <PhotoCaptureWrapper
        key={component.key}
        multiple={isMultiple}
        imageTitle={component.options.label}
        imageData={component.options.uris}
        readonly={isReadonly}
      />
    );
  },
  [ComponentType.RADIO_SELECT]: ({ component }) => {
    const [value, setValue] = useProtocolData(component.key);

    return (
      <RadioSelectionInput
        label={component.options.label ?? ''}
        onChange={setValue}
        value={value}
        options={component.options.choices}
      />
    );
  },
  [ComponentType.BUTTON]: ({ component }) => {
    const { handleAction } = useProtocolContext();

    const onPress = () => {
      if (component.options.action) {
        handleAction(component.options.action);
      }
    };

    return (
      <ButtonWrapper
        id={component.key}
        title={component.options.label ?? ''}
        onPress={onPress}
        variant={component.options.variant}
        icon={<IconMap name={component.options.icon} />}
        iconPosition={component.options.iconPosition}
        color={component.options.priority}
      />
    );
  },
  [ComponentType.CHECKBOX_GROUP]: ({ component }) => {
    const [selectedOptionValues, setSelectedOptionValues] = useProtocolData(
      component.key,
    );

    const checkboxGroup = (
      <CheckboxGroup
        selectedOptionValues={selectedOptionValues}
        options={component.options.choices}
        onChecked={setSelectedOptionValues}
        optionBackgroundColor={component.options.style?.backgroundColor}
      />
    );

    return component.options?.wrapInCard ? (
      <CardWrapper title={component.options.label ?? ''}>
        {checkboxGroup}
      </CardWrapper>
    ) : (
      checkboxGroup
    );
  },
  [ComponentType.DROPDOWN]: undefined,
  [ComponentType.PHOTO_CAPTURE]: ({ component, scenario }) => {
    const { multiple = false, wrapInCard = false } = component.options;
    const isMultiple = Boolean(multiple);

    const [imagesToUpload, setImagesToUpload] =
      useProtocolData('imagesToUpload');
    const imageTitleKey = component.key as ImageTitleType;

    const existingImageData = imagesToUpload?.[imageTitleKey];

    const onCapture = async (imageBase64: string) => {
      const linkedEntityIds = component.linkedEntities;

      if (!linkedEntityIds || linkedEntityIds.length === 0) {
        console.warn(
          `ComponentRegistry.tsx: onCapture(): No linkedEntities defined`,
        );
        return;
      }

      const { Route_Summary__c, Stop__c, Daily_Schedule__c } =
        extractLinkedEntityIds(scenario, linkedEntityIds);

      if (!Route_Summary__c && !Stop__c && !Daily_Schedule__c) {
        console.warn(
          `ComponentRegistry.tsx: onCapture(): Missing required entity IDs: Stop__c=${Stop__c}, Route_Summary__c=${Route_Summary__c}`,
        );
        return;
      }

      let updatedImagesToUpload = { ...imagesToUpload };

      const newImageMetadata = {
        titleType: component.key as ImageTitleType,
        stopId: Stop__c,
        routeSummaryId: Route_Summary__c,
        dailyScheduleId: Daily_Schedule__c,
        base64String: imageBase64,
      };

      if (isMultiple) {
        updatedImagesToUpload[imageTitleKey] = [
          ...(existingImageData ?? []),
          newImageMetadata,
        ];
      } else {
        updatedImagesToUpload[imageTitleKey] = newImageMetadata;
      }

      setImagesToUpload(updatedImagesToUpload);
    };

    const imageData = (() => {
      if (!existingImageData) return isMultiple ? [] : '';

      if (Array.isArray(existingImageData)) {
        return existingImageData.map(
          img => `data:image/jpeg;base64, ${img.base64String}`,
        );
      }
      return `data:image/jpeg;base64, ${existingImageData.base64String}`;
    })();

    const photoWrapper = (
      <PhotoCaptureWrapper
        key={component.key}
        multiple={multiple}
        imageTitle={component.options.label}
        cameraTitle={component.options.cameraTitle}
        onCapture={onCapture}
        imageData={imageData}
      />
    );

    return wrapInCard ? (
      <CardWrapper>{photoWrapper}</CardWrapper>
    ) : (
      photoWrapper
    );
  },
  [ComponentType.TIMER]: ({ component, scenario }) => {
    // eslint-disable-next-line no-unused-vars
    const [_, setValue] = useProtocolData(component.key);

    const onComplete = () => {
      setValue('Inactive');
    };

    const id = scenario[`${component.options.id}`];

    return (
      <View style={styles.timerContainer}>
        <StopTimer
          durationInMinutes={component.options.duration}
          id={`${id}`}
          onComplete={onComplete}
        />
      </View>
    );
  },
  [ComponentType.SLIDER]: ({ component }) => {
    const [value, setValue] = useProtocolData(component.key);
    const {
      max = 100,
      min = 0,
      defaultValue = 0,
      step = 6,
      label = '',
      suffix = '°F',
    } = component.options;

    const currentValue = (() => {
      if (typeof value === 'number' && !isNaN(value)) {
        return Math.max(min, Math.min(max, value));
      }
      return Math.max(min, Math.min(max, defaultValue));
    })();

    useEffect(() => {
      if (value === undefined || value === null) {
        setValue(currentValue);
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const onSlideEnd = (newValue: number) => {
      setValue(newValue);
    };

    return (
      <Slider
        id={component.key}
        min={min}
        max={max}
        step={step}
        defaultValue={currentValue ?? 0}
        title={label}
        suffix={suffix}
        onSlideEnd={onSlideEnd}
      />
    );
  },
  [ComponentType.TEMPERATURE_INFO]: ({ component, scenario }) => {
    const location = scenario[`${component.options.location}`];
    const temperature = scenario[`${component.options.temperature}`];

    return (
      <View style={component.options.style}>
        <TemperatureInfo
          location={location}
          temperature={temperature}
          icon={<IconMap name={component.options.icon} />}
          testID={component.key}
        />
      </View>
    );
  },
};

export default ComponentRegistry;

const styles = StyleSheet.create({
  title: {
    ...h5,
  } as TextStyle,
  subtitle: {
    ...grayishText,
  } as TextStyle,
  iconContainer: {
    alignItems: 'center',
  },
  timerContainer: {
    justifyContent: 'flex-end',
    alignItems: 'center',
    height: '100%',
  },
});
