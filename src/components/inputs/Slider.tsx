import React, { useEffect, useMemo, useState } from 'react';
import { View, Text, StyleSheet, TextStyle } from 'react-native';
import { GestureDetector, Gesture } from 'react-native-gesture-handler';
import colors from '~/styles/colors';
import {
  bottomTabLabels,
  pendingInfoText,
  fontSize12,
  whiteText,
  blackishText,
  p,
} from '~/styles/text';
import { horizontalSpace16, margin16, paddingTop6 } from '~/styles/spacing';
import {
  SLIDER_WIDTH,
  THUMB_SIZE,
  TRACK_HEIGHT,
  THUMB_WIDTH,
} from '~/styles/views';

interface SliderProps {
  id: string;
  min: number;
  max: number;
  step: number;
  defaultValue: number;
  title: string;
  suffix: string;
  onSlideEnd?: (value: number) => void;
}

const Slider: React.FC<SliderProps> = ({
  id,
  min,
  max,
  step,
  defaultValue,
  title,
  suffix,
  onSlideEnd,
}) => {
  const [value, setValue] = useState(defaultValue);
  const [startValue, setStartValue] = useState(defaultValue);

  useEffect(() => {
    setValue(Math.max(min, Math.min(max, defaultValue)));
  }, [min, max, defaultValue]);

  const getPosition = (val: number) => {
    return Math.max(
      0,
      Math.min(
        ((val - min) / (max - min)) * (SLIDER_WIDTH - THUMB_SIZE),
        SLIDER_WIDTH - THUMB_SIZE,
      ),
    );
  };

  const getThumbPosition = (val: number) => {
    return getPosition(val) + THUMB_SIZE / 2;
  };

  const panGesture = Gesture.Pan()
    .onStart(() => {
      setStartValue(value);
    })
    .onUpdate(event => {
      let percentageMoved = event.translationX / (SLIDER_WIDTH - THUMB_SIZE);
      let rawValue = startValue + Math.round(percentageMoved * (max - min));
      let smoothValue = Math.max(min, Math.min(max, rawValue));
      setValue(smoothValue);
    })
    .onEnd(() => {
      onSlideEnd?.(value);
    });

  const markers = useMemo(() => {
    const markerArray: number[] = [];
    for (let i = min; i <= max; i += step) {
      markerArray.push(i);
    }
    return markerArray;
  }, [min, max, step]);

  return (
    <View id={id} testID={`${id}.Container`} style={styles.container}>
      <View style={styles.sliderContainer}>
        <View
          id={`${id}.filledTrack`}
          testID={`${id}.Track.Filled`}
          style={[
            styles.track,
            {
              backgroundColor: colors.darkBlue300,
              width: getThumbPosition(value),
            },
          ]}
        />
        <View
          id={`${id}.unfilledTrack`}
          testID={`${id}.Track.Unfilled`}
          style={[
            styles.track,
            {
              backgroundColor: colors.darkBlue50,
              width: SLIDER_WIDTH - getThumbPosition(value),
              left: getThumbPosition(value),
            },
          ]}
        />
        <GestureDetector gesture={panGesture}>
          <View
            id={`${id}.thumb`}
            testID={`${id}.Thumb`}
            style={[styles.thumb, { left: getPosition(value) }]}
          />
        </GestureDetector>

        <View
          id={`${id}.tooltip`}
          testID={`${id}.Tooltip`}
          style={[styles.tooltip, { left: getPosition(value) }]}>
          <Text style={styles.tooltipText}>{value + suffix}</Text>
          <View
            style={[
              styles.tooltipArrow,
              { borderTopColor: colors.darkBlue500 },
            ]}
          />
        </View>
      </View>

      <View style={styles.markersContainer}>
        {markers.map(marker => (
          <View key={marker} style={styles.markerWrapper}>
            <View
              id={`${id}.marker.${marker}`}
              testID={`${id}.Marker.${marker}`}
              style={styles.markerLine}
            />
            <Text style={styles.markerText}>{marker}</Text>
          </View>
        ))}
      </View>

      <Text id={`${id}.title`} testID={`${id}.Title`} style={styles.title}>
        {title}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    padding: margin16.margin,
    backgroundColor: colors.white,
    borderRadius: THUMB_SIZE / 2,
    paddingTop: THUMB_SIZE * 3,
  },
  title: {
    ...p,
    ...blackishText,
    marginVertical: horizontalSpace16.marginHorizontal,
  } as TextStyle,
  sliderContainer: {
    width: SLIDER_WIDTH,
    height: TRACK_HEIGHT,
    justifyContent: 'center',
  },
  track: {
    position: 'absolute',
    height: TRACK_HEIGHT,
    borderRadius: TRACK_HEIGHT / 2,
  },
  thumb: {
    position: 'absolute',
    width: THUMB_SIZE,
    height: THUMB_SIZE,
    borderRadius: THUMB_SIZE / 2,
    backgroundColor: colors.white,
    borderWidth: 2,
    borderColor: colors.darkBlue50,
    elevation: 2,
  },
  tooltip: {
    height: 30,
    top: -35,
    backgroundColor: colors.darkBlue500,
    padding: 6,
    borderRadius: 6,
    alignItems: 'center',
    width: THUMB_WIDTH,
    marginLeft: -THUMB_SIZE / 2,
  },
  tooltipArrow: {
    width: 0,
    height: 0,
    borderLeftWidth: 6,
    borderRightWidth: 6,
    borderTopWidth: 6,
    borderLeftColor: 'transparent',
    borderRightColor: 'transparent',
    bottom: -6,
  },
  tooltipText: { ...bottomTabLabels, ...whiteText } as TextStyle,
  markersContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: SLIDER_WIDTH,
    marginTop: 10,
    paddingHorizontal: 4,
  },
  markerWrapper: {
    alignItems: 'center',
    paddingTop: paddingTop6.paddingTop,
  },
  markerText: {
    ...pendingInfoText,
    ...fontSize12,
    paddingTop: paddingTop6.paddingTop,
  } as TextStyle,
  markerLine: {
    width: 2,
    height: 10,
    backgroundColor: colors.darkBlue25,
  },
});

export default Slider;
