import React, { useState } from 'react';
import { View, Text, StyleSheet, TextStyle, ViewStyle } from 'react-native';
import { IconNode, Input, InputProps } from '@rneui/base';
import colors from '~/styles/colors';
import { inputBoxContainer } from '~/styles/views';
import { h5, errorText, leftText, small, fontSize16, h4 } from '~/styles/text';
import { marginBottom10, paddingLeft10 } from '~/styles/spacing';
import { Validation } from '~/types/component.types';
import { evaluateFieldValidations } from '~/services/protocol/ProtocolValidationService';

interface TextInputProps extends InputProps {
  id?: string;
  hint?: string;
  numberOfLines?: number;
  icon?: IconNode;
  containerStyle?: ViewStyle;
  validations?: Validation[];
}

const TextInput: React.FC<TextInputProps> = ({
  id,
  hint,
  numberOfLines = 1,
  icon = undefined,
  errorMessage,
  containerStyle,
  onChangeText,
  validations,
  value,
  ...rest
}: TextInputProps) => {
  const [validationError, setValidationError] = useState<string | undefined>();

  const handleChange = (text: string) => {
    onChangeText?.(text);

    if (!validations) return;

    const errors = evaluateFieldValidations(text, validations);

    const firstError = errors.length > 0 ? errors[0] : undefined;

    setValidationError(firstError);
  };

  const errorForDisplay = errorMessage ?? validationError;

  return (
    <View style={[styles.container, containerStyle]}>
      <Input
        testID={id}
        labelStyle={
          [
            h5,
            leftText,
            marginBottom10,
            ...(errorForDisplay ? [errorText] : []),
          ] as TextStyle[]
        }
        multiline={numberOfLines > 1}
        maxLength={255}
        rightIcon={icon}
        value={value}
        onChangeText={handleChange}
        containerStyle={styles.containerStyle}
        inputContainerStyle={[
          inputBoxContainer,
          errorForDisplay ? styles.errorBorder : null,
        ]}
        inputStyle={styles.input}
        errorMessage={errorForDisplay}
        errorStyle={errorForDisplay ? styles.errorText : styles.errorHidden}
        {...rest}
      />
      {Boolean(hint) && <Text style={styles.hintText}>{hint}</Text>}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
  containerStyle: {
    paddingHorizontal: 0,
  },
  input: {
    flex: 1,
    ...h4,
    ...fontSize16,
  } as TextStyle,
  errorBorder: {
    borderColor: 'red',
    borderWidth: 1,
  },
  errorText: {
    ...errorText,
    fontSize: small.fontSize,
    marginTop: 4,
  },
  errorHidden: {
    height: 0,
    margin: 0,
    padding: 0,
  },
  hintText: {
    color: colors.grey400,
    fontSize: small.fontSize,
    marginTop: 4,
    paddingLeft: paddingLeft10.paddingLeft,
  },
});

export default TextInput;
