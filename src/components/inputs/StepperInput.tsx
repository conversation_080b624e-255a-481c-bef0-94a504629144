import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ViewStyle,
  TextStyle,
} from 'react-native';
import colors from '~/styles/colors';
import { center } from '~/styles/views';
import useLongPress from '~/hooks/useLongPress';
import { centerText, h3, letterSpacing } from '~/styles/text';

interface StepperInputProps {
  value: number;
  setValue: (value: number) => void;
  infoLabel: string;
}

const StepperInput: React.FC<StepperInputProps> = ({
  value,
  setValue,
  infoLabel = null,
}) => {
  const increment = () => setValue(value + 1);
  const decrement = () => setValue(value > 1 ? value - 1 : 1);

  const {
    handlePressIn: handlePressInIncrement,
    handlePressOut: handlePressOutIncrement,
  } = useLongPress(() => setValue(value + 2));

  const {
    handlePressIn: handlePressInDecrement,
    handlePressOut: handlePressOutDecrement,
  } = useLongPress(() => setValue(value > 2 ? value - 2 : 1));

  return (
    <View>
      <View style={styles.container}>
        <TouchableOpacity
          style={[styles.button, value === 1 && styles.disabledButton]}
          onPress={decrement}
          onLongPress={handlePressInDecrement}
          onPressOut={handlePressOutDecrement}
          disabled={value === 1}>
          <Text style={[styles.buttonText, value === 1 && styles.disabledText]}>
            -
          </Text>
        </TouchableOpacity>

        <Text style={styles.value}>{value}</Text>

        <TouchableOpacity
          style={styles.button}
          onPress={increment}
          onLongPress={handlePressInIncrement}
          onPressOut={handlePressOutIncrement}>
          <Text style={styles.buttonText}>+</Text>
        </TouchableOpacity>
      </View>

      {infoLabel && (
        <Text
          style={
            (h3 as TextStyle,
            letterSpacing as TextStyle,
            centerText as TextStyle)
          }>
          {infoLabel}
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginVertical: 10,
  },
  button: {
    ...(center as ViewStyle),
    width: 45,
    height: 45,
    borderRadius: 12,
    backgroundColor: colors.lightGrayishBlue,
    marginVertical: 10,
  },
  disabledButton: {
    backgroundColor: colors.backgroundLight,
  },
  buttonText: {
    fontSize: 24,
    color: colors.textBlack,
  },
  disabledText: {
    color: colors.grey400,
  },
  value: {
    fontSize: 45,
    fontWeight: '700',
    marginHorizontal: 20,
    color: colors.textBlack,
  },
});

export default StepperInput;
