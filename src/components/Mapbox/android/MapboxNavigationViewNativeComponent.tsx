import React from 'react';
import {
  requireNativeComponent,
  ViewProps,
  StyleSheet,
  NativeSyntheticEvent,
} from 'react-native';

type NativeCoordinate = [number, number]; // [longitude, latitude]

interface Waypoint {
  latitude: number;
  longitude: number;
  name?: string;
  separatesLegs?: boolean;
}

interface NativeEventsProps {
  onCancelNavigation?: () => void;
  onArrive?: (event: NativeSyntheticEvent<{ point: any }>) => void;
  onError?: (event: NativeSyntheticEvent<{ error: string }>) => void;
}

interface NativeProps extends ViewProps, NativeEventsProps {
  mute?: boolean;
  separateLegs?: boolean;
  distanceUnit?: string;
  startOrigin: NativeCoordinate;
  waypoints?: Waypoint[];
  destinationTitle?: string;
  destination: NativeCoordinate;
  language?: string;
  showCancelButton?: boolean;
  shouldSimulateRoute?: boolean;
  showsEndOfRouteFeedback?: boolean;
  hideStatusView?: boolean;
  travelMode?: string;
}

const RNMapboxNavigation = requireNativeComponent<NativeProps>('MapboxNavigationView');

const MapboxNavigation = (props: NativeProps) => {
  return <RNMapboxNavigation {...props} style={[styles.container, props.style]} />;
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default MapboxNavigation;
