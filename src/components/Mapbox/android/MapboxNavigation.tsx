import React from 'react';
import {
  StyleSheet,
  View,
  requireNativeComponent,
  ViewProps,
  NativeSyntheticEvent,
} from 'react-native';

type NativeCoordinate = [number, number];

interface Waypoint {
  latitude: number;
  longitude: number;
  name?: string;
  separatesLegs?: boolean;
}

interface NativeEventsProps {
  onCancelNavigation?: () => void;
  onArrive?: (event: NativeSyntheticEvent<{ point: any }>) => void;
  onError?: (event: NativeSyntheticEvent<{ error: string }>) => void;
}

interface NativeProps extends ViewProps, NativeEventsProps {
  mute?: boolean;
  separateLegs?: boolean;
  distanceUnit?: string;
  startOrigin: NativeCoordinate;
  waypoints?: Waypoint[];
  destinationTitle?: string;
  destination: NativeCoordinate;
  language?: string;
  showCancelButton?: boolean;
  shouldSimulateRoute?: boolean;
  showsEndOfRouteFeedback?: boolean;
  hideStatusView?: boolean;
  travelMode?: string;
}

const RNMapboxNavigation = requireNativeComponent<NativeProps>('MapboxNavigationView');

interface MapboxNavigationProps {
  startOrigin: {
    longitude: number;
    latitude: number;
  };
  destination: {
    longitude: number;
    latitude: number;
    title: string;
  };
  style?: any;
  distanceUnit?: string;
  onArrive?: (event: any) => void;
  onLocationChange?: (event: any) => void;
  onRouteProgressChange?: (event: any) => void;
  onCancelNavigation?: (event: any) => void;
  onError?: (event: any) => void;
  travelMode?: string;
}

const MapboxNavigation: React.FC<MapboxNavigationProps> = (props) => {
  const {
    startOrigin,
    destination,
    style,
    distanceUnit = 'imperial',
    onLocationChange,
    onRouteProgressChange,
    onCancelNavigation,
    onError,
    travelMode,
    ...rest
  } = props;

  return (
    <View style={style}>
      <RNMapboxNavigation
        style={styles.mapbox}
        distanceUnit={distanceUnit}
        startOrigin={[startOrigin.longitude, startOrigin.latitude]}
        destinationTitle={destination.title}
        destination={[destination.longitude, destination.latitude]}
        onLocationChange={(event) => onLocationChange?.(event.nativeEvent)}
        onRouteProgressChange={(event) =>
          onRouteProgressChange?.(event.nativeEvent)
        }
        onError={(event) => onError?.(event.nativeEvent)}
        onCancelNavigation={(event) =>
          onCancelNavigation?.(event.nativeEvent)
        }
        travelMode={travelMode}
        {...rest}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  mapbox: {
    flex: 1,
  },
  message: {
    textAlign: 'center',
    fontSize: 16,
  },
});

export default MapboxNavigation;
