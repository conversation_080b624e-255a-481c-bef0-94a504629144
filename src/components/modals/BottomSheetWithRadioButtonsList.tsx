import React, { useMemo, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  TextStyle,
  ViewStyle,
} from 'react-native';

import Modal from 'react-native-modal';

import { useSafeAreaInsets } from 'react-native-safe-area-context';
import en from '~/localization/en';
import colors from '~/styles/colors';
import { fontSize16, h5 } from '~/styles/text';
import { center, deviceHeight, itemSeparator } from '~/styles/views';
import RadioButtonItem from '~/components/select/radio/RadioButtonItem';
import SortOptionsSelector, {
  SORTING_OPTIONS,
  ItemSeparator,
} from '~/components/modals/SortModal';
import SortIcon from '~/components/icons/SortIcon';
import { useAddParcelContext } from '~/screens/parcel/AddParcelProvider';

export type BottomSheetItemType = {
  id: string;
  title: string;
  subtitle?: string;
  rightText?: string;
};

export type BottomSheetModalWithRadioButtonProps = {
  id: string;
  selectedId: string | null;
  items: Array<BottomSheetItemType>;
  isVisible: boolean;
  title: string;
  showSortButton?: boolean;
  onClose: () => void;
  onItemSelected: (id: string) => void;
};

const sortingOptions: Array<BottomSheetItemType> = [
  {
    id: SORTING_OPTIONS.ASCENDING,
    title: en.title_a_to_z,
    subtitle: en.subtitle_sorted_alphabetical,
  },
  {
    id: SORTING_OPTIONS.DESCENDING,
    title: en.title_z_to_a,
    subtitle: en.subtitle_sorted_reverse_alphabetical,
  },
  {
    id: SORTING_OPTIONS.NEXT_AVAILABLE_STOPS,
    title: en.title_next_available_stops,
    subtitle: en.subtitle_sorted_closest_time,
  },
];

const BottomSheetModalWithRadioButton = ({
  id,
  items,
  selectedId,
  isVisible,
  title,
  showSortButton = true,
  onClose,
  onItemSelected,
}: BottomSheetModalWithRadioButtonProps) => {
  const { selectedSortingOption, setSelectedSortingOption } =
    useAddParcelContext();
  const { bottom } = useSafeAreaInsets();

  const [isSortingBottomViewVisible, setIsSortingBottomViewVisible] =
    useState(false);

  const sortedItems = useMemo<Array<BottomSheetItemType>>(() => {
    let sortedDestinations = [...items];

    if (selectedSortingOption === SORTING_OPTIONS.ASCENDING) {
      sortedDestinations.sort((a, b) => a.title.localeCompare(b.title));
    } else if (selectedSortingOption === SORTING_OPTIONS.DESCENDING) {
      sortedDestinations.sort((a, b) => b.title.localeCompare(a.title));
    }

    setSelectedSortingOption(selectedSortingOption);

    return sortedDestinations;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [items, selectedSortingOption]);

  const renderItemSeparator = () => {
    return <ItemSeparator style={itemSeparator} />;
  };

  const showSortingView = () => {
    return (
      <SortOptionsSelector
        sortingOptions={sortingOptions}
        onSelect={setSelectedSortingOption}
        selectedOptionId={selectedSortingOption}
        onBackPressed={() => setIsSortingBottomViewVisible(false)}
      />
    );
  };

  const paddingBottom = bottom === 0 ? styles.bottomSheet.padding : bottom;

  return (
    <Modal
      isVisible={isVisible}
      animationIn="slideInUp"
      animationOut="slideOutDown"
      backdropOpacity={0.3}
      onBackdropPress={onClose}
      style={styles.modal}
      statusBarTranslucent>
      <View style={[styles.bottomSheet, { paddingBottom }]}>
        <View style={styles.header}>
          <Text
            id={`BottomSheetModalWithRadioButton.Text.title`}
            style={h5 as TextStyle}>
            {title}
          </Text>

          {showSortButton && (
            <TouchableOpacity
              id={`BottomSheetModalWithRadioButton.TouchableOpacity.sort`}
              testID="sort-action-button"
              style={styles.sortButton}
              onPress={() => {
                setIsSortingBottomViewVisible(true);
              }}>
              <SortIcon color={colors.darkBlue500} />
              <Text
                id={`BottomSheetModalWithRadioButton.Text.sort`}
                style={[fontSize16, { color: colors.darkBlue500 }]}>
                {en.sort}
              </Text>
            </TouchableOpacity>
          )}
        </View>

        <FlatList
          id={`BottomSheetModalWithRadioButton.FlatList.${id}`}
          ItemSeparatorComponent={renderItemSeparator}
          data={sortedItems}
          keyExtractor={item => item.id}
          renderItem={({ item }) => (
            <RadioButtonItem
              item={item}
              isSelected={item.id === selectedId}
              onItemSelected={onItemSelected}
            />
          )}
        />
      </View>

      {isSortingBottomViewVisible && showSortingView()}
    </Modal>
  );
};

const styles = StyleSheet.create({
  modal: {
    margin: 0,
  },
  bottomSheet: {
    flex: 1,
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: colors.white,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    padding: 16,
    maxHeight: deviceHeight * 0.8,
    gap: 24,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  sortButton: {
    ...(center as ViewStyle),
    gap: 8,
    padding: 8,
    paddingHorizontal: 12,
    borderRadius: 10,
    flexDirection: 'row',
    backgroundColor: colors.lightGrayishBlue,
  },
});

export default BottomSheetModalWithRadioButton;
