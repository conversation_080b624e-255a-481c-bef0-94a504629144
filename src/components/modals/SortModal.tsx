import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  FlatList,
  TextStyle,
  StyleSheet,
} from 'react-native';
import Modal from 'react-native-modal';
import RadioButtonItem from '~/components/select/radio/RadioButtonItem';
import en from '~/localization/en';
import { paddingRight10 } from '~/styles/spacing';
import { h5 } from '~/styles/text';
import colors from '~/styles/colors';
import { deviceHeight, itemSeparator } from '~/styles/views';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { BottomSheetItemType } from '~/components/modals/BottomSheetWithRadioButtonsList';
import BackArrow from '~/components/icons/BackArrow';

export const SORTING_OPTIONS = {
  ASCENDING: 'ascending',
  DESCENDING: 'descending',
  NEXT_AVAILABLE_STOPS: 'next_available_stops',
};

export type SortingOptionType =
  | typeof SORTING_OPTIONS.ASCENDING
  | typeof SORTING_OPTIONS.DESCENDING
  | typeof SORTING_OPTIONS.NEXT_AVAILABLE_STOPS;

export const ItemSeparator = ({ style }: { style?: object }) => (
  <View style={style} />
);

const SortModal = ({
  sortingOptions,
  onSelect,
  selectedOptionId,
  onBackPressed,
}: {
  sortingOptions: Array<BottomSheetItemType>;
  onSelect: (id: SortingOptionType) => void;
  selectedOptionId: string | undefined;
  onBackPressed: () => void;
}) => {
  const { bottom } = useSafeAreaInsets();

  const paddingBottom = bottom === 0 ? styles.bottomSheet.padding : bottom;

  const renderItemSeparator = () => {
    return <ItemSeparator style={itemSeparator} />;
  };

  return (
    <Modal
      id={`SortModal.Modal.sort`}
      isVisible
      animationIn="slideInUp"
      animationOut="slideOutDown"
      backdropOpacity={0.3}
      style={styles.modal}
      statusBarTranslucent>
      <View style={[styles.bottomSheet, { paddingBottom }]}>
        <View style={styles.sortingHeader}>
          <TouchableOpacity
            id={`SortModal.TouchableOpacity.back`}
            testID="back-button"
            style={paddingRight10}
            onPress={onBackPressed}>
            <BackArrow color={colors.black} />
          </TouchableOpacity>

          <Text id={`SortModal.Text.sort`} style={h5 as TextStyle}>
            {en.sort_by}
          </Text>
        </View>

        <FlatList
          id={`SortModal.FlatList.sort`}
          ItemSeparatorComponent={renderItemSeparator}
          data={sortingOptions}
          keyExtractor={item => item.id}
          renderItem={({ item }) => (
            <RadioButtonItem
              item={item}
              isSelected={item.id === selectedOptionId}
              onItemSelected={id => {
                onSelect(id);
                onBackPressed();
              }}
            />
          )}
        />
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modal: {
    margin: 0,
  },
  bottomSheet: {
    flex: 1,
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: colors.white,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    padding: 16,
    maxHeight: deviceHeight * 0.8,
    gap: 24,
  },
  sortingHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
});

export default SortModal;
