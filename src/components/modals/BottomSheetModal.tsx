import React from 'react';
import {
  View,
  StyleSheet,
  Text,
  TouchableOpacity,
  FlatList,
} from 'react-native';
import Modal from 'react-native-modal';
import colors from '~/styles/colors';
import { marginBottom10 } from '~/styles/spacing';
import { deviceWidth } from '~/styles/views';

/**
 * Interface for action button items in the action list variant.
 */
export interface ActionItem {
  id: string;
  title: string;
  onPress: () => void;
}

/**
 * Interface for BottomSheetModal Props.
 */
interface BottomSheetModalProps {
  isVisible: boolean;
  onClose: () => void;
  actions: ActionItem[];
  cancelTitle?: string;
}

/**
 * The BottomSheetModal component renders a modal at the bottom of the screen with action buttons.
 *
 * Displays a grouped list of action buttons with dividers, and a separated cancel button at the bottom.
 *
 * @component
 * @param {boolean} isVisible - Whether the modal is visible or not. Default is false.
 * @param {function} props.onClose - A callback function that is triggered when the modal is closed or cancel is pressed. Default is () => {}.
 * @param {ActionItem[]} props.actions - Array of action items (excludes cancel button).
 * @param {string} props.cancelTitle - Title for the cancel button. Default is 'Cancel'.
 */
const BottomSheetModal = ({
  isVisible = false,
  onClose = () => {},
  actions,
  cancelTitle = 'Cancel',
}: BottomSheetModalProps) => {
  const renderActionList = () => {
    return (
      <FlatList
        data={actions}
        keyExtractor={item => item.id}
        renderItem={({ item }) => (
          <TouchableOpacity
            style={styles.actionButton}
            activeOpacity={0.7}
            onPress={item.onPress}>
            <Text style={styles.actionText}>{item.title}</Text>
          </TouchableOpacity>
        )}
        ItemSeparatorComponent={() => <View style={styles.divider} />}
      />
    );
  };

  return (
    <Modal
      isVisible={isVisible}
      onBackdropPress={onClose}
      onSwipeComplete={onClose}
      swipeDirection="down"
      style={styles.modal}
      backdropOpacity={0.5}
      backdropTransitionOutTiming={0}
      statusBarTranslucent>
      <View style={styles.container}>
        {/* Action buttons group */}
        <View style={styles.buttonGroup}>{renderActionList()}</View>

        {/* Cancel button */}
        <TouchableOpacity
          style={styles.cancelButton}
          activeOpacity={0.7}
          onPress={onClose}>
          <Text style={styles.cancelText}>{cancelTitle}</Text>
        </TouchableOpacity>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modal: {
    justifyContent: 'flex-end',
    margin: 0,
  },
  container: {
    alignItems: 'center',
    marginBottom: 20,
  },
  buttonGroup: {
    width: deviceWidth - 40,
    backgroundColor: colors.grayishBlue,
    borderRadius: 12,
    overflow: 'hidden',
  },
  actionButton: {
    height: 56,
    justifyContent: 'center',
    alignItems: 'center',
  },
  actionText: {
    fontSize: 16,
    textAlign: 'center',
    color: colors.blue700,
  },
  divider: {
    height: StyleSheet.hairlineWidth,
    backgroundColor: colors.buttonGray,
  },
  cancelButton: {
    width: deviceWidth - 40,
    height: 56,
    backgroundColor: colors.grayishBlue,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 8,
    ...marginBottom10,
  },
  cancelText: {
    fontSize: 16,
    textAlign: 'center',
    color: colors.blue700,
    fontWeight: 'bold',
  },
});

export default BottomSheetModal;
