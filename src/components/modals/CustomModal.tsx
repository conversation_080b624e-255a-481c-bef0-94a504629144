import React, { useEffect, useState } from 'react';
import {
  Modal,
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextStyle,
} from 'react-native';
import { BlurView } from '@react-native-community/blur';
import colors from '~/styles/colors';
import { h3 } from '~/styles/text';
import TextInput from '~/components/inputs/TextInput';

interface CustomModalProps {
  isVisible: boolean;
  onClose: () => void;
  headerText: string;
  descriptionText: string;
  okButtonText?: string;
  cancelButtonText?: string;
  onOkPress?: (text: string) => void;
  onCancelPress?: () => void;
  headerIcon?: any;
  testID?: string;
  disabled?: boolean;
  // New props for text input functionality
  showTextInput?: boolean;
  textInputLabel?: string;
  textInputPlaceholder?: string;
  textInputValue?: string;
  onTextInputChange?: (text: string) => void;
  textInputValidation?: (text: string) => string | undefined;
}

/**
 * A custom modal component that displays a header, description, optional text input, and optional action buttons.
 *
 * @component
 * @param {boolean} props.isVisible - Controls the visibility of the modal. `true` to show, `false` to hide.
 * @param {function} props.onClose - Callback function to close the modal. Triggered on modal dismiss or cancel action.
 * @param {string} props.headerText - The main title text displayed at the top of the modal.
 * @param {string} props.headerIcon - The main icon displayed at the top of the modal.
 * @param {string} props.descriptionText - The body text displayed as the modal's main content.
 * @param {string} [props.okButtonText] - Optional. Text displayed on the OK/confirmation button. Defaults to "OK" if not provided.
 * @param {string} [props.cancelButtonText] - Optional. Text displayed on the cancel button. Defaults to "Cancel" if not provided.
 * @param {function} [props.onOkPress] - Optional. Callback function triggered when the OK button is pressed.
 * @param {function} [props.onCancelPress] - Optional. Callback function triggered when the Cancel button is pressed.
 * @param {string} [props.testID] - Optional. TestID for the modal.
 * @param {boolean} [props.showTextInput] - Optional. Whether to show a text input in the modal.
 * @param {string} [props.textInputLabel] - Optional. Label for the text input.
 * @param {string} [props.textInputPlaceholder] - Optional. Placeholder for the text input.
 * @param {string} [props.textInputValue] - Optional. Value for the text input.
 * @param {function} [props.onTextInputChange] - Optional. Callback when text input changes.
 * @param {function} [props.textInputValidation] - Optional. Validation function for text input.
 *
 * @example
 * // Example usage of CustomModal with text input
 * <CustomModal
 *   isVisible={isModalVisible}
 *   onClose={() => setIsModalVisible(false)}
 *   headerText="Enter Barcode"
 *   descriptionText="Please enter the barcode manually"
 *   okButtonText="Confirm"
 *   cancelButtonText="Cancel"
 *   onOkPress={handleConfirm}
 *   onCancelPress={() => setIsModalVisible(false)}
 *   showTextInput={true}
 *   textInputLabel="Barcode"
 *   textInputPlaceholder="Enter barcode here"
 *   textInputValue={barcodeValue}
 *   onTextInputChange={setBarcodeValue}
 * />
 */
const CustomModal: React.FC<CustomModalProps> = ({
  isVisible,
  onClose,
  headerText,
  descriptionText,
  okButtonText,
  cancelButtonText,
  onOkPress,
  onCancelPress,
  headerIcon,
  testID,
  disabled,
  showTextInput = false,
  textInputLabel,
  textInputPlaceholder,
}) => {
  const [inputValue, setInputValue] = useState('');

  const isOnlyOkButtonVisible = okButtonText && !cancelButtonText;
  const isOnlyCancelButtonVisible = cancelButtonText && !okButtonText;
  const isBottomViewVisible = okButtonText || cancelButtonText;

  const handleTextInputChange = (text: string) => {
    setInputValue?.(text);
  };

  useEffect(() => {
    if (!isVisible) {
      setInputValue('');
    }
  }, [isVisible]);

  const renderBottomActionView = () => {
    return (
      <View style={styles.bottomView}>
        {cancelButtonText && (
          <TouchableOpacity
            style={[
              styles.button,
              styles.cancelButton,
              isOnlyCancelButtonVisible && styles.flexOne,
            ]}
            aria-disabled={disabled}
            disabled={disabled}
            onPress={onCancelPress}>
            <Text style={styles.cancelButtonText}>{cancelButtonText}</Text>
          </TouchableOpacity>
        )}
        {okButtonText && (
          <TouchableOpacity
            style={[
              styles.button,
              styles.okButton,
              isOnlyOkButtonVisible && styles.flexOne,
            ]}
            onPress={() => onOkPress?.(inputValue)}>
            <Text style={styles.okButtonText}>{okButtonText}</Text>
          </TouchableOpacity>
        )}
      </View>
    );
  };

  return (
    <Modal
      testID={testID}
      visible={isVisible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
      statusBarTranslucent>
      <BlurView style={styles.blurView} blurType="dark" blurAmount={10} />
      <View style={styles.centeredView}>
        <View
          style={[
            styles.modalView,
            showTextInput && styles.modalViewWithInput,
          ]}>
          <View style={styles.headerView}>
            {headerIcon}
            <Text style={styles.headerText}>{headerText}</Text>
          </View>
          <View style={styles.descriptionView}>
            <Text style={styles.descriptionText}>{descriptionText}</Text>
          </View>

          {showTextInput && (
            <View style={styles.inputContainer}>
              <TextInput
                id="CustomModal.TextInput"
                label={textInputLabel}
                placeholder={textInputPlaceholder}
                value={inputValue}
                onChangeText={handleTextInputChange}
                autoFocus={true}
                returnKeyType="done"
                containerStyle={styles.textInputContainer}
              />
            </View>
          )}

          {isBottomViewVisible && renderBottomActionView()}
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  blurView: {
    ...StyleSheet.absoluteFillObject,
  },
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalView: {
    width: '80%',
    backgroundColor: 'white',
    borderRadius: 10,
    overflow: 'hidden',
  },
  modalViewWithInput: {
    width: '85%',
  },
  headerView: {
    alignItems: 'center',
    marginBottom: 10,
    flexDirection: 'column',
    paddingTop: 16,
    paddingHorizontal: 16,
  },
  headerText: {
    ...h3,
    fontSize: 20,
    color: colors.modalHeader,
    marginBottom: 8,
    marginTop: 16,
    textAlign: 'center',
  } as TextStyle,
  descriptionView: {
    marginBottom: 15,
    alignItems: 'center',
    paddingHorizontal: 16,
  },
  descriptionText: {
    ...h3,
    fontSize: 16,
    color: colors.modalDesc,
    textAlign: 'center',
    lineHeight: 22,
  } as TextStyle,
  inputContainer: {
    paddingHorizontal: 16,
    marginBottom: 20,
  },
  textInputContainer: {
    marginBottom: 0,
  },
  bottomView: {
    flexDirection: 'row',
    backgroundColor: colors.blue300,
    paddingVertical: 16,
    paddingHorizontal: 16,
  },
  button: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 12,
    marginHorizontal: 5,
    borderRadius: 8,
  },
  okButton: {
    backgroundColor: colors.red600,
  },
  okButtonText: {
    ...h3,
    fontSize: 16,
    color: colors.white,
    textAlign: 'center',
  } as TextStyle,
  cancelButton: {
    backgroundColor: 'white',
    borderColor: colors.lightGray,
    borderWidth: 1,
  },
  cancelButtonText: {
    ...h3,
    fontSize: 16,
    color: colors.grey900,
    textAlign: 'center',
  } as TextStyle,
  flexOne: {
    flex: 1,
  },
});

export default CustomModal;
