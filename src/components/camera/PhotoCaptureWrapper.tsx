import React from 'react';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useNavigation } from '@react-navigation/native';
import { RootStackParamList } from '~/navigation/RouteStack';
import en from '~/localization/en';
import PhotoCapture from '~/components/camera/PhotoCapture';
import PhotoView from '~/components/camera/PhotoView';
import { resolveImageSource } from '~/components/images/ImageGrid';

type SinglePhotoProps = {
  multiple?: false;
  imageData: string;
  imageTitle: string;
  cameraTitle?: string;
  onCapture: (imageBase64: string) => void;
};

type MultiplePhotosProps = {
  multiple: true;
  imageData: string[];
  imageTitle?: string;
  cameraTitle?: string;
  onCapture?: (imageBase64: string) => void;
  readonly?: boolean;
};

type PhotoCaptureWrapperProps = SinglePhotoProps | MultiplePhotosProps;

const PhotoCaptureWrapper: React.FC<PhotoCaptureWrapperProps> = props => {
  const navigation =
    useNavigation<NativeStackNavigationProp<RootStackParamList>>();

  const openPhotoViewer = (imageUri: string) => {
    navigation.navigate('PhotoViewScreen', {
      source: resolveImageSource(imageUri),
      title: props.imageTitle ?? en.photo_view,
    });
  };

  if (props.multiple) {
    return (
      <PhotoView
        label={props.imageTitle}
        cameraTitle={props.cameraTitle}
        imageUris={props.imageData}
        onCapture={props.onCapture}
        onSelect={openPhotoViewer}
        readonly={props.readonly}
      />
    );
  }

  return (
    <PhotoCapture
      imageUri={props.imageData}
      cameraTitle={props.cameraTitle}
      imageTitle={props.imageTitle}
      onCapture={props.onCapture}
      onSelect={openPhotoViewer}
    />
  );
};

export default PhotoCaptureWrapper;
