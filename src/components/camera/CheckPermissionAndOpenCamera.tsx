import { CameraPermissionService } from '~/services/permission/CameraPermissionService';
import { CameraViewScreenProps } from '~/screens/CameraViewScreen';

type CameraViewScreenOptions = CameraViewScreenProps['route']['params'];

// Function to check camera permissions and navigate to the camera view
export const checkCameraPermissionsAndOpenIt = async (
  navigation: any,
  getImagePathOrBarcodeResult: (path: string) => void,
  options: CameraViewScreenOptions,
) => {
  const hasPermissions = await CameraPermissionService.checkPermission();

  if (!hasPermissions) {
    const requestStatus =
      await CameraPermissionService.forceRequestPermission();

    if (!requestStatus) {
      return;
    }
  }

  await navigateToCameraViewScreen(
    navigation,
    getImagePathOrBarcodeResult,
    options,
  );
};

// Function to navigate to the camera view screen
const navigateToCameraViewScreen = async (
  navigation: any,
  getImagePathOrBarcodeResult: (path: string) => void,
  options: CameraViewScreenOptions,
) => {
  navigation.navigate('CameraViewScreen', {
    ...options,
    getImagePathOrBarcodeResult,
  });
};
