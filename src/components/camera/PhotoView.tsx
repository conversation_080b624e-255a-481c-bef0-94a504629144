import React from 'react';
import {
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  ViewStyle,
  TextStyle,
} from 'react-native';
import ImageGrid from '~/components/images/ImageGrid';
import { checkCameraPermissionsAndOpenIt } from '~/components/camera/CheckPermissionAndOpenCamera';
import { PhotoWithBackground } from '~/assets/icons';
import en from '~/localization/en';
import colors from '~/styles/colors';
import { h4, h5, buttonText } from '~/styles/text';
import { marginTop8, row, center } from '~/styles/views';
import { useNavigation } from '@react-navigation/native';
import CardWrapper from '~/components/cards/CardWrapper';

interface PhotoViewProps {
  imageUris: string[];
  label?: string;
  cameraTitle?: string;
  onCapture?: (imageBase64: string) => void;
  onSelect: (imagePath: string) => void;
  readonly?: boolean;
}

const PhotoView: React.FC<PhotoViewProps> = ({
  imageUris = [],
  readonly = false,
  label = en.photos_taken,
  cameraTitle = en.photos_taken,
  onCapture = () => {},
  onSelect,
}) => {
  const navigation = useNavigation();

  return (
    <CardWrapper>
      <View style={styles.row}>
        <Text style={styles.infoLabel}>{label}</Text>
        {!readonly && (
          <TouchableOpacity
            onPress={async () => {
              await checkCameraPermissionsAndOpenIt(navigation, onCapture, {
                title: cameraTitle,
              });
            }}>
            <Text style={styles.redTextButton}>{en.take_new}</Text>
          </TouchableOpacity>
        )}
      </View>

      <ImageGrid images={imageUris} onImagePress={onSelect} />

      {!imageUris.length && (
        <View style={styles.parcelPlaceholder}>
          <PhotoWithBackground />
          <Text style={styles.infoText}>{en.photos_taken_appear_here}</Text>
        </View>
      )}
    </CardWrapper>
  );
};

const styles = StyleSheet.create({
  infoText: {
    ...h4,
    ...marginTop8,
    color: colors.grey800,
    fontSize: 16,
  } as TextStyle,
  row: {
    ...(row as ViewStyle),
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  infoLabel: {
    ...h5,
    color: colors.grey900,
  } as TextStyle,
  redTextButton: {
    ...(buttonText as TextStyle),
    color: colors.red400,
  } as TextStyle,
  parcelPlaceholder: {
    ...(center as ViewStyle),
    height: 120,
    backgroundColor: colors.backgroundLight,
    borderRadius: 8,
  },
});

export default PhotoView;
