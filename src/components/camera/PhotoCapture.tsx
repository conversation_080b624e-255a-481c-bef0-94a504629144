import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Image,
  TextStyle,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import colors from '~/styles/colors';
import { blackText, buttonText, h5 } from '~/styles/text';
import { deviceWidth } from '~/styles/views';
import { Camera } from '~/components/icons';
import en from '~/localization/en';
import { IMAGE_ASPECT_RATIO } from '~/utils/constants';
import { checkCameraPermissionsAndOpenIt } from '~/components/camera/CheckPermissionAndOpenCamera';

interface PhotoCaptureProps {
  imageTitle: string;
  cameraTitle?: string;
  imageUri: string | null;
  onCapture: (imageBase64: string) => void;
  onSelect: (imageUri: string) => void;
}

const PhotoCapture: React.FC<PhotoCaptureProps> = ({
  imageTitle,
  imageUri,
  cameraTitle,
  onCapture,
  onSelect,
}) => {
  const navigation = useNavigation();

  const openCamera = async () => {
    const cameraHeaderTitle = cameraTitle ?? imageTitle;

    await checkCameraPermissionsAndOpenIt(navigation, onCapture, {
      title: cameraHeaderTitle,
    });
  };

  return (
    <View style={styles.card}>
      <View style={styles.row}>
        <Text style={[h5, blackText] as TextStyle}>{imageTitle}</Text>

        {imageUri && (
          <TouchableOpacity onPress={openCamera}>
            <Text style={styles.redTextButton}>{en.retake}</Text>
          </TouchableOpacity>
        )}
      </View>

      {imageUri ? (
        <TouchableOpacity onPress={() => onSelect(imageUri)}>
          <Image
            style={styles.signature}
            source={{ uri: imageUri }}
            resizeMode="cover"
          />
        </TouchableOpacity>
      ) : (
        <TouchableOpacity style={styles.photoPlaceholder} onPress={openCamera}>
          <Camera />
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  card: { gap: 16 },
  signature: {
    height: deviceWidth * IMAGE_ASPECT_RATIO,
    borderWidth: 1,
    borderRadius: 8,
    borderColor: colors.backgroundLight,
  },
  photoPlaceholder: {
    height: deviceWidth * IMAGE_ASPECT_RATIO,
    backgroundColor: colors.white,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.darkBlue500,
    borderStyle: 'dashed',
  },
  redTextButton: {
    ...(buttonText as TextStyle),
    color: colors.red400,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
});

export default PhotoCapture;
