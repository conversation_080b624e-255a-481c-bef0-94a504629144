import React from 'react';
import { Text, TextStyle, View } from 'react-native';
import { horizontalSpace16, marginTop16 } from '~/styles/spacing';
import { errorText, leftText, p } from '~/styles/text';

type ErrorTextProps = {
  id: string;
  text: string;
  style?: TextStyle;
};

const ErrorText = ({ id, text, style }: ErrorTextProps) => {
  return (
    <View style={[horizontalSpace16, marginTop16]}>
      <Text
        id={id}
        style={style ? style : ([errorText, leftText, p] as TextStyle)}>
        {text}
      </Text>
    </View>
  );
};

export default ErrorText;
