import React from 'react';
import { StyleSheet, Text, TextStyle, View, ViewStyle } from 'react-native';
import { marginBottom10 } from '~/styles/spacing';
import {
  blackText,
  heading,
  leftText,
  small,
  stepperText,
} from '~/styles/text';
import { column, container, row, stepperView } from '~/styles/views';
import TextButton from '~/components/buttons/TextButton';
import { CircleBackground } from '~/components/icons';
import colors from '~/styles/colors';

interface TitleProps {
  title: string;
  subtitle?: string;
  icon?: React.ReactNode;
  textButtonVisible?: boolean;
  textButtonTitle?: string;
  onTextButtonPress?: () => void;
  showProgressText?: boolean;
  progressText?: string; // Should be a string of the form "1/4"
}

const Title = ({
  title,
  subtitle = '',
  icon = null,
  textButtonVisible = false,
  textButtonTitle = '',
  onTextButtonPress = () => {},
  showProgressText = false,
  progressText = '',
}: TitleProps) => {
  const titleView = {
    ...column,
    flex: textButtonVisible ? 6 : 10,
  } as ViewStyle;

  return (
    <View style={container}>
      <View style={styles.header}>
        {icon && (
          <View style={styles.iconView}>
            <CircleBackground fillColor={colors.white}>{icon}</CircleBackground>
          </View>
        )}

        <View style={titleView}>
          <Text style={styles.title}>{title}</Text>
        </View>

        {textButtonVisible && (
          <View style={styles.rightView}>
            <TextButton
              title={textButtonTitle}
              onClick={onTextButtonPress}
              isDisabled={false}
            />
          </View>
        )}

        {showProgressText && Boolean(progressText) && (
          <View style={stepperView as ViewStyle}>
            <Text style={[stepperText as TextStyle, small]}>
              {progressText}
            </Text>
          </View>
        )}
      </View>

      {Boolean(subtitle) && (
        <View style={styles.subtitleView}>
          <Text style={styles.subtitle as TextStyle}>{subtitle}</Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  header: {
    ...row,
    ...marginBottom10,
    ...container,
    alignItems: 'center',
    gap: 12,
  } as ViewStyle,
  title: {
    ...heading,
    ...blackText,
    ...leftText,
  } as TextStyle,
  iconView: {
    ...column,
    ...container,
    justifyContent: 'center',
    alignItems: 'flex-start',
    marginRight: 12,
  } as ViewStyle,
  rightView: {
    ...column,
    justifyContent: 'center',
    flex: 5,
  } as ViewStyle,
  subtitleView: {
    ...container,
    marginBottom: 16,
  } as ViewStyle,
  subtitle: {
    ...leftText,
    color: colors.darkGray,
    fontSize: 20,
    fontFamily: 'Satoshi Variable',
    fontWeight: '400',
    wordWrap: 'break-word',
    alignSelf: 'stretch',
  } as TextStyle,
});

export default Title;
