/**
 * A reusable error component that displays an error message and optionally a button for retry or other actions.
 *
 * @component
 * @param {string} errorText - The error message to display.
 * @param {string} [buttonText] - Optional text for the button. The button will be visible only if this is provided.
 * @param {() => void} [buttonClickFunction] - Optional callback function triggered when the button is pressed.
 * @param {StyleProp<ViewStyle>} [containerStyle] - Optional style for the container view.
 * @param {StyleProp<TextStyle>} [errorTextStyle] - Optional style for the error text.
 * @param {StyleProp<ViewStyle>} [buttonStyle] - Optional style for the button container.
 *
 * @example
 * // Example usage:
 * import React from 'react';
 * import { SafeAreaView } from 'react-native';
 * import ErrorComponent from './ErrorComponent';
 *
 * const App = () => {
 *   const handleRetry = () => {
 *     console.log('Retry button clicked!');
 *   };
 *
 *   return (
 *     <SafeAreaView style={{ flex: 1 }}>
 *       <ErrorComponent
 *         errorText="Something went wrong. Please try again."
 *         buttonText="Retry"
 *         buttonClickFunction={handleRetry}
 *       />
 *     </SafeAreaView>
 *   );
 * };
 *
 * export default App;
 */

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Button,
  StyleProp,
  ViewStyle,
  TextStyle,
} from 'react-native';

interface ErrorComponentProps {
  errorText: string;
  buttonText?: string;
  buttonClickFunction?: () => void;
  containerStyle?: StyleProp<ViewStyle>;
  errorTextStyle?: StyleProp<TextStyle>;
  buttonStyle?: StyleProp<ViewStyle>;
}

const ErrorComponent: React.FC<ErrorComponentProps> = ({
  errorText,
  buttonText,
  buttonClickFunction,
  containerStyle,
  errorTextStyle,
  buttonStyle,
}) => {
  return (
    <View style={[styles.center, containerStyle]}>
      <Text style={[styles.errorText, errorTextStyle]}>{errorText}</Text>
      {buttonText && (
        <View style={[styles.buttonContainer, buttonStyle]}>
          <Button title={buttonText} onPress={buttonClickFunction} />
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  center: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    color: 'red',
    fontSize: 16,
    marginBottom: 10,
    textAlign: 'center',
  },
  buttonContainer: {
    marginTop: 10,
    width: '60%',
  },
});

export default ErrorComponent;
