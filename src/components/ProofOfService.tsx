import React from 'react';
import TextInput from '~/components/inputs/TextInput';
import CardWrapper from '~/components/cards/CardWrapper';
import { View } from 'react-native';
import { gap24 } from '~/styles/spacing';
import en from '~/localization/en';
import PhotoCaptureWrapper from '~/components/camera/PhotoCaptureWrapper';

const ProofOfService = ({
  imageData,
  onCaptureClick,
  onCommentChange,
  commentValue,
  onProofOfServiceChange,
  proofOfServiceValue,
  onBlur,
}: {
  imageData: string;
  onCaptureClick: (img: string) => void;
  onCommentChange: (comment: string) => void;
  commentValue: string;
  onProofOfServiceChange: (proofOfService: string) => void;
  proofOfServiceValue: string;
  onBlur: () => void;
}) => {
  return (
    <CardWrapper id={'ProofOfService'}>
      <View style={gap24}>
        <PhotoCaptureWrapper
          imageTitle={en.service_photo_proof}
          imageData={imageData}
          onCapture={onCaptureClick}
        />

        <TextInput
          id="ProofOfService.TextInput.pod"
          label="Proof of service"
          placeholder="Person spoken to"
          hint="e.g. TT Sarah, Business closed"
          defaultValue={proofOfServiceValue}
          onChangeText={onProofOfServiceChange}
          onBlur={onBlur}
        />

        <TextInput
          id="ProofOfService.TextInput.comment"
          label="Service comments (optional)"
          placeholder="Additional service notes"
          defaultValue={commentValue}
          onChangeText={onCommentChange}
          onBlur={onBlur}
        />
      </View>
    </CardWrapper>
  );
};

export default ProofOfService;
