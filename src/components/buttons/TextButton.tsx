import React from 'react';
import {
  Text,
  TextStyle,
  TouchableOpacity,
  StyleSheet,
  StyleProp,
} from 'react-native';
import colors from '~/styles/colors';
import { buttonText } from '~/styles/text';

type TextButtonProps = {
  title: string;
  onClick: () => void;
  isDisabled: boolean;
  style?: StyleProp<TextStyle>;
  testID?: string;
};

const TextButton = ({
  title,
  onClick,
  isDisabled = false,
  style,
  testID,
}: TextButtonProps) => {
  return (
    <TouchableOpacity
      disabled={isDisabled}
      style={style}
      onPress={onClick}
      testID={testID}>
      <Text style={styles.redTextButton}>{title}</Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  redTextButton: {
    ...(buttonText as TextStyle),
    color: colors.red400,
  } as TextStyle,
});

export default TextButton;
