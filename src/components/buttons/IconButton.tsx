import React from 'react';
import { TouchableOpacity } from 'react-native';
import { cameraIconContainer } from '~/styles/buttons';

interface IconButtonProps {
  onPress: () => void; // Callback for when the button is pressed
  icon: React.ReactNode; // The icon to display in the button
  style?: object; // Optional custom styles
  disableInitialStyling?: boolean;
}

const IconButton: React.FC<IconButtonProps> = ({
  onPress,
  icon,
  style,
  disableInitialStyling,
}) => {
  const buttonStyle = disableInitialStyling
    ? style
    : [cameraIconContainer, style];

  return (
    <TouchableOpacity style={buttonStyle} onPress={onPress} hitSlop={8}>
      {icon}
    </TouchableOpacity>
  );
};

export default IconButton;
