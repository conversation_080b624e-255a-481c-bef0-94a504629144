import React from 'react';

import { Button } from '@rneui/themed';
import { stretchView } from '~/styles/views';
import { buttonText } from '~/styles/text';
import { outlinedButton } from '~/styles/buttons';
import colors from '~/styles/colors';
import { TextStyle, ViewStyle } from 'react-native';

type OutlinedButtonProps = {
  id?: string;
  title: string;
  onClick: () => void;
  style?: object | null;
  color: string;
  isDisabled?: boolean;
  isLoading?: boolean;
};

const OutlinedButton = ({
  id,
  title,
  onClick,
  style = null,
  color = '',
  isDisabled = false,
  isLoading = false,
}: OutlinedButtonProps) => {
  const getButtonColor = () => {
    if (color === 'primary') {
      return colors.red600;
    }

    return colors.grey600;
  };

  const outline = {
    borderColor: getButtonColor(),
    borderRadius: 12,
  };

  const text = {
    color: getButtonColor(),
  };

  return (
    <Button
      id={id}
      testID="button"
      title={title}
      type="outline"
      onPress={() => onClick()}
      loading={isLoading}
      titleStyle={{ ...(buttonText as TextStyle), ...text }}
      buttonStyle={{ ...(outlinedButton as ViewStyle), ...outline }}
      containerStyle={[style, stretchView]}
      color={getButtonColor()}
      disabled={isDisabled}
    />
  );
};

export default OutlinedButton;
