import React, { useCallback, useEffect, useState } from 'react';
import { Stop, TaskListItem } from '~/types/stops.types';
import { buttonFullWidth, primarySolidButton } from '~/styles/buttons';
import FilledButton from '~/components/buttons/FilledButton';
import { useNavigation } from '@react-navigation/native';
import colors from '~/styles/colors';
import StopWatch from '~/components/icons/StopWatch';
import en from '~/localization/en';
import { interpolateString } from '~/utils/strings';
import { formatTimeRemaining } from '~/utils/dateAndTime';
import { useTimer } from '~/hooks/useTimer';
import { ProtocolType } from '~/types/protocol.types';
import { ProtocolService } from '~/services/protocol';
import { buildScenario } from '~/services/protocol/ProtocolScenarioDataService';
import { ProtocolContextData } from '~/types/protocolActions.types';
import { SimpleCondition } from '~/types/condition.types';
import { RouteSummary } from '~/types/routes.types';
import { getProtocolForGeofenceEntry } from '~/services/geofence/GeofenceService';
import { DriverActions } from '~/services/sync/driverActions';
import { submitCheckInLocation } from '~/services/location/LocationService';
import { useStopServices } from '~/hooks/useStopServices';
import { buildStopTasks } from '~/utils/stops';

export const PROTOCOL_MIN_STOP_DURATION = 300;

interface StopActionButtonProps {
  nextStop: Stop;
  currentRoute: RouteSummary | null;
  stopList: Stop[];
  lastCompletedStop?: Stop;
  isPickup: boolean;
  isDelivery: boolean;
  isService: boolean;
}

const StopActionButton = ({
  nextStop,
  currentRoute,
  stopList,
  lastCompletedStop,
  isPickup,
  isDelivery,
  isService,
}: StopActionButtonProps) => {
  const navigation = useNavigation();

  const [scenario, setScenario] = useState<ProtocolContextData | undefined>();
  const [thresholdInSeconds, setThresholdInSeconds] = useState<number>(300);
  const { pickupParcels, deliveryParcels, stopServices } =
    useStopServices(nextStop);
  const [remainingSeconds, setRemainingSeconds] = useState<
    number | undefined
  >();

  const [isProtocolActive, setIsProtocolActive] = useState<
    boolean | undefined
  >();

  const nextStopName = nextStop.Name || en.nextStop;

  const calculateRemainingSeconds = useCallback(() => {
    if (!lastCompletedStop?.Completed_Time__c) {
      setRemainingSeconds(undefined);
      return;
    }

    const elapsedSeconds = getSecondsSinceLastStop(
      lastCompletedStop.Completed_Time__c,
    );
    const remaining = thresholdInSeconds - elapsedSeconds;
    setRemainingSeconds(Math.max(remaining, 0));
  }, [lastCompletedStop?.Completed_Time__c, thresholdInSeconds]);

  useEffect(() => {
    const fetchScenario = async () => {
      try {
        const _scenario = await buildScenario({
          stopId: lastCompletedStop?.Id,
          routeSummaryId: nextStop.Summary__c,
        });

        if (!_scenario) {
          return;
        }

        setScenario(_scenario);
      } catch (error) {
        console.error('Error building scenario:', error);
      }
    };

    if (!lastCompletedStop?.Id || !nextStop?.Summary__c) {
      setIsProtocolActive(false);
      return;
    }

    fetchScenario();
  }, [lastCompletedStop?.Id, nextStop.Summary__c]);

  const checkForProtocol = useCallback(() => {
    if (!scenario) return;

    const protocols = ProtocolService.getApplicableProtocols({
      type: ProtocolType.PRE_ARRIVE_STOP,
      scenario,
    });

    if (protocols.length > 0) {
      // NOTE: Need to find a better way to extract required duration value
      const durationCondition = protocols[0].executionCriteria?.all?.find(
        (cond: SimpleCondition) => cond.customOperator === 'isWithinTimeSince',
      );

      const durationBetweenStops =
        durationCondition?.value ?? PROTOCOL_MIN_STOP_DURATION;

      setThresholdInSeconds(durationBetweenStops);
      setIsProtocolActive(true);
      calculateRemainingSeconds();
    } else {
      setIsProtocolActive(false);
    }
  }, [scenario, calculateRemainingSeconds]);

  useEffect(() => {
    checkForProtocol();
  }, [scenario, checkForProtocol]);

  useTimer({
    isRunning: isProtocolActive === true,
    onTick: checkForProtocol,
  });

  const navigateToStopOverview = (taskList: TaskListItem[]) => {
    navigation.navigate('StopOverviewScreen', {
      title: nextStop.Name,
      task: taskList[0],
      stop: nextStop,
      stopList,
      numberOfServices: taskList.length,
      taskList,
    });
  };

  const navigateToSelectService = (taskList: TaskListItem[]) => {
    navigation.navigate('SelectServiceScreen', {
      title: nextStop.Name,
      stop: nextStop,
      taskList,
      stopList,
      isPickup,
      isDelivery,
      isService,
    });
  };

  const handleStopArrival = async () => {
    await DriverActions.markStopArrival(nextStop.Id);
    await submitCheckInLocation();
  };

  const createTaskList = () => {
    return buildStopTasks({
      isPickup,
      isDelivery,
      isService,
      pickupParcels: Array.from(pickupParcels),
      deliveryParcels: Array.from(deliveryParcels),
      stopServices: Array.from(stopServices),
    });
  };

  const checkPostArrivalProtocol = async () => {
    const result = await getProtocolForGeofenceEntry(
      currentRoute,
      nextStop,
      ProtocolType.POST_ARRIVE_STOP,
    );

    if (result?.protocol && result?.scenario) {
      navigation.navigate('ProtocolEngineStack', {
        protocol: result.protocol,
        scenario: result.scenario,
      });
      return true;
    }
    return false;
  };

  const onArrivedStopClicked = async () => {
    try {
      const hasProtocol = await checkPostArrivalProtocol();
      if (hasProtocol) return;
      await handleStopArrival();

      const taskList = createTaskList();

      if (taskList.length === 1) {
        navigateToStopOverview(taskList);
      } else {
        navigateToSelectService(taskList);
      }
    } catch (error) {
      console.error('[onArrivedStopClicked] Error:', error);
      // Optional: Show a toast/snackbar or fallback UI
    }
  };

  const launchProtocolFlow = () => {
    if (!scenario) {
      return;
    }
    const protocols = ProtocolService.getApplicableProtocols({
      type: ProtocolType.PRE_ARRIVE_STOP,
      scenario,
    });
    navigation.navigate('ProtocolEngineStack', { protocols, scenario });
  };

  const getSecondsSinceLastStop = (completedTime: string): number => {
    const elapsedMillis = Date.now() - new Date(completedTime).getTime();
    return Math.floor(elapsedMillis / 1000);
  };

  if (isProtocolActive === false) {
    return (
      <FilledButton
        id={'StopActionButton.FilledButton.arrived'}
        testID="arrived-stop-button"
        title={interpolateString(en.arrivedAtStop, { stopName: nextStopName })}
        color="primary"
        style={[buttonFullWidth, primarySolidButton]}
        onClick={onArrivedStopClicked}
      />
    );
  }

  return (
    <FilledButton
      id={'StopActionButton.FilledButton.wait'}
      testID="wait-button"
      title={interpolateString(en.waitForMinutes, {
        minutes: formatTimeRemaining(remainingSeconds),
      })}
      color="secondary"
      onClick={launchProtocolFlow}
      style={[buttonFullWidth, primarySolidButton]}
      iconLeft={<StopWatch color={colors.darkGray} />}
    />
  );
};

export default StopActionButton;
