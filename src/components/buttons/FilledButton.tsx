import React from 'react';

import { Button } from '@rneui/themed';
import { buttonText } from '~/styles/text';
import colors from '~/styles/colors';
import { Text, TextStyle } from 'react-native';
import { horizontalSpace16 } from '~/styles/spacing';

type ButtonColor = 'primary' | 'secondary';

type FilledButtonProps = {
  id: string;
  title: string;
  onClick: () => void;
  style: object | null;
  textColor?: string | null;
  containerStyle?: object | null;
  color: ButtonColor;
  isDisabled?: boolean;
  isLoading?: boolean;
  iconLeft?: React.ReactNode;
  iconRight?: React.ReactNode;
  testID?: string;
};

const BackgroundColor = {
  primary: colors.red600,
  secondary: colors.backgroundLight,
} as const;

const TextColor = {
  primary: colors.white,
  secondary: colors.darkGray,
} as const;

const FilledButton = ({
  id,
  title,
  onClick,
  style = null,
  containerStyle = null,
  textColor,
  color,
  isDisabled = false,
  isLoading = false,
  iconLeft,
  iconRight,
}: FilledButtonProps) => {
  return (
    <Button
      id={id}
      testID={id}
      onPress={onClick}
      loading={isLoading}
      buttonStyle={style}
      containerStyle={containerStyle}
      color={BackgroundColor[color]}
      disabled={isDisabled}>
      {iconLeft}

      <Text
        style={[
          { color: TextColor[color] || textColor || colors.white },
          buttonText as TextStyle,
          // eslint-disable-next-line react-native/no-inline-styles
          {
            marginLeft: iconLeft ? 4 : horizontalSpace16.marginHorizontal,
            marginRight: iconRight ? 4 : horizontalSpace16.marginHorizontal,
          },
        ]}
        numberOfLines={1}
        ellipsizeMode="tail">
        {title}
      </Text>

      {iconRight}
    </Button>
  );
};

export default FilledButton;
