import React, { useCallback } from 'react';
import { Alert, Text, Linking, TextStyle } from 'react-native';

const TextURLButton = ({
  id,
  url,
  title,
  style,
}: {
  id: string;
  url: string;
  title: string;
  style: TextStyle;
}) => {
  const handlePress = useCallback(async () => {
    // Checking if the link is supported for links with custom URL scheme
    const supported = await Linking.canOpenURL(url);

    if (supported) {
      // If the URL scheme is "http" the web link should be opened
      // by the mobile browser
      await Linking.openURL(url);
    } else {
      Alert.alert(`Don't know how to open this URL: ${url}`);
    }
  }, [url]);

  return (
    <Text id={id} testID={id} style={style} onPress={handlePress}>
      {title}
    </Text>
  );
};

export default TextURLButton;
