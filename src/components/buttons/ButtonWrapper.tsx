import React from 'react';
import { Button, ButtonProps } from '@rneui/themed';

import { ButtonVariant } from '~/types/component.types';

interface ButtonWrapperProps extends ButtonProps {
  id: string;
  variant?: ButtonVariant;
}

const TYPE_FOR_VARIANT: Record<ButtonVariant, ButtonProps['type']> = {
  filled: 'solid',
  outlined: 'outline',
  link: 'clear',
};

const SIZE_FOR_VARIANT: Record<ButtonVariant, ButtonProps['size']> = {
  filled: 'lg',
  outlined: 'lg',
  link: 'sm',
};

const ButtonWrapper: React.FC<ButtonWrapperProps> = ({
  id,
  variant = 'filled',
  iconPosition = 'left',
  icon,
  ...rest
}) => {
  const size = SIZE_FOR_VARIANT[variant];
  const type = TYPE_FOR_VARIANT[variant];
  const iconProps = icon ? getIconProps(iconPosition) : {};

  return (
    <Button
      testID={id}
      id={id}
      type={type}
      icon={icon}
      size={size}
      {...iconProps}
      {...rest}
    />
  );
};

const getIconProps = (iconPosition: ButtonProps['iconPosition']) => {
  switch (iconPosition) {
    case 'left':
      return {
        titleStyle: {
          marginLeft: 4,
        },
      };

    case 'right':
      return {
        iconRight: true,
        titleStyle: {
          marginRight: 4,
        },
      };

    default:
      return {};
  }
};

export default ButtonWrapper;
