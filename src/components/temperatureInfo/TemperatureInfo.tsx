import * as React from 'react';
import { StyleSheet, View, Text, TextStyle } from 'react-native';
import colors from '~/styles/colors';
import { paddingHorizontal14, paddingVertical16 } from '~/styles/spacing';
import { headerTitle, p } from '~/styles/text';

interface TemperatureInfoProps {
  location: string;
  temperature: number;
  icon: React.ReactNode;
  testID?: string;
}

/**
 * Helper function to format temperature display
 */
const formatTemperature = (temperature: number): string => {
  if (!Number.isFinite(temperature)) {
    return '--°';
  }

  const roundedTemp = Math.round(temperature * 10) / 10;
  return `${roundedTemp}°`;
};

/**
 * Helper function to get temperature color based on value
 */
const getTemperatureColor = (temperature: number): string => {
  if (temperature <= 0) return '#4A90E2'; // Blue for freezing
  if (temperature <= 15) return '#7ED321'; // Green for cool
  if (temperature <= 25) return '#F5A623'; // Orange for warm
  return '#dd2424'; // Red for hot
};

const TemperatureInfo: React.FC<TemperatureInfoProps> = ({
  location,
  temperature = 103,
  icon,
  testID = 'temperature-info',
}) => {
  if (!location || location.trim() === '') {
    console.warn(
      'TemperatureInfo: location prop is required and cannot be empty',
    );
    return null;
  }

  const temperatureColor = getTemperatureColor(temperature);
  const formattedTemperature = formatTemperature(temperature);

  const accessibilityLabel = `Expected temperature in ${location}: ${formattedTemperature}`;

  return (
    <View
      style={styles.temperatureInfo}
      accessible={true}
      accessibilityLabel={accessibilityLabel}
      accessibilityRole="text"
      testID={testID}>
      <View>{icon}</View>

      <View style={styles.temperatureDetails}>
        <Text numberOfLines={1} style={styles.temperatureTitle}>
          Expected temperature
        </Text>
        <Text
          style={styles.locationText}
          numberOfLines={1}
          ellipsizeMode="tail">
          {location}
        </Text>
      </View>

      <Text
        style={[styles.temperatureValue, { color: temperatureColor }]}
        testID={`${testID}-value`}>
        {formattedTemperature}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  temperatureTitle: {
    ...headerTitle,
  } as TextStyle,

  locationText: {
    ...p,
    color: colors.darkGray,
    marginTop: 4,
  } as TextStyle,

  temperatureDetails: {
    justifyContent: 'center',
    alignItems: 'flex-start',
    flex: 1,
    paddingHorizontal: paddingHorizontal14.paddingHorizontal,
  },

  temperatureValue: {
    ...headerTitle,
  } as TextStyle,

  temperatureInfo: {
    borderRadius: 12,
    backgroundColor: colors.red25,
    width: '100%',
    paddingHorizontal: paddingHorizontal14.paddingHorizontal,
    paddingVertical: paddingVertical16.paddingVertical,
    flexDirection: 'row',
    alignItems: 'center',
  },
});

export default TemperatureInfo;
