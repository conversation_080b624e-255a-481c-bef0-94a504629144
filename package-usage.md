## `@bam.tech/react-native-image-resizer`

### Links

- NPM link: [@bam.tech/react-native-image-resizer](https://www.npmjs.com/package/@bam.tech/react-native-image-resizer)
- GitHub link: [react-native-image-resizer](https://github.com/bamlab/react-native-image-resizer)

### Information

- What is this package solving?
  - Provides the capability to resize images directly in the React Native app without needing external tools or libraries, essential for managing memory and network usage when handling large images.
- Why was this package selected?
  - Efficient for processing and resizing images locally, reducing upload sizes for image-based operations.
- Known issues, if any?
  - Limited support for some advanced image formats and may have performance issues on older devices.

---

## `@lodev09/react-native-exify`

### Links

- NPM link: [@lodev09/react-native-exify](https://www.npmjs.com/package/@lodev09/react-native-exify)
- GitHub link: [react-native-exify](https://github.com/lodev09/react-native-exify)

### Information

- What is this package solving?
  - Provides the ability to extract EXIF data from images, which is useful for handling image metadata such as orientation and geolocation.
- Why was this package selected?
  - Needed for working with image metadata in a React Native app.
- Known issues, if any?
  - No major issues reported, but may have compatibility issues with certain image formats.

---

## `@react-native-async-storage/async-storage`

### Links

- NPM link: [@react-native-async-storage/async-storage](https://www.npmjs.com/package/@react-native-async-storage/async-storage)
- GitHub link: [react-native-async-storage](https://github.com/react-native-async-storage/async-storage)

### Information

- What is this package solving?
  - Provides an asynchronous, persistent, key-value storage system for React Native, useful for saving simple data locally.
- Why was this package selected?
  - Efficient and widely used for persisting small data (e.g., authentication tokens, preferences).
- Known issues, if any?
  - Can have issues with large datasets; not optimized for complex data storage needs.

---

## `@react-native-async-storage/root`

### Links

- NPM link: [@react-native-async-storage/root](https://www.npmjs.com/package/@react-native-async-storage/root)
- GitHub link: [react-native-async-storage/root](https://github.com/react-native-async-storage/async-storage)

### Information

- What is this package solving?
  - A workaround to handle AsyncStorage in React Native when root package causes issues.
- Why was this package selected?
  - Useful for resolving compatibility issues with AsyncStorage when used with different versions of React Native.
- Known issues, if any?
  - Dependent on specific versions and may cause conflicts if mismatched.

---

## `@react-native-community/blur`

### Links

- NPM link: [@react-native-community/blur](https://www.npmjs.com/package/@react-native-community/blur)
- GitHub link: [react-native-community/blur](https://github.com/react-native-community/blur)

### Information

- What is this package solving?
  - Adds the ability to apply blur effects to images and views within React Native.
- Why was this package selected?
  - Needed for visual effects and UI enhancements such as blur backgrounds.
- Known issues, if any?
  - Performance issues on older devices and limited support for certain platforms.

---

## `@react-native-community/netinfo`

### Links

- NPM link: [@react-native-community/netinfo](https://www.npmjs.com/package/@react-native-community/netinfo)
- GitHub link: [react-native-community/netinfo](https://github.com/react-native-community/netinfo)

### Information

- What is this package solving?
  - Provides an API for detecting network connectivity status and changes.
- Why was this package selected?
  - Critical for implementing offline functionality and network state management in apps.
- Known issues, if any?
  - Sometimes fails to detect certain network states accurately, especially in specific environments.

---

## `@react-navigation/bottom-tabs`

### Links

- NPM link: [@react-navigation/bottom-tabs](https://www.npmjs.com/package/@react-navigation/bottom-tabs)
- GitHub link: [react-navigation/bottom-tabs](https://github.com/react-navigation/bottom-tabs)

### Information

- What is this package solving?
  - Implements bottom tab navigation for React Native apps.
- Why was this package selected?
  - A widely-used and well-supported solution for managing bottom tab navigation.
- Known issues, if any?
  - Limited customization options in terms of animations and styling.

---

## `@react-navigation/elements`

### Links

- NPM link: [@react-navigation/elements](https://www.npmjs.com/package/@react-navigation/elements)
- GitHub link: [react-navigation/elements](https://github.com/react-navigation/elements)

### Information

- What is this package solving?
  - Provides basic UI components for navigation in React Native.
- Why was this package selected?
  - Essential for implementing navigation elements such as headers, buttons, and other UI components.
- Known issues, if any?
  - May not support advanced use cases out of the box.

---

## `@react-navigation/material-top-tabs`

### Links

- NPM link: [@react-navigation/material-top-tabs](https://www.npmjs.com/package/@react-navigation/material-top-tabs)
- GitHub link: [react-navigation/material-top-tabs](https://github.com/react-navigation/material-top-tabs)

### Information

- What is this package solving?
  - Implements Material Design-style top tab navigation for React Native.
- Why was this package selected?
  - Needed to provide a tab navigation style consistent with Material Design.
- Known issues, if any?
  - Some performance issues and difficulty in customizing the tab bar styles.

---

## `@react-navigation/native`

### Links

- NPM link: [@react-navigation/native](https://www.npmjs.com/package/@react-navigation/native)
- GitHub link: [react-navigation/native](https://github.com/react-navigation/native)

### Information

- What is this package solving?
  - Core package for setting up React Navigation in React Native apps.
- Why was this package selected?
  - Essential for managing navigation between screens in React Native apps.
- Known issues, if any?
  - May have compatibility issues with certain versions of React Native.

---

## `@react-navigation/native-stack`

### Links

- NPM link: [@react-navigation/native-stack](https://www.npmjs.com/package/@react-navigation/native-stack)
- GitHub link: [react-navigation/native-stack](https://github.com/react-navigation/native-stack)

### Information

- What is this package solving?
  - Provides native-stack navigation for React Native, offering better performance for complex navigation stacks.
- Why was this package selected?
  - Required for optimized stack navigation performance.
- Known issues, if any?
  - May have issues with custom screen transitions.

---

## `@rneui/base`

### Links

- NPM link: [@rneui/base](https://www.npmjs.com/package/@rneui/base)
- GitHub link: [react-native-elements/base](https://github.com/react-native-elements/react-native-elements)

### Information

- What is this package solving?
  - Provides base components for React Native UI elements like buttons, text inputs, and others.
- Why was this package selected?
  - Useful for building common UI elements across the app.
- Known issues, if any?
  - No significant issues reported, but may require additional styling adjustments.

---

## `@rneui/themed`

### Links

- NPM link: [@rneui/themed](https://www.npmjs.com/package/@rneui/themed)
- GitHub link: [react-native-elements/themed](https://github.com/react-native-elements/react-native-elements)

### Information

- What is this package solving?
  - Provides a theming API for React Native components, making it easier to manage app-wide themes.
- Why was this package selected?
  - Required for consistent theming across all UI elements in the app.
- Known issues, if any?
  - No significant issues reported.

---

## `axios`

### Links

- NPM link: [axios](https://www.npmjs.com/package/axios)
- GitHub link: [axios](https://github.com/axios/axios)

### Information

- What is this package solving?
  - A promise-based HTTP client for making requests to external APIs.
- Why was this package selected?
  - Popular and reliable HTTP client for managing API calls.
- Known issues, if any?
  - Issues with handling large responses; may require custom handling for certain cases.

---

## `babel-plugin-module-resolver`

### Links

- NPM link: [babel-plugin-module-resolver](https://www.npmjs.com/package/babel-plugin-module-resolver)
- GitHub link: [babel-plugin-module-resolver](https://github.com/tleunen/babel-plugin-module-resolver)

### Information

- What is this package solving?
  - Helps with resolving module paths in React Native, enabling absolute imports.
- Why was this package selected?
  - Makes it easier to manage imports and navigate project structure.
- Known issues, if any?
  - Can cause issues when upgrading Babel versions; requires configuration updates.

---

## `lint-staged`

### Links

- NPM link: [lint-staged](https://www.npmjs.com/package/lint-staged)
- GitHub link: [lint-staged](https://github.com/okonet/lint-staged)

### Information

- What is this package solving?
  - Runs linters on staged files to ensure code quality before committing.
- Why was this package selected?
  - Essential for maintaining code standards in a collaborative environment.
- Known issues, if any?
  - Can cause performance issues with large staged files.

---

## `react`

### Links

- NPM link: [react](https://www.npmjs.com/package/react)
- GitHub link: [react](https://github.com/facebook/react)

### Information

- What is this package solving?
  - The core React library for building user interfaces in React Native.
- Why was this package selected?
  - Fundamental for React Native development.
- Known issues, if any?
  - Frequent updates; might require adjustments after major releases.

---

## `react-native`

### Links

- NPM link: [react-native](https://www.npmjs.com/package/react-native)
- GitHub link: [react-native](https://github.com/facebook/react-native)

### Information

- What is this package solving?
  - The core framework for building cross-platform mobile apps with React.
- Why was this package selected?
  - Fundamental for building mobile apps in React Native.
- Known issues, if any?
  - Frequent breaking changes and compatibility issues with libraries.

---

## `react-native-config`

### Links

- NPM link: [react-native-config](https://www.npmjs.com/package/react-native-config)
- GitHub link: [react-native-config](https://github.com/luggit/react-native-config)

### Information

- What is this package solving?
  - Allows the use of configuration values in React Native apps.
- Why was this package selected?
  - Useful for managing environment-specific variables.
- Known issues, if any?
  - Requires proper linking and configuration on iOS and Android.

---

## `react-native-device-info`

### Links

- NPM link: [react-native-device-info](https://www.npmjs.com/package/react-native-device-info)
- GitHub link: [react-native-device-info](https://github.com/react-native-device-info/react-native-device-info)

### Information

- What is this package solving?
  - Provides device-specific information such as the model, OS version, and other system details.
- Why was this package selected?
  - Essential for gathering device information for analytics and debugging purposes.
- Known issues, if any?
  - Sometimes fails to retrieve certain details on older devices or specific OS versions.

---

## `react-native-fs`

### Links

- NPM link: [react-native-fs](https://www.npmjs.com/package/react-native-fs)
- GitHub link: [react-native-fs](https://github.com/itinance/react-native-fs)

### Information

- What is this package solving?
  - Provides a file system API to read and write files on a device's local storage.
- Why was this package selected?
  - Necessary for file management operations such as saving user data, logs, or images.
- Known issues, if any?
  - Can have permission-related issues, especially on Android 10 and later.

---

## `react-native-keychain`

### Links

- NPM link: [react-native-keychain](https://www.npmjs.com/package/react-native-keychain)
- GitHub link: [react-native-keychain](https://github.com/oblador/react-native-keychain)

### Information

- What is this package solving?
  - Provides a secure way to store sensitive data like passwords or tokens using the device's secure storage.
- Why was this package selected?
  - Necessary for handling secure storage, especially for user authentication tokens.
- Known issues, if any?
  - Sometimes has issues with different Android versions; requires careful setup.

---

## `react-native-modal`

### Links

- NPM link: [react-native-modal](https://www.npmjs.com/package/react-native-modal)
- GitHub link: [react-native-modal](https://github.com/react-native-modal/react-native-modal)

### Information

- What is this package solving?
  - A customizable modal component for React Native, offering a simple API to display modals.
- Why was this package selected?
  - Needed for displaying dialogs and modals with customizable animations and styles.
- Known issues, if any?
  - May cause issues with overlaying animations when used in deep navigation stacks.

---

## `react-native-pager-view`

### Links

- NPM link: [react-native-pager-view](https://www.npmjs.com/package/react-native-pager-view)
- GitHub link: [react-native-pager-view](https://github.com/callstack/react-native-pager-view)

### Information

- What is this package solving?
  - Provides a component for pager-style navigation, allowing swiping through pages or views.
- Why was this package selected?
  - Useful for implementing swipeable views or tab-style navigation.
- Known issues, if any?
  - Limited support for some screen orientation changes and animations.

---

## `react-native-permissions`

### Links

- NPM link: [react-native-permissions](https://www.npmjs.com/package/react-native-permissions)
- GitHub link: [react-native-permissions](https://github.com/zoontek/react-native-permissions)

### Information

- What is this package solving?
  - Manages and requests runtime permissions for the app (camera, location, etc.).
- Why was this package selected?
  - Necessary for handling runtime permissions on both iOS and Android.
- Known issues, if any?
  - Sometimes fails to request permissions on certain Android devices due to OS limitations.

---

## `react-native-safe-area-context`

### Links

- NPM link: [react-native-safe-area-context](https://www.npmjs.com/package/react-native-safe-area-context)
- GitHub link: [react-native-safe-area-context](https://github.com/th3rdwave/react-native-safe-area-context)

### Information

- What is this package solving?
  - Helps manage safe areas and insets, particularly important for devices with notches or rounded corners.
- Why was this package selected?
  - Ensures UI elements respect the safe areas on devices with unique screen designs.
- Known issues, if any?
  - Sometimes doesn't work correctly on older Android versions or when custom navigation is used.

---

## `react-native-screens`

### Links

- NPM link: [react-native-screens](https://www.npmjs.com/package/react-native-screens)
- GitHub link: [react-native-screens](https://github.com/software-mansion/react-native-screens)

### Information

- What is this package solving?
  - Optimizes navigation performance by using native views for each screen.
- Why was this package selected?
  - Enhances performance for navigation stacks in React Native.
- Known issues, if any?
  - Some screen transitions may behave unexpectedly if not configured properly.

---

## `react-native-svg`

### Links

- NPM link: [react-native-svg](https://www.npmjs.com/package/react-native-svg)
- GitHub link: [react-native-svg](https://github.com/react-native-svg/react-native-svg)

### Information

- What is this package solving?
  - Allows rendering SVG images and graphics in React Native apps.
- Why was this package selected?
  - Essential for working with scalable vector graphics in the app's UI.
- Known issues, if any?
  - Performance issues on complex SVGs; may require optimizations for heavy use.

---

## `react-native-tab-view`

### Links

- NPM link: [react-native-tab-view](https://www.npmjs.com/package/react-native-tab-view)
- GitHub link: [react-native-tab-view](https://github.com/satya164/react-native-tab-view)

### Information

- What is this package solving?
  - Provides a simple way to implement tab-based navigation with swipe gestures.
- Why was this package selected?
  - Needed for building swipeable tab views with smooth animations.
- Known issues, if any?
  - Can cause issues with nested navigation and needs configuration for optimal performance.

---

## `react-native-ux-cam`

### Links

- NPM link: [react-native-ux-cam](https://www.npmjs.com/package/react-native-ux-cam)
- GitHub link: [react-native-ux-cam](https://github.com/uxcam/react-native-ux-cam)

### Information

- What is this package solving?
  - Allows the integration of UXCam to track user behavior and interactions in the app.
- Why was this package selected?
  - Provides analytics and helps in improving the user experience by recording interactions.
- Known issues, if any?
  - Sometimes has issues with performance and screen recording on certain devices.

---

## `react-native-vector-icons`

### Links

- NPM link: [react-native-vector-icons](https://www.npmjs.com/package/react-native-vector-icons)
- GitHub link: [react-native-vector-icons](https://github.com/oblador/react-native-vector-icons)

### Information

- What is this package solving?
  - Provides a collection of customizable vector icons for React Native apps.
- Why was this package selected?
  - A reliable and efficient icon library, useful for UI components and buttons.
- Known issues, if any?
  - Limited support for custom icons; requires manual updates for newer icon packs.

---

## `react-native-vision-camera`

### Links

- NPM link: [react-native-vision-camera](https://www.npmjs.com/package/react-native-vision-camera)
- GitHub link: [react-native-vision-camera](https://github.com/mrousavy/react-native-vision-camera)

### Information

- What is this package solving?
  - Provides a modern camera library for React Native, supporting camera and video capture.
- Why was this package selected?
  - Essential for implementing advanced camera functionality with performance optimization.
- Known issues, if any?
  - Requires correct permissions handling, particularly for Android 10+ devices.

---

## `react-redux`

### Links

- NPM link: [react-redux](https://www.npmjs.com/package/react-redux)
- GitHub link: [react-redux](https://github.com/reduxjs/react-redux)

### Information

- What is this package solving?
  - Provides React bindings for Redux, allowing for efficient state management in React apps.
- Why was this package selected?
  - Essential for managing complex application states.
- Known issues, if any?
  - Can cause performance issues with large stores if not properly optimized.

---

## `realm`

### Links

- NPM link: [realm](https://www.npmjs.com/package/realm)
- GitHub link: [realm](https://github.com/realm/realm-js)

### Information

- What is this package solving?
  - Provides an easy-to-use local database solution for React Native apps.
- Why was this package selected?
  - Useful for handling complex local storage and offline data scenarios.
- Known issues, if any?
  - Can experience sync issues when used with multiple devices or users.

---

## `react-native-signature-canvas`

### Links

- [NPM link](https://www.npmjs.com/package/react-native-signature-canvas)
- [GitHub link](https://github.com/RepairShopr/react-native-signature-canvas)

### Information

- **What is this package solving?**

  - Allows users to draw and capture signatures in React Native apps, particularly useful for workflows requiring acknowledgments.

- **Why was this package selected?**

  - Lightweight and customizable solution for signature capture with HTML-based rendering.

- **Known issues, if any?**
  - Requires proper setup to avoid dependency-related issues like `RNCWebViewModule`.

---

## `react-native-webview`

### Links

- [NPM link](https://www.npmjs.com/package/react-native-webview)
- [GitHub link](https://github.com/react-native-webview/react-native-webview)

### Information

- **What is this package solving?**

  - Embeds web content directly within a React Native app using a native WebView component.

- **Why was this package selected?**

  - Standard solution for displaying external or internal web content seamlessly.

- **Known issues, if any?**
  - Performance and rendering may vary on low-end devices.

---

## `@realm/react`

### Links

- NPM link: [@realm/react](https://www.npmjs.com/package/@realm/react)
- GitHub link: [realm/realm-js](https://github.com/realm/realm-js/tree/master/packages/realm-react)

### Information

- What is this package solving?
  - Provides React-specific bindings and hooks for Realm Database, making it easier to integrate Realm with React Native applications.
- Why was this package selected?
  - Offers React-specific features like hooks and components for better integration with Realm Database, improving developer experience.
- Known issues, if any?
  - Must be used with compatible versions of Realm JS to avoid synchronization issues.

---

## `luxon`

### Links

- NPM link: [luxon](https://www.npmjs.com/package/luxon)
- GitHub link: [luxon](https://github.com/moment/luxon)

### Information

- What is this package solving?
  - A modern library for working with dates and times in JavaScript, providing comprehensive functionality for parsing, validating, manipulating, and formatting dates.
- Why was this package selected?
  - Offers robust timezone handling, immutable API design, and comprehensive internationalization support. More feature-rich than date-fns for complex date/time operations.
- Known issues, if any?
  - Slightly larger bundle size compared to date-fns, but offers more comprehensive features.

---

## `@react-native-clipboard/clipboard`

### Links

- NPM link: [@react-native-clipboard/clipboard](https://www.npmjs.com/package/@react-native-clipboard/clipboard)
- GitHub link: [@react-native-clipboard/clipboard](https://github.com/react-native-clipboard/clipboard)

### Information

- What is this package solving?

  - Provides a native module for reading and writing text to the system clipboard in React Native apps.

- Why was this package selected?

  - Officially maintained, lightweight, and works consistently across Android and iOS. Needed to support clipboard copy functionality in UI components.

- Known issues, if any?
  - Limited to basic clipboard operations (text only); no built-in support for images or advanced clipboard formats.

---

## `zustand`

### Links

- NPM link: [zustand](https://www.npmjs.com/package/zustand)
- GitHub link: [zustand](https://github.com/pmndrs/zustand)

- What is this package solving?
  - A small, fast, and scalable state-management library for React and React Native.
- Why was this package selected?
  - Chosen for its minimal API surface, simplicity compared to Redux or Context, and ease of integrating local/global state in React Native apps.
- Known issues, if any?
  - Care is needed when using with asynchronous updates or complex nested state unless structured well.

## `@rnmapbox/maps`

---

### Links

- NPM link: [@rnmapbox/maps](https://www.npmjs.com/package/@rnmapbox/maps)
- GitHub link: [@rnmapbox/maps](https://github.com/rnmapbox/maps)

### Information

- **What is this package solving?**  
  Provides powerful, performant Mapbox maps for React Native with extensive control over rendering, markers, camera, and offline maps. Ideal for geolocation and mapping features in mobile apps.

- **Why was this package selected?**  
  Actively maintained, supports both Android and iOS, and offers native bindings for Mapbox’s advanced mapping capabilities. Chosen for its flexibility, customization, and native performance.

- **Known issues, if any?**

  - iOS animations may run unexpectedly unless manually disabled.
  - Pod installation and setup can be complex due to native linking and environment variable requirements.
  - Compatibility issues with RN < 0.76 due to upstream Codegen changes.

- **Currently used version:**  
  `"@rnmapbox/maps": "10.1.36"`
  > ⚠️ **Why not 10.1.37 or 10.1.38?**  
  > React Native versions prior to 0.76 (and Expo < 52) are incompatible with recent changes introduced in Mapbox v10.1.37+ (see [#3812](https://github.com/rnmapbox/maps/issues/3812)).  
  > These changes caused `UnsupportedModulePropertyParserError` during Codegen because of `TSTypeReference` use in `onLocationUpdate`.  
  > Downgrading to `10.1.36` avoids the issue. Ensure no `@rnmapbox+maps+10.1.37.patch` file is present and use the exact version (no `^`).

---
