{"project_info": {"project_number": "330031371559", "project_id": "rapid-medical-app", "storage_bucket": "rapid-medical-app.firebasestorage.app"}, "client": [{"client_info": {"mobilesdk_app_id": "1:330031371559:android:1b5ddac2d708932e530502", "android_client_info": {"package_name": "com.rapidmedical.app"}}, "oauth_client": [], "api_key": [{"current_key": "AIzaSyD7NdGTIcNlg1yGljOBcpLzsLxSgGs5Lxw"}], "services": {"appinvite_service": {"other_platform_oauth_client": []}}}, {"client_info": {"mobilesdk_app_id": "1:330031371559:android:9225f890e13926e6530502", "android_client_info": {"package_name": "com.rapidmedical.app.dev"}}, "oauth_client": [], "api_key": [{"current_key": "AIzaSyD7NdGTIcNlg1yGljOBcpLzsLxSgGs5Lxw"}], "services": {"appinvite_service": {"other_platform_oauth_client": []}}}, {"client_info": {"mobilesdk_app_id": "1:330031371559:android:1bc06ebf00c58786530502", "android_client_info": {"package_name": "com.rapidmedical.app.staging"}}, "oauth_client": [], "api_key": [{"current_key": "AIzaSyD7NdGTIcNlg1yGljOBcpLzsLxSgGs5Lxw"}], "services": {"appinvite_service": {"other_platform_oauth_client": []}}}], "configuration_version": "1"}