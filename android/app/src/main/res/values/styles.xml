<resources>

    <!-- Base application theme. -->
    <style name="AppTheme" parent="Theme.AppCompat.DayNight.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="android:editTextBackground">@drawable/rn_edit_text_material</item>
        <item name="android:navigationBarColor">@color/navigation_bar_color</item>
    </style>

    <!-- Navigation UI Text Appearances -->
    <style name="PrimaryManeuverTextAppearance" parent="TextAppearance.AppCompat">
        <item name="android:textColor">#FFFFFF</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textSize">30sp</item>
    </style>

    <style name="ManeuverTextAppearance" parent="TextAppearance.AppCompat">
        <item name="android:textColor">#FFFFFF</item>
        <item name="android:textSize">25sp</item>
    </style>

    <style name="StepDistanceRemainingAppearance" parent="TextAppearance.AppCompat">
        <item name="android:textColor">#FFFFFF</item>
        <item name="android:textSize">21sp</item>
    </style>

    <!-- Custom Mapbox Trip Progress Style -->
    <style name="MapboxCustomTripProgressStyle" parent="MapboxStyleTripProgressView">
        <item name="tripProgressTextColor">#7B3CEA</item> <!-- Text color -->
        <item name="tripProgressViewBackgroundColor">#FFFFFF</item> <!-- White background -->
        <item name="android:layout_margin">0dp</item> <!-- Ensure zero margin if needed -->
    </style>

</resources>
