<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.mapbox.maps.MapView
        android:id="@+id/mapView"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.cardview.widget.CardView
        android:id="@+id/tripProgressCard"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:visibility="invisible"
        app:cardElevation="8dp"
        app:cardUseCompatPadding="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <com.mapbox.navigation.ui.components.tripprogress.view.MapboxTripProgressView
            android:id="@+id/tripProgressView"
            android:layout_width="match_parent"
            android:paddingVertical="8dp"
            android:paddingHorizontal="10dp"
            android:layout_height="wrap_content" />

       <ImageView
            android:id="@+id/stop"
            android:layout_width="41dp"
            android:layout_height="41dp"
            android:visibility="gone"
            android:layout_gravity="end|center_vertical"
            android:layout_marginEnd="12dp"
            app:srcCompat="@android:drawable/ic_delete"
            android:contentDescription="@string/delete_icon"/>

    </androidx.cardview.widget.CardView>

    <com.mapbox.navigation.ui.components.maneuver.view.MapboxManeuverView
        android:id="@+id/maneuverView"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_margin="4dp"
        android:visibility="invisible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.mapbox.navigation.ui.components.voice.view.MapboxSoundButton
        android:id="@+id/soundButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="16dp"
        android:visibility="invisible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/maneuverView" />

    <com.mapbox.navigation.ui.components.maps.camera.view.MapboxRouteOverviewButton
        android:id="@+id/routeOverview"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="16dp"
        android:visibility="invisible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/soundButton" />

    <com.mapbox.navigation.ui.components.maps.camera.view.MapboxRecenterButton
        android:id="@+id/recenter"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="invisible"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/routeOverview" />

</androidx.constraintlayout.widget.ConstraintLayout>
