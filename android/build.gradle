buildscript {
    ext     {
        RNMapboxMapsVersion = '11.4.1'
        buildToolsVersion = "34.0.0"
        minSdkVersion = 24
        compileSdkVersion = 34
        targetSdkVersion = 35
        ndkVersion = "26.1.10909125"
        kotlinVersion = "1.9.25"
        appCompatVersion 	= "1.4.2"
        googlePlayServicesLocationVersion = "21.0.1"
        playServicesLocationVersion = "21.0.1"
    }
    repositories {
        google()
        mavenCentral()
        
    }
    dependencies {
        classpath("com.android.tools.build:gradle")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin")
        classpath("com.google.gms:google-services:4.4.2")
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
        // Required for react-native-background-fetch
        maven { url("${project(':react-native-background-fetch').projectDir}/libs") }
        // Required for react-native-background-geolocation
        maven { url("${project(':react-native-background-geolocation').projectDir}/libs") }
        maven { url 'https://developer.huawei.com/repo/' }
        maven {
              url 'https://api.mapbox.com/downloads/v2/releases/maven'
              authentication {
                  basic(BasicAuthentication)
              }
              credentials {
                // Do not change the username below.
                // This should always be `mapbox` (not your username).
                  username = "mapbox"
                  password = "**********************************************************************************************"
              }
        }
    }
}

apply plugin: "com.facebook.react.rootproject"