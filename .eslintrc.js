module.exports = {
  root: true,
  // add plugin:prettier/recommended as the last extension
  extends: ['@react-native', 'plugin:prettier/recommended'],
  rules: {
    'react/react-in-jsx-scope': 0,
    'no-unused-vars': 1,
    'react/no-unstable-nested-components': 0,
    'import/no-duplicates': 'error',
    'no-restricted-imports': [
      'error',
      {
        patterns: ['../'],
      },
    ],
  },
  plugins: ['jest', 'prettier', 'import'],
  ignorePatterns: ['babel.config', 'jest.setup'],
  env: {
    'jest/globals': true,
  },
};
