#! /bin/bash

echo "Step 1/3"
# =================
echo "General cleanup..."
# Remove react native and metro cache
rm -rf $TMPDIR/react-*
rm -rf $TMPDIR/metro-*
watchman watch-del-all

# Remove library cache
# rm -rf rm -rf ~/Library/Caches/CocoaPods && 
rm -rf ~/Library/Developer/Xcode/DerivedData/* && 

# Remove node cache
rm -rf node_modules
yarn cache clean
echo

echo "Step 2/3"
# =================
echo "Cleaning Android..."

rm -rf ~/.gradle && 
rm -rf ./android/.gradle && 
rm -rf ./android/build
# cd android && ./gradlew clean
echo


echo "Step 3/3"
# =================
echo "Cleaning iOS..."

rm -rf vendor &&

cd ios/ &&
rm -rf Pods && 
rm Podfile.lock &&
pod deintegrate && 
pod cache clean --all &&
rm -rf build &&
cd ..
